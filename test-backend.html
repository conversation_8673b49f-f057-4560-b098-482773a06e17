<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Backend & Video Page Test</h1>
    
    <div id="results"></div>
    
    <div class="test-item">
        <h3>Manual Tests:</h3>
        <button onclick="testBackendHealth()">Test Backend Health</button>
        <button onclick="testVideoRedirect()">Test Video Redirect</button>
        <button onclick="testDirectPHP()">Test Direct PHP</button>
        <button onclick="testSimpleVideo()">Test Simple Video</button>
        <button onclick="testDatabaseCheck()">Test Database</button>
    </div>
    
    <div class="test-item">
        <h3>Direct Links:</h3>
        <a href="http://localhost:3000/health" target="_blank">Backend Health Check</a><br>
        <a href="http://localhost:3000/video" target="_blank">Video Route (Backend)</a><br>
        <a href="http://localhost/react-news/frontend/src/pages/user/video.php" target="_blank">Direct PHP Video</a><br>
        <a href="http://localhost/react-news/frontend/src/pages/user/video-simple.php" target="_blank">Simple Video Test</a><br>
        <a href="http://localhost/react-news/frontend/src/pages/user/test-video-db.php" target="_blank">Database Test</a><br>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-item ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
        }

        async function testBackendHealth() {
            try {
                addResult('Testing backend health...', 'info');
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                
                if (response.ok) {
                    addResult(`✅ Backend is running: ${data.message}`, 'success');
                } else {
                    addResult(`❌ Backend error: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Backend connection failed: ${error.message}`, 'error');
            }
        }

        async function testVideoRedirect() {
            try {
                addResult('Testing video redirect...', 'info');
                const response = await fetch('http://localhost:3000/video', {
                    method: 'GET',
                    redirect: 'manual'
                });
                
                if (response.type === 'opaqueredirect' || response.status === 302) {
                    addResult('✅ Video redirect is working', 'success');
                } else {
                    addResult(`⚠️ Unexpected response: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Video redirect test failed: ${error.message}`, 'error');
            }
        }

        async function testDirectPHP() {
            try {
                addResult('Testing direct PHP access...', 'info');
                const response = await fetch('http://localhost/react-news/frontend/src/pages/user/video.php');
                
                if (response.ok) {
                    const text = await response.text();
                    if (text.includes('Video - News App')) {
                        addResult('✅ Direct PHP access works', 'success');
                    } else {
                        addResult('⚠️ PHP file loaded but content unexpected', 'error');
                    }
                } else {
                    addResult(`❌ Direct PHP access failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Direct PHP test failed: ${error.message}`, 'error');
            }
        }

        async function testSimpleVideo() {
            try {
                addResult('Testing simple video page...', 'info');
                const response = await fetch('http://localhost/react-news/frontend/src/pages/user/video-simple.php');
                
                if (response.ok) {
                    addResult('✅ Simple video page accessible', 'success');
                } else {
                    addResult(`❌ Simple video page failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Simple video test failed: ${error.message}`, 'error');
            }
        }

        async function testDatabaseCheck() {
            try {
                addResult('Testing database check...', 'info');
                const response = await fetch('http://localhost/react-news/frontend/src/pages/user/test-video-db.php');
                
                if (response.ok) {
                    addResult('✅ Database test page accessible', 'success');
                } else {
                    addResult(`❌ Database test failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Database test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run tests on page load
        window.onload = function() {
            addResult('Starting automatic tests...', 'info');
            setTimeout(testBackendHealth, 500);
            setTimeout(testVideoRedirect, 1000);
            setTimeout(testDirectPHP, 1500);
            setTimeout(testSimpleVideo, 2000);
            setTimeout(testDatabaseCheck, 2500);
        };
    </script>
</body>
</html>
