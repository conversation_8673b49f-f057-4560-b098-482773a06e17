<?php
// Enable error display for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Set content type first
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// sendJsonResponse function is already defined in connect.php

// Helper function to get notifications
function getNotifications($limit = 10, $unread_only = false) {
    try {
        $pdo = getConnection();

        $sql = "SELECT * FROM notifications";
        if ($unread_only) {
            $sql .= " WHERE is_read = 0";
        }
        $sql .= " ORDER BY created_at DESC LIMIT ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting notifications: " . $e->getMessage());
        return [];
    }
}

// Helper function to get unread notification count
function getUnreadNotificationCount() {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE is_read = 0");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    } catch (Exception $e) {
        error_log("Error getting unread notification count: " . $e->getMessage());
        return 0;
    }
}

// Helper function to generate slug
function generateSlug($text) {
    // Handle empty or null text
    if (empty($text)) {
        $text = 'untitled-post';
    }

    // Replace non-alphanumeric characters with hyphens
    $slug = preg_replace('/[^a-zA-Z0-9\s]/', '', $text);
    $slug = preg_replace('/\s+/', '-', trim($slug));
    $slug = strtolower($slug);

    // If slug is empty after processing, use default
    if (empty($slug)) {
        $slug = 'untitled-post';
    }

    // Ensure uniqueness
    $pdo = getConnection();
    $originalSlug = $slug;
    $counter = 1;

    while (true) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetchColumn() == 0) {
            break;
        }
        $slug = $originalSlug . '-' . $counter;
        $counter++;
    }

    return $slug;
}





// Function to add news with complete fields
function addNewsComplete($title, $content, $status, $image, $category_id, $image_alt = '', $excerpt = '', $meta_title = '', $meta_description = '', $tags = '', $featured = 0, $image_base64 = '') {
    try {
        $pdo = getConnection();

        // Generate slug from title
        $slug = generateSlug($title);

        // Generate excerpt from content if not provided
        if (empty($excerpt)) {
            $excerpt = substr(strip_tags($content), 0, 200) . '...';
        }

        // Use title as meta_title if not provided
        if (empty($meta_title)) {
            $meta_title = $title;
        }

        // Generate meta_description from excerpt if not provided
        if (empty($meta_description)) {
            $meta_description = $excerpt;
        }

        // Calculate reading time (average 200 words per minute)
        $wordCount = str_word_count(strip_tags($content));
        $readingTime = max(1, ceil($wordCount / 200));

        // Set published_at if status is published
        $publishedAt = ($status === 'published') ? date('Y-m-d H:i:s') : null;

        $stmt = $pdo->prepare("
            INSERT INTO posts (
                title, slug, description, content, excerpt, image, image_base64, image_alt,
                category_id, status, featured, meta_title, meta_description, tags,
                reading_time, published_at, date, user_id, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 1, NOW(), NOW())
        ");

        return $stmt->execute([
            $title, $slug, $excerpt, $content, $excerpt, $image, $image_base64, $image_alt,
            $category_id, $status, $featured, $meta_title, $meta_description, $tags,
            $readingTime, $publishedAt
        ]);
    } catch (Exception $e) {
        error_log("Error in addNewsComplete: " . $e->getMessage());
        return false;
    }
}

// Function to add news (legacy compatibility)
function addNews($title, $content, $status, $image, $category_id) {
    return addNewsComplete($title, $content, $status, $image, $category_id);
}

// Function to update news with complete fields
function updateNewsComplete($id, $title, $content, $status, $image = null, $category_id = null, $image_alt = '', $excerpt = '', $meta_title = '', $meta_description = '', $tags = '', $featured = 0) {
    try {
        $pdo = getConnection();

        // Get current news data
        $currentNews = getNewsById($id);
        if (!$currentNews) {
            return false;
        }

        // Generate new slug if title changed
        $slug = ($currentNews['title'] !== $title) ? generateSlug($title) : $currentNews['slug'];

        // Use provided excerpt or generate from content
        if (empty($excerpt)) {
            $excerpt = substr(strip_tags($content), 0, 200) . '...';
        }

        // Calculate reading time
        $wordCount = str_word_count(strip_tags($content));
        $readingTime = max(1, ceil($wordCount / 200));

        // Set published_at if status changed to published
        $publishedAt = null;
        if ($status === 'published' && $currentNews['status'] !== 'published') {
            $publishedAt = date('Y-m-d H:i:s');
        } elseif ($currentNews['published_at']) {
            $publishedAt = $currentNews['published_at'];
        }

        // Use current values if not provided
        if ($category_id === null) {
            $category_id = $currentNews['category_id'];
        }
        if (empty($meta_title)) {
            $meta_title = $currentNews['meta_title'] ?? $title;
        }
        if (empty($meta_description)) {
            $meta_description = $currentNews['meta_description'] ?? $excerpt;
        }

        // Prepare SQL based on whether image is being updated
        if ($image) {
            $stmt = $pdo->prepare("
                UPDATE posts
                SET title = ?, slug = ?, description = ?, content = ?, excerpt = ?,
                    image = ?, image_alt = ?, category_id = ?, status = ?, featured = ?,
                    meta_title = ?, meta_description = ?, tags = ?, reading_time = ?,
                    published_at = ?, updated_at = NOW()
                WHERE id = ?
            ");
            return $stmt->execute([
                $title, $slug, $excerpt, $content, $excerpt,
                $image, $image_alt, $category_id, $status, $featured,
                $meta_title, $meta_description, $tags, $readingTime,
                $publishedAt, $id
            ]);
        } else {
            // Keep current image and image_alt if not updating
            $currentImage = $currentNews['image'];
            $currentImageAlt = empty($image_alt) ? $currentNews['image_alt'] : $image_alt;

            $stmt = $pdo->prepare("
                UPDATE posts
                SET title = ?, slug = ?, description = ?, content = ?, excerpt = ?,
                    image_alt = ?, category_id = ?, status = ?, featured = ?,
                    meta_title = ?, meta_description = ?, tags = ?, reading_time = ?,
                    published_at = ?, updated_at = NOW()
                WHERE id = ?
            ");
            return $stmt->execute([
                $title, $slug, $excerpt, $content, $excerpt,
                $currentImageAlt, $category_id, $status, $featured,
                $meta_title, $meta_description, $tags, $readingTime,
                $publishedAt, $id
            ]);
        }
    } catch (Exception $e) {
        error_log("Error in updateNewsComplete: " . $e->getMessage());
        return false;
    }
}

// Function to update news (legacy compatibility)
function updateNews($id, $title, $content, $status, $image = null, $category_id = null) {
    return updateNewsComplete($id, $title, $content, $status, $image, $category_id);
}

// Function to delete news permanently
function deleteNews($id) {
    try {
        $pdo = getConnection();

        // Get news data first to delete associated image file
        $stmt = $pdo->prepare("SELECT image FROM posts WHERE id = ?");
        $stmt->execute([$id]);
        $news = $stmt->fetch(PDO::FETCH_ASSOC);

        error_log("Deleting news ID: " . $id);
        if ($news) {
            error_log("News image path: " . ($news['image'] ?? 'none'));
        }

        // Delete associated image file if exists
        if ($news && $news['image']) {
            $imagePath = $news['image'];
            $filePath = '';

            // Handle different path formats - prioritize root/uploads
            if (strpos($imagePath, '/uploads/') === 0) {
                $filename = str_replace('/uploads/', '', $imagePath);
                $filePath = __DIR__ . '/../../../uploads/' . $filename;
            } else if (strpos($imagePath, '/react-news/uploads/') === 0) {
                $filename = str_replace('/react-news/uploads/', '', $imagePath);
                $filePath = __DIR__ . '/../../../uploads/' . $filename;
            } else if (strpos($imagePath, '/react-news/frontend/uploads/') === 0) {
                $filename = str_replace('/react-news/frontend/uploads/', '', $imagePath);
                $filePath = __DIR__ . '/../../../uploads/' . $filename;
            } else if (strpos($imagePath, 'assets/') === 0) {
                $filePath = __DIR__ . '/' . $imagePath;
            } else if (!strpos($imagePath, '/')) {
                // Just filename
                $filePath = __DIR__ . '/../../../uploads/' . $imagePath;
            }

            if ($filePath && file_exists($filePath)) {
                if (unlink($filePath)) {
                    error_log("Image file deleted: " . $filePath);
                } else {
                    error_log("Failed to delete image file: " . $filePath);
                }
            } else {
                error_log("Image file not found: " . $filePath);
            }
        }

        // Delete from related tables first (to maintain referential integrity)

        // Delete from saved table
        $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ?");
        $stmt->execute([$id]);
        error_log("Deleted from saved table for post ID: " . $id);

        // Delete from comments table if exists
        $stmt = $pdo->prepare("DELETE FROM comments WHERE post_id = ?");
        $stmt->execute([$id]);
        error_log("Deleted from comments table for post ID: " . $id);

        // Delete from page_views table if exists
        $stmt = $pdo->prepare("DELETE FROM page_views WHERE post_id = ?");
        $stmt->execute([$id]);
        error_log("Deleted from page_views table for post ID: " . $id);

        // Finally delete the post itself
        $stmt = $pdo->prepare("DELETE FROM posts WHERE id = ?");
        $result = $stmt->execute([$id]);

        if ($result) {
            error_log("Successfully deleted news ID: " . $id);
        } else {
            error_log("Failed to delete news ID: " . $id);
        }

        return $result;
    } catch (Exception $e) {
        error_log("Error in deleteNews: " . $e->getMessage());
        return false;
    }
}

// Function to handle logo file upload
function handleLogoUpload($file) {
    // Create uploads directory in root folder (outside frontend)
    $uploadDir = __DIR__ . '/../../../uploads/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    // Validate file type - Support all common image formats
    $allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        'image/bmp', 'image/tiff', 'image/tif', 'image/svg+xml', 'image/ico',
        'image/x-icon', 'image/vnd.microsoft.icon'
    ];
    $fileType = $file['type'];

    // Also check file extension as backup validation
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico'];

    if (!in_array($fileType, $allowedTypes) && !in_array($extension, $allowedExtensions)) {
        return ['success' => false, 'message' => 'Format file tidak didukung. Gunakan JPG, PNG, GIF, WebP, BMP, TIFF, SVG, atau ICO.'];
    }

    // Validate file size (2MB max)
    if ($file['size'] > 2 * 1024 * 1024) {
        return ['success' => false, 'message' => 'File size too large. Maximum 2MB allowed.'];
    }

    // Generate unique filename with random characters
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $randomString = bin2hex(random_bytes(8)); // 16 character random string
    $filename = 'logo_' . time() . '_' . $randomString . '.' . $extension;
    $filePath = $uploadDir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // Return web-accessible path for database storage
        $webPath = '/react-news/uploads/' . $filename;
        return [
            'success' => true,
            'path' => $webPath,
            'full_path' => $filePath,
            'filename' => $filename,
            'url' => 'http://localhost' . $webPath
        ];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

// Function to get image URL - prioritize file, fallback to base64
function getNewsImageUrl($imagePath, $imageBase64 = '') {
    if (!$imagePath && !$imageBase64) {
        return null;
    }

    // If we have a file path, check if file exists
    if ($imagePath) {
        $filename = '';
        if (strpos($imagePath, '/uploads/') === 0) {
            $filename = str_replace('/uploads/', '', $imagePath);
        } else if (strpos($imagePath, '/react-news/uploads/') === 0) {
            $filename = str_replace('/react-news/uploads/', '', $imagePath);
        } else if (!strpos($imagePath, '/')) {
            $filename = $imagePath;
        } else {
            $filename = basename($imagePath);
        }

        $filePath = __DIR__ . '/../../../uploads/' . $filename;
        if (file_exists($filePath)) {
            return 'http://localhost/uploads/' . $filename;
        }
    }

    // If file doesn't exist but we have base64, return data URL
    if ($imageBase64) {
        return 'data:image/jpeg;base64,' . $imageBase64;
    }

    return null;
}

function handleNewsImageUpload($file) {
    // Create uploads directory in root folder (standardized location)
    $uploadDir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    error_log("Upload directory: " . $uploadDir);
    error_log("Current directory: " . __DIR__);

    // Validate file type - Support all common image formats
    $allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        'image/bmp', 'image/tiff', 'image/tif', 'image/svg+xml', 'image/ico',
        'image/x-icon', 'image/vnd.microsoft.icon'
    ];
    $fileType = $file['type'];

    // Also check file extension as backup validation
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico'];

    if (!in_array($fileType, $allowedTypes) && !in_array($extension, $allowedExtensions)) {
        return ['success' => false, 'message' => 'Format file tidak didukung. Gunakan JPG, PNG, GIF, WebP, BMP, TIFF, SVG, atau ICO.'];
    }

    // Validate file size (5MB max for news images)
    if ($file['size'] > 5 * 1024 * 1024) {
        return ['success' => false, 'message' => 'File size too large. Maximum 5MB allowed.'];
    }

    // Generate unique filename for news
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'news_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $filePath = $uploadDir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // Read file and encode to base64 for database storage
        $imageData = file_get_contents($filePath);
        $base64Image = base64_encode($imageData);

        // Return both file path and base64 data
        $webPath = '/react-news/frontend/uploads/' . $filename;
        return [
            'success' => true,
            'path' => $webPath,
            'full_path' => $filePath,
            'filename' => $filename,
            'base64' => $base64Image,
            'url' => 'http://localhost' . $webPath
        ];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

// Helper function to generate thumbnail from uploaded video
function generateVideoThumbnail($videoBase64, $videoFormat, $videoId) {
    try {
        $uploadDir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/';

        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Create temporary video file
        $tempVideoPath = $uploadDir . 'temp_video_' . $videoId . '.' . $videoFormat;
        $videoContent = base64_decode($videoBase64);
        file_put_contents($tempVideoPath, $videoContent);

        // Generate thumbnail filename
        $thumbnailFilename = 'video_thumb_' . $videoId . '_' . time() . '.jpg';
        $thumbnailPath = $uploadDir . $thumbnailFilename;

        // Try to use FFmpeg to extract thumbnail
        $ffmpegCommands = [
            'ffmpeg',
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg',
            'C:\\ffmpeg\\bin\\ffmpeg.exe' // Windows path
        ];

        $thumbnailGenerated = false;
        foreach ($ffmpegCommands as $ffmpegPath) {
            if (is_executable($ffmpegPath) || $ffmpegPath === 'ffmpeg') {
                $command = "\"$ffmpegPath\" -i \"$tempVideoPath\" -ss 00:00:01 -vframes 1 -y \"$thumbnailPath\" 2>&1";
                exec($command, $output, $returnCode);

                if ($returnCode === 0 && file_exists($thumbnailPath)) {
                    $thumbnailGenerated = true;
                    break;
                }
            }
        }

        // Clean up temporary video file
        if (file_exists($tempVideoPath)) {
            unlink($tempVideoPath);
        }

        if ($thumbnailGenerated) {
            return [
                'success' => true,
                'path' => '/react-news/uploads/' . $thumbnailFilename,
                'filename' => $thumbnailFilename
            ];
        } else {
            // Fallback: Create a default thumbnail using GD
            return generateDefaultVideoThumbnail($videoId);
        }
    } catch (Exception $e) {
        error_log("Error generating video thumbnail: " . $e->getMessage());
        return generateDefaultVideoThumbnail($videoId);
    }
}

// Helper function to generate default video thumbnail using GD
function generateDefaultVideoThumbnail($videoId) {
    try {
        $uploadDir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/';

        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Create a default thumbnail image
        $width = 320;
        $height = 180;
        $image = imagecreatetruecolor($width, $height);

        // Colors
        $backgroundColor = imagecolorallocate($image, 229, 231, 235); // Gray-200
        $iconColor = imagecolorallocate($image, 107, 114, 128); // Gray-500
        $textColor = imagecolorallocate($image, 75, 85, 99); // Gray-600

        // Fill background
        imagefill($image, 0, 0, $backgroundColor);

        // Draw play button circle
        $centerX = $width / 2;
        $centerY = $height / 2;
        $circleRadius = 30;
        imagefilledellipse($image, $centerX, $centerY, $circleRadius * 2, $circleRadius * 2, $iconColor);

        // Draw play triangle
        $trianglePoints = [
            $centerX - 10, $centerY - 15,  // Top left
            $centerX - 10, $centerY + 15,  // Bottom left
            $centerX + 15, $centerY        // Right point
        ];
        imagefilledpolygon($image, $trianglePoints, 3, $backgroundColor);

        // Add text
        $text = "Video Upload";
        $fontSize = 3;
        $textWidth = imagefontwidth($fontSize) * strlen($text);
        $textX = ($width - $textWidth) / 2;
        $textY = $centerY + 50;
        imagestring($image, $fontSize, $textX, $textY, $text, $textColor);

        // Save image
        $thumbnailFilename = 'video_thumb_default_' . $videoId . '_' . time() . '.jpg';
        $thumbnailPath = $uploadDir . $thumbnailFilename;

        if (imagejpeg($image, $thumbnailPath, 80)) {
            imagedestroy($image);
            return [
                'success' => true,
                'path' => '/react-news/uploads/' . $thumbnailFilename,
                'filename' => $thumbnailFilename
            ];
        } else {
            imagedestroy($image);
            return ['success' => false, 'message' => 'Gagal membuat thumbnail default'];
        }
    } catch (Exception $e) {
        error_log("Error generating default video thumbnail: " . $e->getMessage());
        return ['success' => false, 'message' => 'Gagal membuat thumbnail'];
    }
}

function handleVideoFileUpload($file) {
    // Create uploads directory in root folder
    $uploadDir = __DIR__ . '/../../../uploads/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Gagal membuat direktori upload'];
        }
    }

    // Set proper permissions
    chmod($uploadDir, 0755);

    // Validate file upload
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'File tidak valid atau tidak terupload'];
    }

    // Check if file exists (for real uploads, this should be in temp directory)
    if (!file_exists($file['tmp_name'])) {
        return ['success' => false, 'message' => 'File temporary tidak ditemukan'];
    }

    // Validate file type - Support common video formats
    $allowedTypes = [
        'video/mp4', 'video/avi', 'video/mov', 'video/webm', 'video/mkv',
        'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv', 'video/3gpp',
        'video/ogg', 'video/flv', 'video/x-flv'
    ];
    $fileType = $file['type'];

    // Also check file extension as backup validation
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowedExtensions = ['mp4', 'avi', 'mov', 'webm', 'mkv', 'wmv', '3gp', 'ogg', 'flv'];

    if (!in_array($fileType, $allowedTypes) && !in_array($extension, $allowedExtensions)) {
        return ['success' => false, 'message' => 'Format file tidak didukung. Gunakan MP4, AVI, MOV, WebM, MKV, WMV, 3GP, OGG, atau FLV.'];
    }

    // Validate file size (50MB max for videos - lebih realistis)
    if ($file['size'] > 50 * 1024 * 1024) {
        return ['success' => false, 'message' => 'Ukuran file terlalu besar. Maksimal 50MB.'];
    }

    // Generate unique filename for video
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = 'video_' . time() . '_' . rand(1000, 9999) . '.' . $extension;

    // Ensure upload directory path is correct
    $realUploadDir = realpath($uploadDir);
    if (!$realUploadDir) {
        return ['success' => false, 'message' => 'Upload directory tidak valid'];
    }

    $filePath = $realUploadDir . DIRECTORY_SEPARATOR . $filename;

    // Move uploaded file
    $moveSuccess = false;

    // Debug logging
    error_log("Video upload - Attempting to move file");
    error_log("Video upload - Source: " . $file['tmp_name']);
    error_log("Video upload - Destination: " . $filePath);
    error_log("Video upload - Is uploaded file: " . (is_uploaded_file($file['tmp_name']) ? 'YES' : 'NO'));

    if (is_uploaded_file($file['tmp_name'])) {
        $moveSuccess = move_uploaded_file($file['tmp_name'], $filePath);
        error_log("Video upload - move_uploaded_file result: " . ($moveSuccess ? 'SUCCESS' : 'FAILED'));
    } else {
        // Fallback for testing or non-HTTP uploads
        $moveSuccess = copy($file['tmp_name'], $filePath);
        error_log("Video upload - copy fallback result: " . ($moveSuccess ? 'SUCCESS' : 'FAILED'));
    }

    if ($moveSuccess) {
        // Set file permissions
        chmod($filePath, 0644);

        // Verify file was actually moved and is accessible
        if (file_exists($filePath) && is_readable($filePath)) {
            $actualSize = filesize($filePath);
            error_log("Video upload - File verification SUCCESS");
            error_log("Video upload - File size: " . $actualSize . " bytes");

            // Return consistent path format for database storage
            // Store filename only, construct full path when needed
            return [
                'success' => true,
                'path' => $filename, // Store just filename in database
                'full_path' => $filePath,
                'filename' => $filename,
                'size' => $actualSize, // Use actual file size
                'format' => $extension,
                'url' => 'http://localhost/react-news/uploads/' . $filename
            ];
        } else {
            error_log("Video upload - File verification FAILED");
            error_log("Video upload - File exists: " . (file_exists($filePath) ? 'YES' : 'NO'));
            error_log("Video upload - File readable: " . (is_readable($filePath) ? 'YES' : 'NO'));

            // Clean up if verification failed
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            return ['success' => false, 'message' => 'File upload gagal - tidak dapat diverifikasi'];
        }
    } else {
        $errorMsg = 'Gagal mengupload file';
        switch ($file['error']) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errorMsg = 'File terlalu besar';
                break;
            case UPLOAD_ERR_PARTIAL:
                $errorMsg = 'File hanya terupload sebagian';
                break;
            case UPLOAD_ERR_NO_FILE:
                $errorMsg = 'Tidak ada file yang dipilih';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $errorMsg = 'Direktori temporary tidak ditemukan';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $errorMsg = 'Gagal menulis file ke disk';
                break;
        }
        return ['success' => false, 'message' => $errorMsg];
    }
}

// Try to include config and handle errors
try {
    require_once __DIR__ . '/config.php';
} catch (Exception $e) {
    sendJsonResponse(['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()]);
}

// Get action from POST, GET, or JSON body
$action = $_POST['action'] ?? $_GET['action'] ?? '';

// If no action found in POST/GET, try JSON body
if (empty($action)) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
}

// Debug logging (remove in production)
error_log("API Action: " . $action);
error_log("POST data: " . print_r($_POST, true));
error_log("JSON input: " . file_get_contents('php://input'));

// Create uploads directory if it doesn't exist
$uploadsDir = __DIR__ . '/assets/news';
if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Wrap everything in try-catch to handle errors properly
try {
    switch ($action) {
        case 'get_stats':
            $stats = getStats();
            sendJsonResponse(['success' => true, 'data' => $stats]);
            break;

        case 'get_notifications':
            $limit = $_GET['limit'] ?? 10;
            $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
            $notifications = getNotifications($limit, $unread_only);
            $unread_count = getUnreadNotificationCount();

            sendJsonResponse([
                'success' => true,
                'data' => $notifications,
                'unread_count' => $unread_count
            ]);
            break;

        case 'mark_notification_read':
            $id = $_POST['id'] ?? null;
            if ($id) {
                $result = markNotificationAsRead($id);
                sendJsonResponse(['success' => $result]);
            } else {
                sendJsonResponse(['success' => false, 'message' => 'ID required']);
            }
            break;

        case 'mark_all_notifications_read':
            $result = markAllNotificationsAsRead();
            sendJsonResponse(['success' => $result]);
            break;

        case 'add_notification':
            $title = $_POST['title'] ?? '';
            $message = $_POST['message'] ?? '';
            $type = $_POST['type'] ?? 'info';
            $action_type = $_POST['action_type'] ?? 'create';
            $entity_type = $_POST['entity_type'] ?? 'news';
            $entity_id = $_POST['entity_id'] ?? null;

            if (empty($title) || empty($message)) {
                sendJsonResponse(['success' => false, 'message' => 'Title and message are required']);
            }

            $result = addNotification($title, $message, $type, $action_type, $entity_type, $entity_id);
            sendJsonResponse(['success' => $result]);
            break;

        case 'get_news':
            $limit = $_GET['limit'] ?? null;
            $category = $_GET['category'] ?? null;
            error_log("Getting news with limit: " . ($limit ?? 'none') . ", category: " . ($category ?? 'none'));
            $result = getNews($limit, $category);
            error_log("News result: " . json_encode($result));
            sendJsonResponse($result);
            break;

        case 'add_news':
            $title = $_POST['title'] ?? '';
            $content = $_POST['content'] ?? '';
            $status = $_POST['status'] ?? 'draft';
            $category_id = $_POST['category_id'] ?? 1;
            $image_alt = $_POST['image_alt'] ?? '';
            $excerpt = $_POST['excerpt'] ?? '';
            $meta_title = $_POST['meta_title'] ?? '';
            $meta_description = $_POST['meta_description'] ?? '';
            $tags = $_POST['tags'] ?? '';
            $featured = isset($_POST['featured']) && $_POST['featured'] == '1' ? 1 : 0;
            $image = null;

            if (empty($title) || empty($content)) {
                sendJsonResponse(['success' => false, 'message' => 'Title and content are required']);
            }

            // Handle image upload
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['image'];

                // Validate file type - Support all common image formats
                $allowedTypes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                    'image/bmp', 'image/tiff', 'image/tif', 'image/svg+xml', 'image/ico',
                    'image/x-icon', 'image/vnd.microsoft.icon'
                ];

                // Also check file extension as backup validation
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico'];

                if (!in_array($file['type'], $allowedTypes) && !in_array($extension, $allowedExtensions)) {
                    sendJsonResponse(['success' => false, 'message' => 'Format file tidak didukung. Gunakan JPG, PNG, GIF, WebP, BMP, TIFF, SVG, atau ICO.']);
                }

                // Validate file size (5MB max for news images)
                if ($file['size'] > 5 * 1024 * 1024) {
                    sendJsonResponse(['success' => false, 'message' => 'File size too large. Maximum 5MB allowed.']);
                }

                // Use the same upload handler as logo
                $uploadResult = handleNewsImageUpload($file);
                if ($uploadResult['success']) {
                    $image = $uploadResult['path'];
                    $image_base64 = $uploadResult['base64'];
                    error_log("Image uploaded successfully. Path saved to database: " . $image);
                    error_log("Full file path: " . $uploadResult['full_path']);
                    error_log("Web URL: " . $uploadResult['url']);
                } else {
                    error_log("Image upload failed: " . $uploadResult['message']);
                    sendJsonResponse(['success' => false, 'message' => $uploadResult['message']]);
                }
            }

            $result = addNewsComplete($title, $content, $status, $image, $category_id, $image_alt, $excerpt, $meta_title, $meta_description, $tags, $featured, $image_base64 ?? '');

            // Add notification for successful news creation
            if ($result) {
                addNotification(
                    'Berita Berhasil Ditambahkan',
                    "Berita \"$title\" berhasil ditambahkan dengan status $status",
                    'create',
                    'news',
                    'success',
                    $result // Assuming addNewsComplete returns the news ID
                );
            }

            sendJsonResponse(['success' => $result, 'message' => $result ? 'News added successfully' : 'Failed to add news']);
            break;

        case 'update_news':
            $id = $_POST['id'] ?? 0;
            $title = $_POST['title'] ?? '';
            $content = $_POST['content'] ?? '';
            $status = $_POST['status'] ?? 'draft';
            $category_id = $_POST['category_id'] ?? null;
            $image_alt = $_POST['image_alt'] ?? '';
            $excerpt = $_POST['excerpt'] ?? '';
            $meta_title = $_POST['meta_title'] ?? '';
            $meta_description = $_POST['meta_description'] ?? '';
            $tags = $_POST['tags'] ?? '';
            $featured = isset($_POST['featured']) && $_POST['featured'] == '1' ? 1 : 0;
            $image = null;

            if (empty($id) || empty($title) || empty($content)) {
                sendJsonResponse(['success' => false, 'message' => 'ID, title and content are required']);
            }

            // Handle image upload
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['image'];

                // Use the same upload handler as add_news
                $uploadResult = handleNewsImageUpload($file);
                if ($uploadResult['success']) {
                    $image = $uploadResult['path'];

                    // Delete old image if exists
                    $oldNews = getNewsById($id);
                    if ($oldNews && $oldNews['image']) {
                        // Handle both old and new path formats
                        $oldImagePath = '';
                        if (strpos($oldNews['image'], '/uploads/') === 0) {
                            $filename = str_replace('/uploads/', '', $oldNews['image']);
                            $oldImagePath = __DIR__ . '/../../../uploads/' . $filename;
                        } else if (strpos($oldNews['image'], '/react-news/uploads/') === 0) {
                            $filename = str_replace('/react-news/uploads/', '', $oldNews['image']);
                            $oldImagePath = __DIR__ . '/../../../uploads/' . $filename;
                        } else if (strpos($oldNews['image'], 'assets/') === 0) {
                            $oldImagePath = __DIR__ . '/' . $oldNews['image'];
                        }

                        if ($oldImagePath && file_exists($oldImagePath)) {
                            unlink($oldImagePath);
                        }
                    }
                } else {
                    sendJsonResponse(['success' => false, 'message' => $uploadResult['message']]);
                }
            }

            $result = updateNewsComplete($id, $title, $content, $status, $image, $category_id, $image_alt, $excerpt, $meta_title, $meta_description, $tags, $featured);

            // Add notification for successful news update
            if ($result) {
                addNotification(
                    'Berita Berhasil Diperbarui',
                    "Berita \"$title\" berhasil diperbarui dengan status $status",
                    'update',
                    'news',
                    'success',
                    $id
                );
            }

            sendJsonResponse(['success' => $result, 'message' => $result ? 'News updated successfully' : 'Failed to update news']);
            break;

        case 'delete_news':
            $id = $_POST['id'] ?? 0;

            if (empty($id)) {
                error_log("Delete news failed: ID is required");
                sendJsonResponse(['success' => false, 'message' => 'ID is required']);
            }

            error_log("Attempting to delete news with ID: " . $id);

            // Get news title before deletion for notification
            $newsData = getNewsById($id);
            $newsTitle = $newsData ? $newsData['title'] : 'Berita';

            $result = deleteNews($id);

            $message = $result ? 'News deleted successfully' : 'Failed to delete news';
            error_log("Delete news result for ID " . $id . ": " . ($result ? 'SUCCESS' : 'FAILED'));

            // Add notification for successful news deletion
            if ($result) {
                addNotification(
                    'Berita Berhasil Dihapus',
                    "Berita \"$newsTitle\" berhasil dihapus dari sistem",
                    'delete',
                    'news',
                    'success',
                    $id
                );
            }

            sendJsonResponse(['success' => $result, 'message' => $message]);
            break;

        case 'get_news_by_id':
            $id = $_GET['id'] ?? 0;

            if (empty($id)) {
                error_log("Get news by ID failed: ID is required");
                sendJsonResponse(['success' => false, 'message' => 'ID is required']);
            }

            error_log("Getting news by ID: " . $id);
            $news = getNewsById($id);
            error_log("Get news by ID result: " . ($news ? json_encode($news) : 'false'));
            sendJsonResponse(['success' => $news !== false, 'data' => $news]);
            break;

        case 'get_saved_news':
            $savedNews = getSavedNews();
            sendJsonResponse(['success' => true, 'data' => $savedNews]);
            break;

        case 'add_saved_news':
            $input = json_decode(file_get_contents('php://input'), true);
            $postId = $input['post_id'] ?? 0;
            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $result = addSavedNews($postId);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'News saved successfully' : 'Failed to save news']);
            break;

        case 'remove_saved_news':
            $input = json_decode(file_get_contents('php://input'), true);
            $postId = $input['post_id'] ?? 0;
            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $result = removeSavedNews($postId);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'News removed from saved successfully' : 'Failed to remove news from saved']);
            break;

        case 'update_website_settings':
            $websiteName = $_POST['website_name'] ?? '';
            $websiteDescription = $_POST['website_description'] ?? '';
            $success = true;
            $messages = [];
            $logoPath = '';

            // Handle logo file upload
            if (isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = handleLogoUpload($_FILES['logo_file']);
                if ($uploadResult['success']) {
                    $logoPath = $uploadResult['path'];
                    if (!updateSetting('website_logo', $logoPath)) {
                        $success = false;
                        $messages[] = 'Failed to save logo path to database';
                    }
                } else {
                    $success = false;
                    $messages[] = $uploadResult['message'];
                }
            }

            // Update website name
            if (!empty($websiteName)) {
                if (!updateSetting('website_name', $websiteName)) {
                    $success = false;
                    $messages[] = 'Failed to update website name';
                }
            }

            // Update website description
            if (!empty($websiteDescription)) {
                if (!updateSetting('website_description', $websiteDescription)) {
                    $success = false;
                    $messages[] = 'Failed to update website description';
                }
            }

            sendJsonResponse([
                'success' => $success,
                'message' => $success ? 'Website settings updated successfully' : implode(', ', $messages),
                'data' => [
                    'website_name' => $websiteName,
                    'website_logo' => $logoPath ?: getSetting('website_logo'),
                    'website_description' => $websiteDescription
                ]
            ]);
            break;

        case 'update_color_settings':
            $primaryColor = $_POST['primary_color'] ?? '';
            $secondaryColor = $_POST['secondary_color'] ?? '';
            $accentColor = $_POST['accent_color'] ?? '';
            $success = true;
            $messages = [];

            if (!empty($primaryColor)) {
                if (!updateSetting('primary_color', $primaryColor)) {
                    $success = false;
                    $messages[] = 'Failed to update primary color';
                }
            }

            if (!empty($secondaryColor)) {
                if (!updateSetting('secondary_color', $secondaryColor)) {
                    $success = false;
                    $messages[] = 'Failed to update secondary color';
                }
            }

            if (!empty($accentColor)) {
                if (!updateSetting('accent_color', $accentColor)) {
                    $success = false;
                    $messages[] = 'Failed to update accent color';
                }
            }

            sendJsonResponse([
                'success' => $success,
                'message' => $success ? 'Color settings updated successfully' : implode(', ', $messages)
            ]);
            break;

        case 'get_settings':
            $settings = [
                'website_name' => getSetting('website_name'),
                'website_logo' => getSetting('website_logo'),
                'primary_color' => getSetting('primary_color'),
                'secondary_color' => getSetting('secondary_color'),
                'accent_color' => getSetting('accent_color')
            ];
            sendJsonResponse(['success' => true, 'data' => $settings]);
            break;

        case 'upload_logo':
            if (!isset($_FILES['logo']) || $_FILES['logo']['error'] !== UPLOAD_ERR_OK) {
                sendJsonResponse(['success' => false, 'message' => 'No file uploaded or upload error']);
            }

            $file = $_FILES['logo'];

            // Validate file type - Support all common image formats
            $allowedTypes = [
                'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
                'image/bmp', 'image/tiff', 'image/tif', 'image/svg+xml', 'image/ico',
                'image/x-icon', 'image/vnd.microsoft.icon'
            ];

            // Also check file extension as backup validation
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif', 'svg', 'ico'];

            if (!in_array($file['type'], $allowedTypes) && !in_array($extension, $allowedExtensions)) {
                sendJsonResponse(['success' => false, 'message' => 'Format file tidak didukung. Gunakan JPG, PNG, GIF, WebP, BMP, TIFF, SVG, atau ICO.']);
            }

            // Validate file size (2MB max)
            if ($file['size'] > 2 * 1024 * 1024) {
                sendJsonResponse(['success' => false, 'message' => 'File size too large. Maximum 2MB allowed.']);
            }

            // Create uploads directory if it doesn't exist
            // Path dari frontend/src/pages/admin/api.php ke root/uploads
            $uploadsDir = __DIR__ . '/../../../../uploads';
            if (!is_dir($uploadsDir)) {
                mkdir($uploadsDir, 0755, true);
            }

            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $originalFilename = $file['name'];
            $filename = 'logo_' . time() . '.' . $extension;
            $filepath = 'uploads/' . $filename;

            if (move_uploaded_file($file['tmp_name'], $uploadsDir . '/' . $filename)) {
                try {
                    // Update key-value structure for website_logo
                    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, category, is_public)
                                          VALUES ('website_logo', ?, 'file', 'general', 1)
                                          ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()");
                    $stmt->execute([$filepath, $filepath]);

                    // Update logo columns in settings table
                    $stmt = $pdo->prepare("UPDATE settings SET
                        logo_filename = ?,
                        logo_original_name = ?,
                        logo_file_path = ?,
                        logo_file_size = ?,
                        logo_mime_type = ?,
                        logo_uploaded_at = NOW()
                        WHERE setting_key = 'website_logo'");

                    $stmt->execute([
                        $filename,
                        $originalFilename,
                        $filepath,
                        $file['size'],
                        $file['type']
                    ]);

                    // Also insert to logos table for history tracking
                    $stmt = $pdo->prepare("UPDATE logos SET is_active = 0"); // Deactivate previous
                    $stmt->execute();

                    $stmt = $pdo->prepare("INSERT INTO logos (name, filename, original_filename, file_path, file_size, mime_type, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)");
                    $stmt->execute([
                        'Website Logo',
                        $filename,
                        $originalFilename,
                        $filepath,
                        $file['size'],
                        $file['type']
                    ]);

                    // Add notification for successful logo upload
                    addNotification(
                        'Logo Berhasil Diupload',
                        'Logo website berhasil diperbarui dan disimpan ke database',
                        'upload',
                        'settings',
                        'success'
                    );

                    sendJsonResponse(['success' => true, 'message' => 'Logo uploaded successfully', 'filepath' => $filepath]);
                } catch (Exception $e) {
                    sendJsonResponse(['success' => false, 'message' => 'Failed to save logo to database: ' . $e->getMessage()]);
                }
            } else {
                sendJsonResponse(['success' => false, 'message' => 'Failed to upload logo']);
            }
            break;

        case 'update_settings':
            $success = true;
            $messages = [];
            $updatedData = [];

            // Handle logo upload first if present
            $logoPath = null;
            if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = handleLogoUpload($_FILES['logo']);
                if ($uploadResult['success']) {
                    $logoPath = $uploadResult['path'];
                    if (updateSetting('website_logo', $logoPath)) {
                        $updatedData['website_logo'] = $logoPath;
                    } else {
                        $success = false;
                        $messages[] = 'Failed to save logo path to database';
                    }
                } else {
                    $success = false;
                    $messages[] = $uploadResult['message'];
                }
            }

            // Update all other settings
            $settingsToUpdate = [
                'website_name' => $_POST['website_name'] ?? '',
                'website_description' => $_POST['website_description'] ?? '',
                'primary_color' => $_POST['primary_color'] ?? '',
                'secondary_color' => $_POST['secondary_color'] ?? '',
                'accent_color' => $_POST['accent_color'] ?? '',
                'meta_keywords' => $_POST['meta_keywords'] ?? '',
                'meta_description' => $_POST['meta_description'] ?? '',
                'facebook_url' => $_POST['facebook_url'] ?? '',
                'twitter_url' => $_POST['twitter_url'] ?? '',
                'instagram_url' => $_POST['instagram_url'] ?? '',
                'youtube_url' => $_POST['youtube_url'] ?? '',
                'landing_primary_color' => $_POST['landing_primary_color'] ?? '',
                'landing_secondary_color' => $_POST['landing_secondary_color'] ?? '',
                'landing_accent_color' => $_POST['landing_accent_color'] ?? '',
                'landing_background_color' => $_POST['landing_background_color'] ?? '',
                'landing_text_color' => $_POST['landing_text_color'] ?? '',
                'sidebar_color' => $_POST['sidebar_color'] ?? '',
                'sidebar_header_color' => $_POST['sidebar_header_color'] ?? ''
            ];

            foreach ($settingsToUpdate as $key => $value) {
                if (!empty($value) || $value === '') { // Allow empty strings
                    if (updateSetting($key, $value)) {
                        $updatedData[$key] = $value;
                    } else {
                        $success = false;
                        $messages[] = "Failed to update $key";
                    }
                }
            }

            sendJsonResponse([
                'success' => $success,
                'message' => $success ? 'Settings updated successfully' : implode(', ', $messages),
                'data' => $updatedData
            ]);
            break;

        case 'get_categories':
            try {
                $categories = getCategories();
                if ($categories === false) {
                    sendJsonResponse(['success' => false, 'message' => 'Failed to fetch categories']);
                } else {
                    sendJsonResponse(['success' => true, 'data' => $categories]);
                }
            } catch (Exception $e) {
                error_log("Error in get_categories API: " . $e->getMessage());
                sendJsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
            }
            break;

        case 'get_recent_activities':
            $limit = $_GET['limit'] ?? 10;
            $activities = getRecentActivities($limit);
            sendJsonResponse(['success' => true, 'data' => $activities]);
            break;

        case 'get_popular_posts':
            $limit = $_GET['limit'] ?? 5;
            $posts = getPopularPosts($limit);
            sendJsonResponse(['success' => true, 'data' => $posts]);
            break;

        // Authentication endpoints - delegate to auth_api.php
        case 'register':
        case 'register_with_token':
        case 'login':
        case 'forgot_password':
        case 'verify_reset_token':
        case 'reset_password':
        case 'verify_token':
        case 'verify_jwt_token':
        case 'reset_password_with_jwt':
            include 'auth_api.php';
            exit;
            break;

        // User management endpoints
        case 'get_user':
            $userId = $_GET['id'] ?? $input['id'] ?? null;
            if (!$userId) {
                sendJsonResponse(['success' => false, 'message' => 'User ID is required']);
                break;
            }

            try {
                $conn = getConnection();
                $stmt = $conn->prepare("
                    SELECT u.*, pr.token as jwt_token
                    FROM users u
                    LEFT JOIN password_resets pr ON u.id = pr.user_id AND pr.expires_at > NOW()
                    WHERE u.id = ?
                ");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($user) {
                    sendJsonResponse(['success' => true, 'user' => $user]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'User not found']);
                }
            } catch (PDOException $e) {
                sendJsonResponse(['success' => false, 'message' => 'Database error']);
            }
            break;

        case 'update_user':
            $userId = $input['id'] ?? null;
            $name = $input['name'] ?? '';
            $email = $input['email'] ?? '';
            $role = $input['role'] ?? '';
            $status = $input['status'] ?? '';
            $newPassword = $input['new_password'] ?? null;

            if (!$userId || !$name || !$email || !$role || !$status) {
                sendJsonResponse(['success' => false, 'message' => 'All fields are required']);
                break;
            }

            try {
                $conn = getConnection();

                // Check if email already exists for other users
                $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $userId]);
                if ($stmt->fetch()) {
                    sendJsonResponse(['success' => false, 'message' => 'Email already exists']);
                    break;
                }

                // Update user data
                if ($newPassword) {
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET name = ?, email = ?, role = ?, status = ?, password = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$name, $email, $role, $status, $hashedPassword, $userId]);
                } else {
                    $stmt = $conn->prepare("
                        UPDATE users
                        SET name = ?, email = ?, role = ?, status = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$name, $email, $role, $status, $userId]);
                }

                sendJsonResponse(['success' => true, 'message' => 'User updated successfully']);
            } catch (PDOException $e) {
                sendJsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
            }
            break;

        case 'delete_user':
            $userId = $input['id'] ?? null;
            if (!$userId) {
                sendJsonResponse(['success' => false, 'message' => 'User ID is required']);
                break;
            }

            try {
                $conn = getConnection();

                // Check if user is admin
                $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$user) {
                    sendJsonResponse(['success' => false, 'message' => 'User not found']);
                    break;
                }

                if ($user['role'] === 'admin') {
                    sendJsonResponse(['success' => false, 'message' => 'Cannot delete admin user']);
                    break;
                }

                // Delete user
                $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$userId]);

                // Also delete related password reset tokens
                $stmt = $conn->prepare("DELETE FROM password_resets WHERE user_id = ?");
                $stmt->execute([$userId]);

                sendJsonResponse(['success' => true, 'message' => 'User deleted successfully']);
            } catch (PDOException $e) {
                sendJsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
            }
            break;

        case 'get_dashboard_data':
            $stats = getStats();
            $recentActivities = getRecentActivities(5);
            $popularPosts = getPopularPosts(5);
            $categories = getCategories();

            sendJsonResponse([
                'success' => true,
                'data' => [
                    'stats' => $stats,
                    'recent_activities' => $recentActivities,
                    'popular_posts' => $popularPosts,
                    'categories' => $categories
                ]
            ]);
            break;

        case 'get_saved_posts':
            $pdo = getConnection();
            $stmt = $pdo->query("
                SELECT
                    p.id, p.title, p.description as content, p.image,
                    p.views, p.share, p.likes, p.created_at,
                    c.name as category_name, c.color as category_color,
                    a.username as author_name
                FROM saved s
                JOIN posts p ON s.post_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN admin a ON p.user_id = a.id
                ORDER BY s.saved_at DESC
            ");
            sendJsonResponse($stmt->fetchAll());
            break;

        case 'save_post':
            $postId = $_POST['post_id'] ?? $_GET['post_id'] ?? 0;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            try {
                $stmt = $pdo->prepare("INSERT IGNORE INTO saved (post_id, ip_address) VALUES (?, ?)");
                $result = $stmt->execute([$postId, $ipAddress]);
                sendJsonResponse(['success' => $result, 'message' => $result ? 'Post saved successfully' : 'Post already saved']);
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error saving post: ' . $e->getMessage()]);
            }
            break;

        case 'unsave_post':
            $postId = $_POST['post_id'] ?? $_GET['post_id'] ?? 0;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ? AND ip_address = ?");
            $result = $stmt->execute([$postId, $ipAddress]);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'Post unsaved successfully' : 'Failed to unsave post']);
            break;

        case 'update_post_share':
            $postId = $_POST['post_id'] ?? $_GET['post_id'] ?? 0;

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE posts SET share = share + 1 WHERE id = ?");
            $result = $stmt->execute([$postId]);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'Share count updated' : 'Failed to update share count']);
            break;

        case 'update_post_views':
            $postId = $_POST['post_id'] ?? $_GET['post_id'] ?? 0;

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE posts SET views = views + 1 WHERE id = ?");
            $result = $stmt->execute([$postId]);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'View count updated' : 'Failed to update view count']);
            break;

        case 'increment_views':
            $postId = $_POST['id'] ?? $_GET['id'] ?? 0;

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE posts SET views = views + 1 WHERE id = ?");
            $result = $stmt->execute([$postId]);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'View count updated' : 'Failed to update view count']);
            break;

        case 'toggle_like':
            $postId = $_POST['id'] ?? $_GET['id'] ?? 0;

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            // Check if this IP has already liked this post
            $stmt = $pdo->prepare("SELECT id FROM post_likes WHERE post_id = ? AND ip_address = ?");
            $stmt->execute([$postId, $ipAddress]);
            $existingLike = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($existingLike) {
                // Unlike - remove the like
                $stmt = $pdo->prepare("DELETE FROM post_likes WHERE post_id = ? AND ip_address = ?");
                $stmt->execute([$postId, $ipAddress]);

                // Decrement likes count in posts table
                $stmt = $pdo->prepare("UPDATE posts SET likes = GREATEST(0, likes - 1) WHERE id = ?");
                $stmt->execute([$postId]);

                $liked = false;
                $message = 'Like removed';
            } else {
                // Like - add the like
                $stmt = $pdo->prepare("INSERT INTO post_likes (post_id, ip_address, user_agent, liked_at) VALUES (?, ?, ?, NOW())");
                $stmt->execute([$postId, $ipAddress, $userAgent]);

                // Increment likes count in posts table
                $stmt = $pdo->prepare("UPDATE posts SET likes = likes + 1 WHERE id = ?");
                $stmt->execute([$postId]);

                $liked = true;
                $message = 'Like added';
            }

            // Get updated likes count
            $stmt = $pdo->prepare("SELECT likes FROM posts WHERE id = ?");
            $stmt->execute([$postId]);
            $updatedPost = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($updatedPost) {
                sendJsonResponse([
                    'success' => true,
                    'message' => $message,
                    'likes' => $updatedPost['likes'],
                    'liked' => $liked
                ]);
            } else {
                sendJsonResponse(['success' => false, 'message' => 'Post not found']);
            }
            break;

        case 'get_liked_posts':
            $pdo = getConnection();
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

            // Get all posts liked by this IP address
            $stmt = $pdo->prepare("SELECT post_id FROM post_likes WHERE ip_address = ?");
            $stmt->execute([$ipAddress]);
            $likedPosts = $stmt->fetchAll(PDO::FETCH_COLUMN);

            sendJsonResponse([
                'success' => true,
                'data' => $likedPosts,
                'ip_address' => $ipAddress
            ]);
            break;

        case 'get_videos':
            $pdo = getConnection();

            // Get all published videos ordered by creation date
            $stmt = $pdo->prepare("
                SELECT
                    id, title, description, content, youtube_url, youtube_id,
                    thumbnail, category, tags, duration, status, views, likes,
                    shares, comments_count, featured, created_at, updated_at
                FROM videos
                WHERE status = 'published'
                ORDER BY created_at DESC
            ");
            $stmt->execute();
            $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);

            sendJsonResponse([
                'success' => true,
                'data' => $videos,
                'count' => count($videos)
            ]);
            break;

        case 'add_video':
            // Add new video
            $video_type = $_POST['video_type'] ?? 'youtube';

            // Validate based on video type
            if ($video_type === 'youtube') {
                if (empty($_POST['title']) || empty($_POST['description']) || empty($_POST['youtube_url'])) {
                    sendJsonResponse(['success' => false, 'message' => 'Judul, deskripsi, dan URL YouTube harus diisi']);
                }
            } else {
                if (empty($_POST['title']) || empty($_POST['description']) || !isset($_FILES['video_file'])) {
                    sendJsonResponse(['success' => false, 'message' => 'Judul, deskripsi, dan file video harus diisi']);
                }
            }

            $title = $_POST['title'];
            $description = $_POST['description'];
            $content = $_POST['content'] ?? '';
            $youtube_url = $video_type === 'youtube' ? $_POST['youtube_url'] : '';
            $youtube_id = $video_type === 'youtube' ? ($_POST['youtube_id'] ?? '') : '';
            $category = $_POST['category'] ?? 'Umum';
            $tags = $_POST['tags'] ?? '';
            $duration = $_POST['duration'] ?? '';
            $status = $_POST['status'] ?? 'published';
            $featured = isset($_POST['featured']) ? 1 : 0;

            // Initialize video file variables
            $video_base64 = null;
            $video_size = null;
            $video_format = null;

            // Handle video file upload for upload type - save to database instead of file
            if ($video_type === 'upload') {
                if (!isset($_FILES['video_file']) || $_FILES['video_file']['error'] !== UPLOAD_ERR_OK) {
                    sendJsonResponse(['success' => false, 'message' => 'File video harus diupload untuk tipe upload']);
                }

                $videoFile = $_FILES['video_file'];
                $video_size = $videoFile['size'];
                $video_format = strtolower(pathinfo($videoFile['name'], PATHINFO_EXTENSION));

                // Validate file size (max 50MB for better performance)
                if ($video_size > 50 * 1024 * 1024) {
                    sendJsonResponse(['success' => false, 'message' => 'Ukuran file video terlalu besar (maksimal 50MB)']);
                }

                // Validate file format
                $allowedFormats = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
                if (!in_array($video_format, $allowedFormats)) {
                    sendJsonResponse(['success' => false, 'message' => 'Format video tidak didukung. Gunakan: ' . implode(', ', $allowedFormats)]);
                }

                // Read file content and encode to base64
                $videoContent = file_get_contents($videoFile['tmp_name']);
                if ($videoContent === false) {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal membaca file video']);
                }

                $video_base64 = base64_encode($videoContent);
            }

            // Handle thumbnail upload or generation
            $thumbnail = '';
            if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK) {
                // Custom thumbnail uploaded
                $uploadResult = handleNewsImageUpload($_FILES['thumbnail']);
                if ($uploadResult['success']) {
                    $thumbnail = $uploadResult['path'];
                }
            } else if ($video_type === 'youtube' && !empty($youtube_id)) {
                // Use YouTube thumbnail if no custom thumbnail
                $thumbnail = "https://img.youtube.com/vi/$youtube_id/maxresdefault.jpg";
            } else if ($video_type === 'upload' && !empty($video_base64)) {
                // Auto-generate thumbnail from uploaded video
                $thumbnailResult = generateVideoThumbnail($video_base64, $video_format, time());
                if ($thumbnailResult['success']) {
                    $thumbnail = $thumbnailResult['path'];
                    error_log("Auto-generated thumbnail: " . $thumbnail);
                } else {
                    error_log("Failed to generate thumbnail: " . $thumbnailResult['message']);
                }
            }

            try {
                $pdo = getConnection();
                $pdo->beginTransaction();

                $stmt = $pdo->prepare("INSERT INTO videos (
                    title, description, content, youtube_url, youtube_id,
                    video_base64, video_type, video_size, video_format,
                    thumbnail, category, tags, duration, status,
                    featured, created_at, updated_at
                ) VALUES (
                    ?, ?, ?, ?, ?,
                    ?, ?, ?, ?,
                    ?, ?, ?, ?, ?,
                    ?, NOW(), NOW()
                )");

                $result = $stmt->execute([
                    $title, $description, $content, $youtube_url, $youtube_id,
                    $video_base64, $video_type, $video_size, $video_format,
                    $thumbnail, $category, $tags, $duration, $status,
                    $featured
                ]);

                if ($result) {
                    $videoId = $pdo->lastInsertId();
                    $pdo->commit();
                    sendJsonResponse([
                        'success' => true,
                        'message' => 'Video berhasil ditambahkan ke database',
                        'id' => $videoId
                    ]);
                } else {
                    $pdo->rollback();
                    sendJsonResponse(['success' => false, 'message' => 'Gagal menyimpan video ke database']);
                }
            } catch (PDOException $e) {
                $pdo->rollback();
                sendJsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
            }
            break;

        case 'get_all_videos':
            // Get all videos for admin (including drafts)
            $pdo = getConnection();

            // Get all videos ordered by creation date
            $stmt = $pdo->prepare("
                SELECT
                    id, title, description, content, youtube_url, youtube_id,
                    video_size, video_format, video_type,
                    thumbnail, category, tags, duration, status, views, likes,
                    shares, comments_count, featured, created_at, updated_at
                FROM videos
                ORDER BY created_at DESC
            ");
            $stmt->execute();
            $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);

            sendJsonResponse([
                'success' => true,
                'data' => $videos,
                'count' => count($videos)
            ]);
            break;

        case 'get_video':
            // Get single video by ID
            $videoId = $_GET['id'] ?? 0;

            if (empty($videoId)) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID is required']);
            }

            try {
                $pdo = getConnection();
                $stmt = $pdo->prepare("
                    SELECT
                        id, title, description, content, youtube_url, youtube_id,
                        video_size, video_format, video_type,
                        thumbnail, category, tags, duration, status, views, likes,
                        shares, comments_count, featured, created_at, updated_at
                    FROM videos
                    WHERE id = ?
                ");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($video) {
                    sendJsonResponse([
                        'success' => true,
                        'data' => $video
                    ]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Video not found']);
                }
            } catch (PDOException $e) {
                sendJsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
            }
            break;

        case 'update_video':
            // Update existing video
            $videoId = $_POST['video_id'] ?? 0;
            $video_type = $_POST['video_type'] ?? 'youtube';

            if (empty($videoId) || empty($_POST['title']) || empty($_POST['description'])) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID, judul, dan deskripsi harus diisi']);
            }

            // Validate based on video type
            if ($video_type === 'youtube' && empty($_POST['youtube_url'])) {
                sendJsonResponse(['success' => false, 'message' => 'URL YouTube harus diisi untuk video YouTube']);
            }

            $title = $_POST['title'];
            $description = $_POST['description'];
            $content = $_POST['content'] ?? '';
            $youtube_url = $video_type === 'youtube' ? $_POST['youtube_url'] : '';
            $youtube_id = $video_type === 'youtube' ? ($_POST['youtube_id'] ?? '') : '';
            $category = $_POST['category'] ?? 'Umum';
            $tags = $_POST['tags'] ?? '';
            $duration = $_POST['duration'] ?? '';
            $status = $_POST['status'] ?? 'published';
            $featured = isset($_POST['featured']) ? 1 : 0;

            // Handle video file upload (optional for update) - save to database instead of file
            $videoFileUpdate = '';
            $videoFileValues = [];
            if ($video_type === 'upload' && isset($_FILES['video_file']) && $_FILES['video_file']['error'] === UPLOAD_ERR_OK) {
                $videoFile = $_FILES['video_file'];
                $video_size = $videoFile['size'];
                $video_format = strtolower(pathinfo($videoFile['name'], PATHINFO_EXTENSION));

                // Validate file size (max 100MB)
                if ($video_size > 100 * 1024 * 1024) {
                    sendJsonResponse(['success' => false, 'message' => 'Ukuran file video terlalu besar (maksimal 100MB)']);
                }

                // Validate file format
                $allowedFormats = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
                if (!in_array($video_format, $allowedFormats)) {
                    sendJsonResponse(['success' => false, 'message' => 'Format video tidak didukung. Gunakan: ' . implode(', ', $allowedFormats)]);
                }

                // Read file content and encode to base64
                $videoContent = file_get_contents($videoFile['tmp_name']);
                if ($videoContent === false) {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal membaca file video']);
                }

                $video_base64 = base64_encode($videoContent);
                $videoFileUpdate = ", video_base64 = ?, video_size = ?, video_format = ?";
                $videoFileValues = [$video_base64, $video_size, $video_format];
            }

            // Handle thumbnail upload or auto-generation (optional for update)
            $thumbnailUpdate = '';
            $thumbnailValue = '';
            if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK) {
                // Custom thumbnail uploaded
                $uploadResult = handleNewsImageUpload($_FILES['thumbnail']);
                if ($uploadResult['success']) {
                    $thumbnailUpdate = ", thumbnail = ?";
                    $thumbnailValue = $uploadResult['path'];
                }
            } else if ($video_type === 'upload' && !empty($video_base64)) {
                // Auto-generate thumbnail from newly uploaded video
                $thumbnailResult = generateVideoThumbnail($video_base64, $video_format, $videoId);
                if ($thumbnailResult['success']) {
                    $thumbnailUpdate = ", thumbnail = ?";
                    $thumbnailValue = $thumbnailResult['path'];
                    error_log("Auto-generated thumbnail for video update: " . $thumbnailValue);
                }
            } else if ($video_type === 'youtube' && !empty($youtube_id)) {
                // Update YouTube thumbnail
                $thumbnailUpdate = ", thumbnail = ?";
                $thumbnailValue = "https://img.youtube.com/vi/$youtube_id/maxresdefault.jpg";
            }

            try {
                $pdo = getConnection();

                // Build query based on what's being updated
                $updateFields = "title = ?, description = ?, content = ?, youtube_url = ?, youtube_id = ?, video_type = ?, category = ?, tags = ?, duration = ?, status = ?, featured = ?, updated_at = NOW()";
                $updateValues = [$title, $description, $content, $youtube_url, $youtube_id, $video_type, $category, $tags, $duration, $status, $featured];

                // Add video file updates if provided
                if ($videoFileUpdate) {
                    $updateFields .= $videoFileUpdate;
                    $updateValues = array_merge($updateValues, $videoFileValues);
                }

                // Add thumbnail update if provided
                if ($thumbnailUpdate) {
                    $updateFields .= $thumbnailUpdate;
                    $updateValues[] = $thumbnailValue;
                }

                // Add video ID at the end
                $updateValues[] = $videoId;

                $stmt = $pdo->prepare("UPDATE videos SET $updateFields WHERE id = ?");
                $result = $stmt->execute($updateValues);

                if ($result) {
                    sendJsonResponse([
                        'success' => true,
                        'message' => 'Video berhasil diupdate'
                    ]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal mengupdate video']);
                }
            } catch (PDOException $e) {
                sendJsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
            }
            break;

        case 'delete_video':
            // Delete video
            $videoId = $_POST['id'] ?? $_GET['id'] ?? 0;

            if (empty($videoId)) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID is required']);
            }

            try {
                $pdo = getConnection();

                // Check if video exists
                $stmt = $pdo->prepare("SELECT id FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$video) {
                    sendJsonResponse(['success' => false, 'message' => 'Video not found']);
                }

                // Delete video from database (video_base64 data will be automatically deleted)
                $stmt = $pdo->prepare("DELETE FROM videos WHERE id = ?");
                $result = $stmt->execute([$videoId]);

                if ($result) {
                    sendJsonResponse([
                        'success' => true,
                        'message' => 'Video berhasil dihapus dari database'
                    ]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal menghapus video dari database']);
                }
            } catch (PDOException $e) {
                sendJsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
            }
            break;

        case 'toggle_video_like':
            $videoId = $_POST['id'] ?? $_GET['id'] ?? 0;

            if (empty($videoId)) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID is required']);
            }

            $pdo = getConnection();
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            // Check if this IP has already liked this video
            $stmt = $pdo->prepare("SELECT id FROM video_likes WHERE video_id = ? AND ip_address = ?");
            $stmt->execute([$videoId, $ipAddress]);
            $existingLike = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($existingLike) {
                // Unlike - remove the like
                $stmt = $pdo->prepare("DELETE FROM video_likes WHERE video_id = ? AND ip_address = ?");
                $stmt->execute([$videoId, $ipAddress]);

                // Decrement likes count in videos table
                $stmt = $pdo->prepare("UPDATE videos SET likes = GREATEST(0, likes - 1) WHERE id = ?");
                $stmt->execute([$videoId]);

                $liked = false;
                $message = 'Video like removed';
            } else {
                // Like - add the like
                $stmt = $pdo->prepare("INSERT INTO video_likes (video_id, ip_address, user_agent, liked_at) VALUES (?, ?, ?, NOW())");
                $stmt->execute([$videoId, $ipAddress, $userAgent]);

                // Increment likes count in videos table
                $stmt = $pdo->prepare("UPDATE videos SET likes = likes + 1 WHERE id = ?");
                $stmt->execute([$videoId]);

                $liked = true;
                $message = 'Video liked';
            }

            // Get updated likes count
            $stmt = $pdo->prepare("SELECT likes FROM videos WHERE id = ?");
            $stmt->execute([$videoId]);
            $updatedVideo = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($updatedVideo) {
                sendJsonResponse([
                    'success' => true,
                    'message' => $message,
                    'likes' => $updatedVideo['likes'],
                    'liked' => $liked
                ]);
            } else {
                sendJsonResponse(['success' => false, 'message' => 'Video not found']);
            }
            break;

        case 'get_video_liked_status':
            $pdo = getConnection();
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

            // Get all videos liked by this IP address
            $stmt = $pdo->prepare("SELECT video_id FROM video_likes WHERE ip_address = ?");
            $stmt->execute([$ipAddress]);
            $likedVideos = $stmt->fetchAll(PDO::FETCH_COLUMN);

            sendJsonResponse([
                'success' => true,
                'data' => $likedVideos,
                'ip_address' => $ipAddress
            ]);
            break;

        case 'increment_video_views':
            $videoId = $_POST['id'] ?? $_GET['id'] ?? 0;

            if (empty($videoId)) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID is required']);
            }

            $pdo = getConnection();
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            // Add view record
            $stmt = $pdo->prepare("INSERT INTO video_views (video_id, ip_address, user_agent, viewed_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$videoId, $ipAddress, $userAgent]);

            // Update views count in videos table
            $stmt = $pdo->prepare("UPDATE videos SET views = views + 1 WHERE id = ?");
            $result = $stmt->execute([$videoId]);

            sendJsonResponse(['success' => $result, 'message' => $result ? 'Video view counted' : 'Failed to count view']);
            break;

        case 'increment_video_share':
            $videoId = $_POST['id'] ?? $_GET['id'] ?? 0;

            if (empty($videoId)) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID is required']);
            }

            $pdo = getConnection();
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $platform = $_POST['platform'] ?? $_GET['platform'] ?? 'direct';

            // Add share record
            $stmt = $pdo->prepare("INSERT INTO video_shares (video_id, ip_address, user_agent, platform, shared_at) VALUES (?, ?, ?, ?, NOW())");
            $stmt->execute([$videoId, $ipAddress, $userAgent, $platform]);

            // Update shares count in videos table
            $stmt = $pdo->prepare("UPDATE videos SET shares = shares + 1 WHERE id = ?");
            $result = $stmt->execute([$videoId]);

            // Get updated shares count
            $stmt = $pdo->prepare("SELECT shares FROM videos WHERE id = ?");
            $stmt->execute([$videoId]);
            $updatedVideo = $stmt->fetch(PDO::FETCH_ASSOC);

            sendJsonResponse([
                'success' => $result,
                'message' => $result ? 'Video share counted' : 'Failed to count share',
                'shares' => $updatedVideo ? $updatedVideo['shares'] : 0
            ]);
            break;

        case 'get_video_comments':
            // Get video comments
            $videoId = $_GET['video_id'] ?? 0;
            if (empty($videoId)) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID is required']);
            }

            try {
                $pdo = getConnection();
                $stmt = $pdo->prepare("
                    SELECT id, video_id, user_name, comment_text, likes, created_at, updated_at
                    FROM video_comment
                    WHERE video_id = ? AND is_approved = 1
                    ORDER BY created_at DESC
                ");
                $stmt->execute([$videoId]);
                $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

                sendJsonResponse([
                    'success' => true,
                    'comments' => $comments,
                    'count' => count($comments)
                ]);
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'toggle_video_save':
            $videoId = intval($_POST['id'] ?? 0);
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

            if ($videoId > 0) {
                try {
                    $pdo = getConnection();

                    // Create video_saves table if it doesn't exist
                    $pdo->exec("CREATE TABLE IF NOT EXISTS `video_saves` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `video_id` int(11) NOT NULL,
                        `ip_address` varchar(45) NOT NULL,
                        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `unique_video_save` (`video_id`, `ip_address`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

                    // Check if already saved
                    $stmt = $pdo->prepare("SELECT id FROM video_saves WHERE video_id = ? AND ip_address = ?");
                    $stmt->execute([$videoId, $ipAddress]);
                    $existing = $stmt->fetch();

                    if ($existing) {
                        // Remove save
                        $stmt = $pdo->prepare("DELETE FROM video_saves WHERE video_id = ? AND ip_address = ?");
                        $stmt->execute([$videoId, $ipAddress]);

                        // Get updated count
                        $stmt = $pdo->prepare("SELECT COUNT(*) as save_count FROM video_saves WHERE video_id = ?");
                        $stmt->execute([$videoId]);
                        $result = $stmt->fetch();

                        sendJsonResponse([
                            'success' => true,
                            'saved' => false,
                            'action' => 'removed',
                            'saves' => intval($result['save_count'])
                        ]);
                    } else {
                        // Add save
                        $stmt = $pdo->prepare("INSERT INTO video_saves (video_id, ip_address) VALUES (?, ?)");
                        $stmt->execute([$videoId, $ipAddress]);

                        // Get updated count
                        $stmt = $pdo->prepare("SELECT COUNT(*) as save_count FROM video_saves WHERE video_id = ?");
                        $stmt->execute([$videoId]);
                        $result = $stmt->fetch();

                        sendJsonResponse([
                            'success' => true,
                            'saved' => true,
                            'action' => 'added',
                            'saves' => intval($result['save_count'])
                        ]);
                    }
                } catch (Exception $e) {
                    sendJsonResponse(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
                }
            } else {
                sendJsonResponse(['success' => false, 'error' => 'Invalid video ID']);
            }
            break;

        case 'add_video_comment':
            // Add video comment
            $videoId = $_POST['video_id'] ?? 0;
            $commentText = $_POST['comment_text'] ?? '';
            $userName = $_POST['user_name'] ?? 'Unknown';
            $userId = $_POST['user_id'] ?? null;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            // Handle empty user_id properly
            if (empty($userId) || $userId === '' || $userId === 'null') {
                $userId = null;
            } else {
                $userId = (int)$userId; // Convert to integer
            }

            if (empty($videoId) || empty($commentText)) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID dan komentar harus diisi']);
            }

            if (strlen($commentText) > 500) {
                sendJsonResponse(['success' => false, 'message' => 'Komentar maksimal 500 karakter']);
            }

            try {
                $pdo = getConnection();

                // Check if video exists
                $stmt = $pdo->prepare("SELECT id FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                if (!$stmt->fetch()) {
                    sendJsonResponse(['success' => false, 'message' => 'Video tidak ditemukan']);
                }

                // Insert comment with proper NULL handling
                $stmt = $pdo->prepare("
                    INSERT INTO video_comment (
                        video_id, user_id, user_name, comment_text,
                        ip_address, user_agent, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
                ");

                $result = $stmt->execute([
                    $videoId, $userId, $userName, $commentText,
                    $ipAddress, $userAgent
                ]);

                if ($result) {
                    $commentId = $pdo->lastInsertId();

                    // Update video comment count
                    $stmt = $pdo->prepare("
                        UPDATE videos
                        SET comments_count = (
                            SELECT COUNT(*) FROM video_comment
                            WHERE video_id = ? AND is_approved = 1
                        )
                        WHERE id = ?
                    ");
                    $stmt->execute([$videoId, $videoId]);

                    sendJsonResponse([
                        'success' => true,
                        'message' => 'Komentar berhasil ditambahkan',
                        'comment_id' => $commentId,
                        'user_info' => [
                            'user_id' => $userId,
                            'user_name' => $userName
                        ]
                    ]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal menambahkan komentar']);
                }
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'like_video_comment':
            // Like comment
            $commentId = $_POST['comment_id'] ?? 0;
            if (empty($commentId)) {
                sendJsonResponse(['success' => false, 'message' => 'Comment ID is required']);
            }

            try {
                $pdo = getConnection();
                $stmt = $pdo->prepare("UPDATE video_comment SET likes = likes + 1 WHERE id = ?");
                $result = $stmt->execute([$commentId]);

                if ($result) {
                    sendJsonResponse(['success' => true, 'message' => 'Komentar berhasil dilike']);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal like komentar']);
                }
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'delete_notification':
            // Delete single notification
            $notificationId = $_POST['id'] ?? 0;
            if (empty($notificationId)) {
                sendJsonResponse(['success' => false, 'message' => 'Notification ID is required']);
            }

            try {
                $pdo = getConnection();
                $stmt = $pdo->prepare("DELETE FROM notifications WHERE id = ?");
                $result = $stmt->execute([$notificationId]);

                if ($result) {
                    sendJsonResponse(['success' => true, 'message' => 'Notifikasi berhasil dihapus']);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal menghapus notifikasi']);
                }
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'clear_all_notifications':
            // Delete all notifications
            try {
                $pdo = getConnection();
                $stmt = $pdo->prepare("DELETE FROM notifications");
                $result = $stmt->execute();

                if ($result) {
                    sendJsonResponse(['success' => true, 'message' => 'Semua notifikasi berhasil dihapus']);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal menghapus semua notifikasi']);
                }
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'increment_video_view':
            // Increment video view count
            $videoId = $_POST['video_id'] ?? $_GET['video_id'] ?? 0;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            if (empty($videoId)) {
                sendJsonResponse(['success' => false, 'message' => 'Video ID is required']);
            }

            try {
                $pdo = getConnection();

                // Check if video exists
                $stmt = $pdo->prepare("SELECT id, views FROM videos WHERE id = ?");
                $stmt->execute([$videoId]);
                $video = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$video) {
                    sendJsonResponse(['success' => false, 'message' => 'Video tidak ditemukan']);
                }

                // Check if this IP has viewed this video recently (within 1 hour)
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as count
                    FROM video_views
                    WHERE video_id = ? AND ip_address = ?
                    AND viewed_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ");
                $stmt->execute([$videoId, $ipAddress]);
                $recentView = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($recentView['count'] == 0) {
                    // Record new view
                    $stmt = $pdo->prepare("
                        INSERT INTO video_views (video_id, ip_address, user_agent, viewed_at)
                        VALUES (?, ?, ?, NOW())
                    ");
                    $stmt->execute([$videoId, $ipAddress, $userAgent]);

                    // Increment video views count
                    $stmt = $pdo->prepare("UPDATE videos SET views = views + 1 WHERE id = ?");
                    $stmt->execute([$videoId]);

                    $newViews = $video['views'] + 1;

                    sendJsonResponse([
                        'success' => true,
                        'message' => 'View count updated',
                        'views' => $newViews
                    ]);
                } else {
                    sendJsonResponse([
                        'success' => true,
                        'message' => 'View already counted recently',
                        'views' => $video['views']
                    ]);
                }
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'get_pengaturan':
            $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
            $stmt->execute();
            $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pengaturan) {
                // Create default if not exists
                $stmt = $pdo->prepare("INSERT INTO pengaturan (nama_website, deskripsi_website, logo_file_path) VALUES (?, ?, ?)");
                $stmt->execute(['React News Portal', 'Portal berita terkini dan terpercaya', 'uploads/default-logo.png']);

                $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
                $stmt->execute();
                $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);
            }

            sendJsonResponse(['success' => true, 'data' => $pengaturan]);
            break;

        case 'update_pengaturan':
            try {
                $nama_website = $_POST['nama_website'] ?? '';
                $deskripsi_website = $_POST['deskripsi_website'] ?? '';
                $warna_sidebar = $_POST['warna_sidebar'] ?? '#2563EB';
                $warna_sidebar_header = $_POST['warna_sidebar_header'] ?? '#1D4ED8';
                $warna_primary = $_POST['warna_primary'] ?? '#3B82F6';
                $warna_secondary = $_POST['warna_secondary'] ?? '#10B981';
                $warna_accent = $_POST['warna_accent'] ?? '#F59E0B';

                // Debug log
                error_log("Update pengaturan - Data received: " . json_encode($_POST));
                error_log("Update pengaturan - Files received: " . json_encode($_FILES));

                // Handle logo upload
                $logoData = [];
                if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
                    $file = $_FILES['logo'];

                    error_log("Processing logo upload: " . $file['name']);

                    // Validate file
                    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
                    if (!in_array($file['type'], $allowedTypes)) {
                        sendJsonResponse(['success' => false, 'message' => 'Tipe file tidak diizinkan. Gunakan JPG, PNG, atau GIF']);
                        return;
                    }

                    if ($file['size'] > 2 * 1024 * 1024) {
                        sendJsonResponse(['success' => false, 'message' => 'Ukuran file terlalu besar (maksimal 2MB)']);
                        return;
                    }

                    // Upload file
                    $uploadsDir = __DIR__ . '/../../../../uploads';
                    if (!is_dir($uploadsDir)) {
                        if (!mkdir($uploadsDir, 0755, true)) {
                            sendJsonResponse(['success' => false, 'message' => 'Gagal membuat folder uploads']);
                            return;
                        }
                    }

                    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $filename = 'logo_' . time() . '.' . $extension;
                    $filepath = 'uploads/' . $filename;

                    if (move_uploaded_file($file['tmp_name'], $uploadsDir . '/' . $filename)) {
                        $logoData = [
                            'logo_filename' => $filename,
                            'logo_original_name' => $file['name'],
                            'logo_file_path' => $filepath,
                            'logo_file_size' => $file['size'],
                            'logo_mime_type' => $file['type'],
                            'logo_uploaded_at' => date('Y-m-d H:i:s')
                        ];
                        error_log("Logo uploaded successfully: " . $filepath);
                    } else {
                        sendJsonResponse(['success' => false, 'message' => 'Gagal mengupload file logo']);
                        return;
                    }
                } else if (isset($_FILES['logo']) && $_FILES['logo']['error'] !== UPLOAD_ERR_NO_FILE) {
                    // Handle upload errors
                    $errorMessages = [
                        UPLOAD_ERR_INI_SIZE => 'File terlalu besar (melebihi upload_max_filesize)',
                        UPLOAD_ERR_FORM_SIZE => 'File terlalu besar (melebihi MAX_FILE_SIZE)',
                        UPLOAD_ERR_PARTIAL => 'File hanya terupload sebagian',
                        UPLOAD_ERR_NO_TMP_DIR => 'Folder temporary tidak ditemukan',
                        UPLOAD_ERR_CANT_WRITE => 'Gagal menulis file ke disk',
                        UPLOAD_ERR_EXTENSION => 'Upload dihentikan oleh ekstensi'
                    ];

                    $errorCode = $_FILES['logo']['error'];
                    $errorMessage = $errorMessages[$errorCode] ?? 'Error upload tidak dikenal';
                    sendJsonResponse(['success' => false, 'message' => 'Error upload logo: ' . $errorMessage]);
                    return;
                }

            // Update pengaturan
            $sql = "UPDATE pengaturan SET
                    nama_website = ?,
                    deskripsi_website = ?,
                    warna_sidebar = ?,
                    warna_sidebar_header = ?,
                    warna_primary = ?,
                    warna_secondary = ?,
                    warna_accent = ?";

            $params = [$nama_website, $deskripsi_website, $warna_sidebar, $warna_sidebar_header, $warna_primary, $warna_secondary, $warna_accent];

            if (!empty($logoData)) {
                $sql .= ", logo_filename = ?, logo_original_name = ?, logo_file_path = ?, logo_file_size = ?, logo_mime_type = ?, logo_uploaded_at = ?";
                $params = array_merge($params, array_values($logoData));
            }

            $sql .= " WHERE id = 1";

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);

                if ($result) {
                    // Also update old settings table for compatibility
                    updateSetting('website_name', $nama_website);
                    if (!empty($logoData)) {
                        updateSetting('website_logo', $logoData['logo_file_path']);
                    }

                    sendJsonResponse(['success' => true, 'message' => 'Pengaturan berhasil disimpan']);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal menyimpan pengaturan']);
                }

            } catch (Exception $e) {
                error_log("Error update pengaturan: " . $e->getMessage());
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'admin_logout':
            // Admin logout - destroy session
            try {
                // Start session if not already started
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }

                // Destroy all session data
                $_SESSION = array();

                // Delete session cookie
                if (ini_get("session.use_cookies")) {
                    $params = session_get_cookie_params();
                    setcookie(session_name(), '', time() - 42000,
                        $params["path"], $params["domain"],
                        $params["secure"], $params["httponly"]
                    );
                }

                // Destroy the session
                session_destroy();

                // Clear any additional cookies
                setcookie('admin_logged_in', '', time() - 3600, '/');
                setcookie('admin_username', '', time() - 3600, '/');
                setcookie('admin_session', '', time() - 3600, '/');

                sendJsonResponse([
                    'success' => true,
                    'message' => 'Logout berhasil',
                    'redirect' => 'auth/login.php'
                ]);
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error during logout: ' . $e->getMessage()]);
            }
            break;

        case 'update_user':
            // Update user information
            $userId = $_POST['id'] ?? 0;
            $name = $_POST['name'] ?? '';
            $email = $_POST['email'] ?? '';
            $role = $_POST['role'] ?? 'user';
            $status = $_POST['status'] ?? 'active';

            if (empty($userId) || empty($name) || empty($email)) {
                sendJsonResponse(['success' => false, 'message' => 'Data tidak lengkap']);
            }

            try {
                $pdo = getConnection();

                // Check if user exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                if (!$stmt->fetch()) {
                    sendJsonResponse(['success' => false, 'message' => 'Pengguna tidak ditemukan']);
                }

                // Check email uniqueness (exclude current user)
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $userId]);
                if ($stmt->fetch()) {
                    sendJsonResponse(['success' => false, 'message' => 'Email sudah digunakan pengguna lain']);
                }

                // Update user
                $stmt = $pdo->prepare("
                    UPDATE users
                    SET name = ?, email = ?, role = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");

                $result = $stmt->execute([$name, $email, $role, $status, $userId]);

                if ($result) {
                    sendJsonResponse([
                        'success' => true,
                        'message' => 'Data pengguna berhasil diperbarui'
                    ]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal memperbarui data pengguna']);
                }
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'generate_jwt_token':
            // Generate new JWT token for user
            $userId = $_POST['user_id'] ?? 0;

            if (empty($userId)) {
                sendJsonResponse(['success' => false, 'message' => 'User ID diperlukan']);
            }

            try {
                $pdo = getConnection();

                // Check if user exists
                $stmt = $pdo->prepare("SELECT id, name, email FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$user) {
                    sendJsonResponse(['success' => false, 'message' => 'Pengguna tidak ditemukan']);
                }

                // Generate JWT token (simple implementation)
                $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
                $payload = json_encode([
                    'user_id' => $user['id'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'iat' => time(),
                    'exp' => time() + (24 * 60 * 60) // 24 hours
                ]);

                $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
                $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

                $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, 'your-secret-key', true);
                $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

                $jwt = $base64Header . "." . $base64Payload . "." . $base64Signature;

                // Save token to database (create table if needed)
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO user_tokens (user_id, token, expires_at, created_at)
                        VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR), NOW())
                        ON DUPLICATE KEY UPDATE
                        token = VALUES(token),
                        expires_at = VALUES(expires_at),
                        created_at = VALUES(created_at)
                    ");
                    $stmt->execute([$userId, $jwt]);
                } catch (PDOException $e) {
                    // If table doesn't exist, create it
                    if (strpos($e->getMessage(), "doesn't exist") !== false) {
                        $pdo->exec("
                            CREATE TABLE user_tokens (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                user_id INT NOT NULL,
                                token TEXT NOT NULL,
                                expires_at TIMESTAMP NOT NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                UNIQUE KEY unique_user (user_id),
                                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                            )
                        ");

                        // Try insert again
                        $stmt = $pdo->prepare("
                            INSERT INTO user_tokens (user_id, token, expires_at, created_at)
                            VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR), NOW())
                        ");
                        $stmt->execute([$userId, $jwt]);
                    } else {
                        throw $e;
                    }
                }

                sendJsonResponse([
                    'success' => true,
                    'message' => 'JWT Token berhasil dibuat',
                    'token' => $jwt
                ]);

            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'refresh_jwt_token':
            // Refresh existing JWT token
            $userId = $_POST['user_id'] ?? 0;

            if (empty($userId)) {
                sendJsonResponse(['success' => false, 'message' => 'User ID diperlukan']);
            }

            // Same logic as generate_jwt_token
            $_POST['action'] = 'generate_jwt_token';
            // Recursively call generate_jwt_token
            include __FILE__;
            break;

        case 'revoke_jwt_token':
            // Revoke JWT token
            $userId = $_POST['user_id'] ?? 0;

            if (empty($userId)) {
                sendJsonResponse(['success' => false, 'message' => 'User ID diperlukan']);
            }

            try {
                $pdo = getConnection();

                // Delete token from database
                $stmt = $pdo->prepare("DELETE FROM user_tokens WHERE user_id = ?");
                $result = $stmt->execute([$userId]);

                if ($result) {
                    sendJsonResponse([
                        'success' => true,
                        'message' => 'JWT Token berhasil di-revoke'
                    ]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal revoke token']);
                }
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'get_user_stats':
            // Get user statistics for add-user page
            try {
                $pdo = getConnection();

                $stats = [];

                // Total users
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM users");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total'] = $result['total'] ?? 0;

                // Active users
                $stmt = $pdo->prepare("SELECT COUNT(*) as active FROM users WHERE status = 'active'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['active'] = $result['active'] ?? 0;

                // Admin users
                $stmt = $pdo->prepare("SELECT COUNT(*) as admin FROM users WHERE role = 'admin'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['admin'] = $result['admin'] ?? 0;

                // Users with JWT tokens
                try {
                    $stmt = $pdo->prepare("
                        SELECT COUNT(DISTINCT u.id) as with_token
                        FROM users u
                        INNER JOIN user_tokens ut ON u.id = ut.user_id
                        WHERE ut.expires_at > NOW()
                    ");
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $stats['with_token'] = $result['with_token'] ?? 0;
                } catch (PDOException $e) {
                    // If user_tokens table doesn't exist
                    $stats['with_token'] = 0;
                }

                sendJsonResponse([
                    'success' => true,
                    'stats' => $stats
                ]);
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        case 'add_user':
            // Add new user
            $name = $_POST['name'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';
            $role = $_POST['role'] ?? 'user';
            $status = $_POST['status'] ?? 'active';
            $generateJWT = $_POST['generate_jwt'] ?? '0';

            if (empty($name) || empty($email) || empty($password)) {
                sendJsonResponse(['success' => false, 'message' => 'Nama, email, dan password harus diisi']);
            }

            if (strlen($password) < 6) {
                sendJsonResponse(['success' => false, 'message' => 'Password minimal 6 karakter']);
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                sendJsonResponse(['success' => false, 'message' => 'Format email tidak valid']);
            }

            try {
                $pdo = getConnection();

                // Check if email already exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    sendJsonResponse(['success' => false, 'message' => 'Email sudah terdaftar']);
                }

                // Hash password
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                // Insert user
                $stmt = $pdo->prepare("
                    INSERT INTO users (name, email, password, role, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ");

                $result = $stmt->execute([$name, $email, $hashedPassword, $role, $status]);

                if ($result) {
                    $userId = $pdo->lastInsertId();
                    $message = 'User berhasil ditambahkan';

                    // Generate JWT token if requested
                    if ($generateJWT === '1') {
                        try {
                            // Generate JWT token (same logic as generate_jwt_token)
                            $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
                            $payload = json_encode([
                                'user_id' => $userId,
                                'name' => $name,
                                'email' => $email,
                                'iat' => time(),
                                'exp' => time() + (24 * 60 * 60) // 24 hours
                            ]);

                            $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
                            $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

                            $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, 'your-secret-key', true);
                            $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

                            $jwt = $base64Header . "." . $base64Payload . "." . $base64Signature;

                            // Save token to database
                            try {
                                $stmt = $pdo->prepare("
                                    INSERT INTO user_tokens (user_id, token, expires_at, created_at)
                                    VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR), NOW())
                                ");
                                $stmt->execute([$userId, $jwt]);
                                $message .= ' dengan JWT Token';
                            } catch (PDOException $e) {
                                // If table doesn't exist, create it
                                if (strpos($e->getMessage(), "doesn't exist") !== false) {
                                    $pdo->exec("
                                        CREATE TABLE user_tokens (
                                            id INT AUTO_INCREMENT PRIMARY KEY,
                                            user_id INT NOT NULL,
                                            token TEXT NOT NULL,
                                            expires_at TIMESTAMP NOT NULL,
                                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                            UNIQUE KEY unique_user (user_id),
                                            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                                        )
                                    ");

                                    // Try insert again
                                    $stmt = $pdo->prepare("
                                        INSERT INTO user_tokens (user_id, token, expires_at, created_at)
                                        VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR), NOW())
                                    ");
                                    $stmt->execute([$userId, $jwt]);
                                    $message .= ' dengan JWT Token';
                                } else {
                                    error_log("JWT Token creation failed: " . $e->getMessage());
                                    $message .= ' (JWT Token gagal dibuat)';
                                }
                            }
                        } catch (Exception $jwtError) {
                            error_log("JWT Token generation error: " . $jwtError->getMessage());
                            $message .= ' (JWT Token gagal dibuat)';
                        }
                    }

                    sendJsonResponse([
                        'success' => true,
                        'message' => $message,
                        'user_id' => $userId
                    ]);
                } else {
                    sendJsonResponse(['success' => false, 'message' => 'Gagal menambahkan user']);
                }
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
            }
            break;

        default:
            sendJsonResponse(['success' => false, 'message' => 'Invalid action: ' . $action, 'received_action' => $action]);
            break;
    }
} catch (Exception $e) {
    sendJsonResponse(['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()]);
}

// Function to get saved news from database
function getSavedNews() {
    try {
        $pdo = getConnection();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name, s.saved_at
            FROM saved s
            JOIN posts p ON s.post_id = p.id
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 'published' AND s.ip_address = ?
            ORDER BY s.saved_at DESC
        ");
        $stmt->execute([$ipAddress]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Format the results to match frontend expectations
        $formattedResults = [];
        foreach ($results as $row) {
            $formattedResults[] = [
                'id' => (int)$row['id'],
                'title' => $row['title'],
                'description' => $row['content'], // Use content as description
                'content' => $row['content'],
                'image' => $row['image'],
                'category' => $row['category_name'] ?: 'Umum',
                'category_name' => $row['category_name'] ?: 'Umum',
                'category_id' => (int)$row['category_id'],
                'status' => $row['status'],
                'views' => (int)$row['views'],
                'date' => $row['created_at'],
                'created_at' => $row['created_at'],
                'updated_at' => $row['updated_at']
            ];
        }

        return $formattedResults;
    } catch (Exception $e) {
        error_log("Error in getSavedNews: " . $e->getMessage());
        return [];
    }
}

// Function to get news by ID
function getNewsById($id) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT
                p.id,
                p.title,
                p.slug,
                p.description,
                p.content,
                p.excerpt,
                p.image,
                p.image_base64,
                p.image_alt,
                p.category_id,
                p.status,
                p.featured,
                p.meta_title,
                p.meta_description,
                p.tags,
                p.share,
                p.views,
                p.likes,
                p.comments_count,
                p.reading_time,
                p.published_at,
                p.date,
                p.user_id,
                p.created_at,
                p.updated_at,
                c.name as category_name,
                c.color as category_color
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.id = ?
        ");
        $stmt->execute([$id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            return [
                'id' => (int)$result['id'],
                'title' => $result['title'],
                'slug' => $result['slug'],
                'description' => $result['description'] ?: ($result['excerpt'] ?: substr(strip_tags($result['content']), 0, 200) . '...'),
                'content' => $result['content'],
                'excerpt' => $result['excerpt'],
                'image' => $result['image'],
                'image_alt' => $result['image_alt'],
                'category' => $result['category_name'] ?: 'Umum',
                'category_name' => $result['category_name'] ?: 'Umum',
                'category_id' => (int)$result['category_id'],
                'category_color' => $result['category_color'] ?: '#3B82F6',
                'status' => $result['status'],
                'featured' => (bool)$result['featured'],
                'meta_title' => $result['meta_title'],
                'meta_description' => $result['meta_description'],
                'tags' => $result['tags'],
                'share' => (int)$result['share'],
                'views' => (int)$result['views'],
                'likes' => (int)$result['likes'],
                'comments_count' => (int)$result['comments_count'],
                'reading_time' => (int)$result['reading_time'],
                'published_at' => $result['published_at'],
                'date' => $result['date'] ?: $result['created_at'],
                'user_id' => (int)$result['user_id'],
                'created_at' => $result['created_at'],
                'updated_at' => $result['updated_at']
            ];
        }

        return false;
    } catch (Exception $e) {
        error_log("Error in getNewsById: " . $e->getMessage());
        return false;
    }
}

// Function to get all news with optional limit and category filter
function getNews($limit = null, $category = null) {
    try {
        $pdo = getConnection();

        $sql = "
            SELECT
                p.id,
                p.title,
                p.slug,
                p.description,
                p.content,
                p.excerpt,
                p.image,
                p.image_base64,
                p.image_alt,
                p.category_id,
                p.status,
                p.featured,
                p.meta_title,
                p.meta_description,
                p.tags,
                p.share,
                p.views,
                p.likes,
                p.comments_count,
                p.reading_time,
                p.published_at,
                p.date,
                p.user_id,
                p.created_at,
                p.updated_at,
                c.name as category_name,
                c.color as category_color
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE 1=1
        ";

        $params = [];

        // Add category filter if specified
        if ($category && $category !== 'all') {
            $sql .= " AND p.category_id = ?";
            $params[] = $category;
        }

        $sql .= " ORDER BY p.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }

        error_log("Executing SQL: " . $sql);
        error_log("With params: " . json_encode($params));

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        error_log("Raw results count: " . count($results));
        if (count($results) > 0) {
            error_log("First result: " . json_encode($results[0]));
        }

        // Format the results to match frontend expectations
        $formattedResults = [];
        foreach ($results as $row) {
            $formattedResults[] = [
                'id' => (int)$row['id'],
                'title' => $row['title'],
                'slug' => $row['slug'],
                'description' => $row['description'] ?: ($row['excerpt'] ?: substr(strip_tags($row['content']), 0, 200) . '...'),
                'content' => $row['content'],
                'excerpt' => $row['excerpt'],
                'image' => $row['image'],
                'image_alt' => $row['image_alt'],
                'category' => $row['category_name'] ?: 'Umum',
                'category_name' => $row['category_name'] ?: 'Umum',
                'category_id' => (int)$row['category_id'],
                'category_color' => $row['category_color'] ?: '#3B82F6',
                'status' => $row['status'],
                'featured' => (bool)$row['featured'],
                'meta_title' => $row['meta_title'],
                'meta_description' => $row['meta_description'],
                'tags' => $row['tags'],
                'share' => (int)$row['share'],
                'views' => (int)$row['views'],
                'likes' => (int)$row['likes'],
                'comments_count' => (int)$row['comments_count'],
                'reading_time' => (int)$row['reading_time'],
                'published_at' => $row['published_at'],
                'date' => $row['date'] ?: $row['created_at'],
                'user_id' => (int)$row['user_id'],
                'created_at' => $row['created_at'],
                'updated_at' => $row['updated_at']
            ];
        }

        return ['success' => true, 'data' => $formattedResults];
    } catch (Exception $e) {
        error_log("Error in getNews: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to fetch news', 'error' => $e->getMessage()];
    }
}

// Function to add news to saved/bookmark
function addSavedNews($postId) {
    try {
        $pdo = getConnection();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

        // Check if already saved by this IP
        $checkStmt = $pdo->prepare("SELECT id FROM saved WHERE post_id = ? AND ip_address = ?");
        $checkStmt->execute([$postId, $ipAddress]);
        if ($checkStmt->fetch()) {
            return true; // Already saved by this IP
        }

        // Add to saved with IP address
        $stmt = $pdo->prepare("INSERT INTO saved (post_id, ip_address, saved_at) VALUES (?, ?, NOW())");
        return $stmt->execute([$postId, $ipAddress]);
    } catch (Exception $e) {
        error_log("Error in addSavedNews: " . $e->getMessage());
        return false;
    }
}

// Function to remove news from saved/bookmark
function removeSavedNews($postId) {
    try {
        $pdo = getConnection();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

        // Delete only for this IP address
        $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ? AND ip_address = ?");
        return $stmt->execute([$postId, $ipAddress]);
    } catch (Exception $e) {
        error_log("Error in removeSavedNews: " . $e->getMessage());
        return false;
    }
}
?>
