<?php
// Test script to check video database and create sample data
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Video Database Test</h1>";

try {
    require_once __DIR__ . '/../admin/config.php';
    echo "<p>✅ Config loaded successfully</p>";
} catch (Exception $e) {
    echo "<p>❌ Config error: " . $e->getMessage() . "</p>";
    exit;
}

try {
    $pdo = getConnection();
    echo "<p>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p>❌ Database connection error: " . $e->getMessage() . "</p>";
    exit;
}

// Check if videos table exists
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'videos'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ Videos table exists</p>";
    } else {
        echo "<p>⚠️ Videos table does not exist - creating it...</p>";
        
        // Create videos table
        $pdo->exec("CREATE TABLE IF NOT EXISTS `videos` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL,
            `description` text,
            `content` longtext,
            `youtube_url` varchar(500) DEFAULT NULL,
            `youtube_id` varchar(20) DEFAULT NULL,
            `video_base64` LONGBLOB DEFAULT NULL,
            `video_type` ENUM('youtube', 'upload') DEFAULT 'youtube',
            `video_size` BIGINT DEFAULT NULL,
            `video_format` VARCHAR(10) DEFAULT NULL,
            `thumbnail` varchar(255) DEFAULT NULL,
            `category` varchar(100) DEFAULT 'Umum',
            `tags` text,
            `duration` varchar(10) DEFAULT '00:00',
            `status` enum('draft','published') DEFAULT 'published',
            `views` int(11) DEFAULT 0,
            `likes` int(11) DEFAULT 0,
            `shares` int(11) DEFAULT 0,
            `comments_count` int(11) DEFAULT 0,
            `featured` tinyint(1) DEFAULT 0,
            `created_by` int(11) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `category` (`category`),
            KEY `status` (`status`),
            KEY `created_at` (`created_at`),
            KEY `video_type` (`video_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        
        echo "<p>✅ Videos table created successfully</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error checking/creating videos table: " . $e->getMessage() . "</p>";
}

// Check existing videos
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos");
    $count = $stmt->fetch()['count'];
    echo "<p>📊 Total videos in database: $count</p>";
    
    if ($count == 0) {
        echo "<p>⚠️ No videos found - inserting sample data...</p>";
        
        // Insert sample videos
        $sampleVideos = [
            [
                'title' => 'Breaking News: Teknologi AI Terbaru',
                'description' => 'Perkembangan teknologi AI yang mengubah dunia digital saat ini',
                'content' => 'Video berita eksklusif tentang perkembangan teknologi AI terbaru yang akan mengubah cara kita bekerja dan berinteraksi dengan teknologi.',
                'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'youtube_id' => 'dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'category' => 'Teknologi',
                'tags' => 'teknologi,ai,digital,inovasi,berita',
                'duration' => '02:45'
            ],
            [
                'title' => 'Update Ekonomi Indonesia Terkini',
                'description' => 'Analisis mendalam kondisi ekonomi Indonesia dan proyeksi masa depan',
                'content' => 'Laporan komprehensif tentang perkembangan ekonomi Indonesia terbaru dengan analisis dari para ahli ekonomi.',
                'youtube_url' => 'https://www.youtube.com/watch?v=M7lc1UVf-VE',
                'youtube_id' => 'M7lc1UVf-VE',
                'video_type' => 'youtube',
                'category' => 'Ekonomi',
                'tags' => 'ekonomi,indonesia,bisnis,keuangan',
                'duration' => '03:20'
            ],
            [
                'title' => 'Highlight Olahraga Hari Ini',
                'description' => 'Momen-momen terbaik dari berbagai pertandingan olahraga',
                'content' => 'Kumpulan highlight dan momen spektakuler dari dunia olahraga yang tidak boleh dilewatkan.',
                'youtube_url' => 'https://www.youtube.com/watch?v=9bZkp7q19f0',
                'youtube_id' => '9bZkp7q19f0',
                'video_type' => 'youtube',
                'category' => 'Olahraga',
                'tags' => 'olahraga,sepakbola,highlight,kompetisi',
                'duration' => '04:15'
            ]
        ];

        $insertStmt = $pdo->prepare("
            INSERT INTO videos (
                title, description, content, youtube_url, youtube_id, 
                video_type, category, tags, duration, status, 
                views, likes, shares, comments_count, featured,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', ?, ?, ?, ?, 0, NOW(), NOW())
        ");

        foreach ($sampleVideos as $sample) {
            $insertStmt->execute([
                $sample['title'],
                $sample['description'],
                $sample['content'],
                $sample['youtube_url'],
                $sample['youtube_id'],
                $sample['video_type'],
                $sample['category'],
                $sample['tags'],
                $sample['duration'],
                rand(100, 1000), // views
                rand(10, 100),   // likes
                rand(5, 50),     // shares
                rand(0, 20)      // comments_count
            ]);
        }
        
        echo "<p>✅ Sample videos inserted successfully</p>";
        
        // Recheck count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos");
        $newCount = $stmt->fetch()['count'];
        echo "<p>📊 New total videos: $newCount</p>";
    }
    
    // Show existing videos
    $stmt = $pdo->query("SELECT id, title, youtube_url, youtube_id, video_type, status FROM videos ORDER BY created_at DESC LIMIT 5");
    $videos = $stmt->fetchAll();
    
    if (!empty($videos)) {
        echo "<h2>📋 Recent Videos:</h2>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>YouTube ID</th><th>Status</th><th>URL</th></tr>";
        foreach ($videos as $video) {
            echo "<tr>";
            echo "<td>" . $video['id'] . "</td>";
            echo "<td>" . htmlspecialchars($video['title']) . "</td>";
            echo "<td>" . $video['video_type'] . "</td>";
            echo "<td>" . $video['youtube_id'] . "</td>";
            echo "<td>" . $video['status'] . "</td>";
            echo "<td><a href='" . htmlspecialchars($video['youtube_url']) . "' target='_blank'>View</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error checking videos: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='video.php'>🎬 Go to Video Page</a></p>";
echo "<p><a href='http://localhost:3000/video'>🔗 Go to Video Page via React Route</a></p>";
?>
