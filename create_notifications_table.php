<?php
require_once 'frontend/src/pages/admin/connect.php';

try {
    $pdo = getConnection();
    
    // Check if notifications table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'notifications'");
    $stmt->execute();
    $exists = $stmt->fetch();
    
    if ($exists) {
        echo "✅ Table notifications already exists\n";
    } else {
        echo "Creating notifications table...\n";
        
        $sql = "CREATE TABLE notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type VARCHAR(50) DEFAULT 'info',
            category VARCHAR(50) DEFAULT 'general',
            status VARCHAR(20) DEFAULT 'unread',
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "✅ Notifications table created successfully\n";
        
        // Insert some sample notifications
        $stmt = $pdo->prepare("INSERT INTO notifications (title, message, type, category) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Selamat Datang', 'Sistem pengaturan berhasil diaktifkan', 'success', 'system']);
        $stmt->execute(['Info', 'Silakan konfigurasi pengaturan website Anda', 'info', 'settings']);
        
        echo "✅ Sample notifications inserted\n";
    }
    
    // Test notifications query
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "📊 Total notifications: " . $result['count'] . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
