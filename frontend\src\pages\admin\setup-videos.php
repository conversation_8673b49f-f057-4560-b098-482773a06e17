<?php
// Setup videos table and sample data for React video page
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    require_once __DIR__ . '/config.php';
    $pdo = getConnection();
    
    // Create videos table if it doesn't exist
    $createTableSQL = "CREATE TABLE IF NOT EXISTS `videos` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `description` text,
        `content` longtext,
        `youtube_url` varchar(500) DEFAULT NULL,
        `youtube_id` varchar(20) DEFAULT NULL,
        `video_base64` LONGBLOB DEFAULT NULL,
        `video_type` ENUM('youtube', 'upload') DEFAULT 'youtube',
        `video_size` BIGINT DEFAULT NULL,
        `video_format` VARCHAR(10) DEFAULT NULL,
        `thumbnail` varchar(255) DEFAULT NULL,
        `category` varchar(100) DEFAULT 'Umum',
        `tags` text,
        `duration` varchar(10) DEFAULT '00:00',
        `status` enum('draft','published') DEFAULT 'published',
        `views` int(11) DEFAULT 0,
        `likes` int(11) DEFAULT 0,
        `shares` int(11) DEFAULT 0,
        `comments_count` int(11) DEFAULT 0,
        `featured` tinyint(1) DEFAULT 0,
        `created_by` int(11) DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `category` (`category`),
        KEY `status` (`status`),
        KEY `created_at` (`created_at`),
        KEY `video_type` (`video_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($createTableSQL);
    
    // Check if videos exist
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM videos WHERE status = 'published'");
    $count = $stmt->fetch()['count'];
    
    if ($count == 0) {
        // Insert sample videos
        $sampleVideos = [
            [
                'title' => 'Breaking News: Teknologi AI Terbaru 2024',
                'description' => 'Perkembangan teknologi AI yang mengubah dunia digital saat ini. Simak ulasan lengkap tentang inovasi terbaru dalam bidang kecerdasan buatan.',
                'content' => 'Video berita eksklusif tentang perkembangan teknologi AI terbaru yang akan mengubah cara kita bekerja dan berinteraksi dengan teknologi.',
                'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'youtube_id' => 'dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'category' => 'Teknologi',
                'tags' => 'teknologi,ai,digital,inovasi,berita',
                'duration' => '02:45',
                'views' => 1250,
                'likes' => 89,
                'shares' => 45,
                'comments_count' => 12
            ],
            [
                'title' => 'Update Ekonomi Indonesia Terkini',
                'description' => 'Analisis mendalam kondisi ekonomi Indonesia dan proyeksi masa depan. Laporan komprehensif dari para ahli ekonomi terpercaya.',
                'content' => 'Laporan komprehensif tentang perkembangan ekonomi Indonesia terbaru dengan analisis dari para ahli ekonomi.',
                'youtube_url' => 'https://www.youtube.com/watch?v=M7lc1UVf-VE',
                'youtube_id' => 'M7lc1UVf-VE',
                'video_type' => 'youtube',
                'category' => 'Ekonomi',
                'tags' => 'ekonomi,indonesia,bisnis,keuangan',
                'duration' => '03:20',
                'views' => 890,
                'likes' => 67,
                'shares' => 32,
                'comments_count' => 8
            ],
            [
                'title' => 'Highlight Olahraga Hari Ini',
                'description' => 'Momen-momen terbaik dari berbagai pertandingan olahraga. Kumpulan highlight dan aksi spektakuler yang tidak boleh dilewatkan.',
                'content' => 'Kumpulan highlight dan momen spektakuler dari dunia olahraga yang tidak boleh dilewatkan.',
                'youtube_url' => 'https://www.youtube.com/watch?v=9bZkp7q19f0',
                'youtube_id' => '9bZkp7q19f0',
                'video_type' => 'youtube',
                'category' => 'Olahraga',
                'tags' => 'olahraga,sepakbola,highlight,kompetisi',
                'duration' => '04:15',
                'views' => 2340,
                'likes' => 156,
                'shares' => 78,
                'comments_count' => 23
            ],
            [
                'title' => 'Berita Politik Terkini',
                'description' => 'Update terbaru dari dunia politik Indonesia. Analisis mendalam tentang perkembangan politik nasional dan internasional.',
                'content' => 'Berita politik terkini dengan analisis mendalam dari para pakar politik.',
                'youtube_url' => 'https://www.youtube.com/watch?v=ScMzIvxBSi4',
                'youtube_id' => 'ScMzIvxBSi4',
                'video_type' => 'youtube',
                'category' => 'Politik',
                'tags' => 'politik,pemerintah,indonesia,berita',
                'duration' => '05:30',
                'views' => 1890,
                'likes' => 134,
                'shares' => 56,
                'comments_count' => 18
            ],
            [
                'title' => 'Tips Kesehatan dan Gaya Hidup',
                'description' => 'Tips praktis untuk menjaga kesehatan dan menjalani gaya hidup sehat. Panduan lengkap dari para ahli kesehatan.',
                'content' => 'Video edukasi tentang tips kesehatan dan gaya hidup sehat untuk kehidupan yang lebih baik.',
                'youtube_url' => 'https://www.youtube.com/watch?v=kJQP7kiw5Fk',
                'youtube_id' => 'kJQP7kiw5Fk',
                'video_type' => 'youtube',
                'category' => 'Kesehatan',
                'tags' => 'kesehatan,tips,gaya hidup,sehat',
                'duration' => '06:45',
                'views' => 3450,
                'likes' => 234,
                'shares' => 123,
                'comments_count' => 45
            ]
        ];

        $insertStmt = $pdo->prepare("
            INSERT INTO videos (
                title, description, content, youtube_url, youtube_id, 
                video_type, category, tags, duration, status, 
                views, likes, shares, comments_count, featured,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', ?, ?, ?, ?, 0, NOW(), NOW())
        ");

        $insertedCount = 0;
        foreach ($sampleVideos as $sample) {
            $result = $insertStmt->execute([
                $sample['title'],
                $sample['description'],
                $sample['content'],
                $sample['youtube_url'],
                $sample['youtube_id'],
                $sample['video_type'],
                $sample['category'],
                $sample['tags'],
                $sample['duration'],
                $sample['views'],
                $sample['likes'],
                $sample['shares'],
                $sample['comments_count']
            ]);
            
            if ($result) {
                $insertedCount++;
            }
        }
        
        echo json_encode([
            'success' => true,
            'message' => "Videos table created and $insertedCount sample videos inserted successfully",
            'inserted_count' => $insertedCount,
            'total_videos' => $insertedCount
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'message' => "Videos table already exists with $count published videos",
            'existing_count' => $count,
            'total_videos' => $count
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'error' => $e->getMessage()
    ]);
}
?>
