-- Create table pengaturan untuk sistem pengaturan website
CREATE TABLE IF NOT EXISTS pengaturan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_website VARCHAR(255) NOT NULL DEFAULT 'React News Portal',
    deskripsi_website TEXT NULL,
    logo_filename VA<PERSON>HA<PERSON>(255) NULL,
    logo_original_name VA<PERSON>HAR(255) NULL,
    logo_file_path VARCHAR(500) DEFAULT 'uploads/default-logo.png',
    logo_file_size INT DEFAULT 0,
    logo_mime_type VARCHAR(100) DEFAULT 'image/png',
    logo_uploaded_at TIMESTAMP NULL,

    -- <PERSON>na untuk admin sidebar
    warna_sidebar VARCHAR(7) DEFAULT '#2563EB',
    warna_sidebar_header VARCHAR(7) DEFAULT '#1D4ED8',

    -- <PERSON>na untuk landing page
    warna_primary VARCHAR(7) DEFAULT '#3B82F6',
    warna_secondary VARCHAR(7) DEFAULT '#10B981',
    warna_accent VARCHAR(7) DEFAULT '#F59E0B',

    -- Meta data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default pengaturan jika belum ada
INSERT INTO pengaturan (
    nama_website,
    deskripsi_website,
    logo_file_path,
    warna_sidebar,
    warna_sidebar_header,
    warna_primary,
    warna_secondary,
    warna_accent
)
SELECT
    'React News Portal',
    'Portal berita terkini dan terpercaya dengan teknologi React modern',
    'uploads/default-logo.png',
    '#2563EB',
    '#1D4ED8',
    '#3B82F6',
    '#10B981',
    '#F59E0B'
WHERE NOT EXISTS (SELECT 1 FROM pengaturan WHERE id = 1);

-- Sync data dari settings table lama jika ada
UPDATE pengaturan p 
SET nama_website = (
    SELECT setting_value FROM settings 
    WHERE setting_key = 'website_name' 
    LIMIT 1
) WHERE EXISTS (
    SELECT 1 FROM settings WHERE setting_key = 'website_name'
);

UPDATE pengaturan p 
SET logo_file_path = (
    SELECT setting_value FROM settings 
    WHERE setting_key = 'website_logo' 
    LIMIT 1
) WHERE EXISTS (
    SELECT 1 FROM settings WHERE setting_key = 'website_logo'
);
