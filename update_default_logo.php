<?php
require_once 'frontend/src/pages/admin/connect.php';

try {
    $pdo = getConnection();

    // Check table structure first
    $stmt = $pdo->prepare("DESCRIBE settings");
    $stmt->execute();
    $columns = $stmt->fetchAll();

    echo "📋 Settings table structure:\n";
    foreach ($columns as $column) {
        echo "  - " . $column['Field'] . " (" . $column['Type'] . ")\n";
    }

    // Update using key-value structure
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('website_logo', 'uploads/default-logo.png') ON DUPLICATE KEY UPDATE setting_value = 'uploads/default-logo.png'");
    $result = $stmt->execute();

    if ($result) {
        echo "✅ Default logo updated successfully in key-value structure\n";
    } else {
        echo "❌ Failed to update key-value structure\n";
    }

    // Check current logo value
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'website_logo'");
    $stmt->execute();
    $result = $stmt->fetch();

    if ($result) {
        echo "📋 Current logo value: " . $result['setting_value'] . "\n";
    } else {
        echo "📋 No logo setting found\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
