import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery
} from '@mui/material';
import FavoriteIcon from '@mui/icons-material/Favorite';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import ShareIcon from '@mui/icons-material/Share';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';

// Video data will be loaded from database via API

// Individual Video Item Component
function VideoItem({ video, isActive, onVideoClick }) {
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [likes, setLikes] = useState(video.stats.likes);
  const iframeRef = useRef(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Load liked status from database
  useEffect(() => {
    const loadLikedStatus = async () => {
      try {
        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');
        const data = await response.json();

        if (data.success && Array.isArray(data.data)) {
          setIsLiked(data.data.includes(video.id));
        }
      } catch (error) {
        console.error('Failed to load liked status:', error);
      }
    };

    loadLikedStatus();
  }, [video.id]);

  // Increment view count when video becomes active
  useEffect(() => {
    if (isActive) {
      const incrementView = async () => {
        try {
          const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${video.id}`
          });

          const data = await response.json();
          if (data.success) {
            console.log('✅ View count incremented for video:', video.id);
            video.stats.views = data.views || video.stats.views + 1;
          }
        } catch (error) {
          console.error('❌ Failed to increment view count:', error);
        }
      };

      // Delay to avoid rapid increments
      const timer = setTimeout(incrementView, 1000);
      return () => clearTimeout(timer);
    }
  }, [isActive, video.id, video.stats]);

  // TikTok aspect ratio: 9:16 (vertical) - Always vertical
  const videoHeight = isMobile ? '100vh' : '85vh';
  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio

  // YouTube iframe API for better control
  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {
    console.log('Generating YouTube embed URL for:', videoId, 'autoplay:', autoplay);

    if (!videoId) {
      console.warn('⚠️ No YouTube ID provided, cannot generate embed URL');
      return 'about:blank'; // Return blank page instead of null
    }

    if (videoId.length !== 11) {
      console.warn('⚠️ Invalid YouTube ID format (should be 11 characters):', videoId);
    }

    const params = new URLSearchParams({
      autoplay: autoplay ? '1' : '0',
      mute: '1',
      controls: '1', // Enable controls for debugging
      loop: '1',
      playlist: videoId,
      rel: '0',
      showinfo: '0',
      modestbranding: '1',
      iv_load_policy: '3',
      fs: '1', // Allow fullscreen
      disablekb: '0' // Allow keyboard for debugging
    });
    const url = `https://www.youtube.com/embed/${videoId}?${params.toString()}`;
    console.log('Generated YouTube URL:', url);
    return url;
  };

  const handleVideoClick = async () => {
    // For YouTube iframe, we'll just show interaction feedback
    console.log('Video clicked:', video.title);

    try {
      // Direct to localhost PHP API (more reliable)
      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${video.id}`
      });

      const data = await response.json();
      console.log('View increment response:', data);

      if (data.success) {
        console.log('✅ View count updated successfully');
        // Update local view count if needed
        video.stats.views = data.views || video.stats.views + 1;
      } else {
        console.error('❌ Failed to update view count:', data.message);
      }
    } catch (error) {
      console.error('❌ Failed to update view count:', error);
    }
  };

  const handleLike = async () => {
    const wasLiked = isLiked;

    // Optimistic update
    setIsLiked(!isLiked);
    setLikes(prev => isLiked ? prev - 1 : prev + 1);

    try {
      console.log('Toggling like for video:', video.id);

      // Direct to Laragon PHP API (more reliable)
      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${video.id}`
      });

      const data = await response.json();
      console.log('Like toggle response:', data);

      if (data.success) {
        // Update with server response
        setIsLiked(data.liked || !wasLiked);
        setLikes(data.likes || (wasLiked ? likes - 1 : likes + 1));
        console.log('✅ Like toggled successfully');
      } else {
        console.error('❌ Failed to toggle like on server:', data.message);
        // Revert on failure
        setIsLiked(wasLiked);
        setLikes(prev => wasLiked ? prev + 1 : prev - 1);
      }
    } catch (error) {
      console.error('❌ Failed to toggle video like:', error);
      // Revert on error
      setIsLiked(wasLiked);
      setLikes(prev => wasLiked ? prev + 1 : prev - 1);
    }
  };

  const handleSave = () => {
    setIsSaved(!isSaved);
  };

  const handleShare = async () => {
    console.log('Sharing video:', video.id);

    try {
      // Direct to Laragon PHP API (more reliable)
      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${video.id}`
      });

      const data = await response.json();
      console.log('Share response:', data);

      if (data.success) {
        // Update local share count
        video.stats.shares = data.shares || video.stats.shares + 1;
        console.log('✅ Share count updated successfully');

        // Show share options (you can customize this)
        if (navigator.share) {
          navigator.share({
            title: video.title,
            text: video.description,
            url: video.videoUrl
          });
        } else {
          // Fallback: copy to clipboard
          navigator.clipboard.writeText(video.videoUrl);
          alert('Link video telah disalin ke clipboard!');
        }
      } else {
        console.error('❌ Failed to update share count:', data.message);
      }
    } catch (error) {
      console.error('❌ Failed to update share count:', error);
    }
  };

  const handleComment = () => {
    console.log('Open comments for video:', video.id);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 hari lalu';
    if (diffDays < 7) return `${diffDays} hari lalu`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;
    return `${Math.ceil(diffDays / 30)} bulan lalu`;
  };

  console.log('🎬 Rendering VideoItem with video:', video.title, 'ID:', video.youtubeId, 'Active:', isActive);

  if (!video || !video.youtubeId) {
    console.error('❌ Invalid video data - missing video or YouTube ID:', video);
    return (
      <Box sx={{
        width: '100%',
        height: '100%',
        bgcolor: '#333',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        textAlign: 'center',
        p: 3
      }}>
        <Box sx={{ fontSize: '3rem', mb: 2 }}>📹</Box>
        <Box sx={{ fontSize: '1.2rem', mb: 1 }}>Video Tidak Tersedia</Box>
        <Box sx={{ fontSize: '0.9rem', opacity: 0.7 }}>
          {video?.title || 'Video tidak memiliki ID YouTube yang valid'}
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{
      position: 'relative',
      height: '100vh',
      width: '100%',
      bgcolor: '#000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden'
    }}>
      {/* Debug Info */}
      <Box sx={{
        position: 'absolute',
        top: 10,
        left: 10,
        color: 'lime',
        fontSize: 14,
        zIndex: 50,
        bgcolor: 'rgba(0,0,0,0.8)',
        p: 1,
        borderRadius: 1
      }}>
        {video.title} | Type: {video.videoType} | ID: {video.youtubeId || 'N/A'} | Active: {isActive ? 'YES' : 'NO'}
      </Box>

      {/* Video Container */}
      <Box sx={{
        width: isMobile ? '100%' : '400px',
        height: isMobile ? '100%' : '700px',
        bgcolor: '#333',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        {/* Render different video types */}
        {video.videoType === 'youtube' ? (
          /* YouTube Video Iframe */
          <iframe
            ref={iframeRef}
            src={getYouTubeEmbedUrl(video.youtubeId, isActive)}
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              cursor: 'pointer',
              backgroundColor: '#000'
            }}
            onClick={handleVideoClick}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title={video.title}
            onLoad={() => console.log('✅ YouTube iframe loaded successfully for:', video.youtubeId)}
            onError={() => console.error('❌ YouTube iframe failed to load for:', video.youtubeId)}
          />
        ) : (
          /* Uploaded Video Player */
          <video
            ref={iframeRef}
            src={video.videoUrl}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              backgroundColor: '#000'
            }}
            controls={isActive}
            autoPlay={isActive}
            muted={!isActive}
            loop
            onClick={handleVideoClick}
            onLoadedData={() => console.log('✅ Upload video loaded successfully for:', video.title)}
            onError={() => console.error('❌ Upload video failed to load for:', video.title)}
          />
        )}

        {/* Fallback content */}
        <Box sx={{
          position: 'absolute',
          bottom: 10,
          left: 10,
          right: 10,
          color: 'white',
          bgcolor: 'rgba(0,0,0,0.7)',
          p: 2,
          borderRadius: 1
        }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            {video.title}
          </Typography>
          <Typography variant="body2" sx={{ mb: 1 }}>
            {video.description}
          </Typography>
          <Typography variant="caption">
            Views: {video.stats.views} | Likes: {video.stats.likes}
          </Typography>
        </Box>
      </Box>

      {/* Video Overlay for Interaction */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        cursor: 'pointer'
      }}
      onClick={handleVideoClick}
      />

      {/* Right Side Actions (TikTok Style) */}
      <Box sx={{
        position: 'absolute',
        right: 12,
        bottom: 100,
        display: 'flex',
        flexDirection: 'column',
        gap: 3,
        zIndex: 3
      }}>
        {/* Profile Avatar */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Avatar
            src={video.author.avatar}
            sx={{ 
              width: 48, 
              height: 48, 
              border: '2px solid white',
              cursor: 'pointer'
            }}
          />
          {video.author.verified && (
            <Box sx={{
              bgcolor: '#1976d2',
              borderRadius: '50%',
              width: 20,
              height: 20,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mt: -1,
              border: '2px solid white'
            }}>
              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>
            </Box>
          )}
        </Box>

        {/* Like Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            onClick={handleLike}
            sx={{
              color: isLiked ? '#e91e63' : 'white',
              bgcolor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }
            }}
          >
            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>
            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}
          </Typography>
        </Box>

        {/* Comment Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            onClick={handleComment}
            sx={{
              color: 'white',
              bgcolor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }
            }}
          >
            <ChatBubbleOutlineIcon />
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>
            {video.stats.comments}
          </Typography>
        </Box>

        {/* Save Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            onClick={handleSave}
            sx={{
              color: isSaved ? '#ffc107' : 'white',
              bgcolor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }
            }}
          >
            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>
            Simpan
          </Typography>
        </Box>

        {/* Share Button */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <IconButton
            onClick={handleShare}
            sx={{
              color: 'white',
              bgcolor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }
            }}
          >
            <ShareIcon />
          </IconButton>
          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>
            {video.stats.shares}
          </Typography>
        </Box>
      </Box>

      {/* Bottom Info (TikTok Style) */}
      <Box sx={{
        position: 'absolute',
        bottom: 20,
        left: 16,
        right: 80,
        zIndex: 3
      }}>
        {/* Upload Date and Duration */}
        <Typography sx={{ 
          color: 'rgba(255, 255, 255, 0.8)', 
          fontSize: 12, 
          mb: 1 
        }}>
          {formatDate(video.uploadDate)} • {video.duration}
        </Typography>

        {/* Title */}
        <Typography sx={{ 
          color: 'white', 
          fontWeight: 'bold', 
          fontSize: 16, 
          mb: 1,
          lineHeight: 1.3
        }}>
          {video.title}
        </Typography>

        {/* Description */}
        <Typography sx={{ 
          color: 'rgba(255, 255, 255, 0.9)', 
          fontSize: 14, 
          mb: 2,
          lineHeight: 1.4,
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden'
        }}>
          {video.description}
        </Typography>

        {/* Tags */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {video.tags.map((tag, index) => (
            <Chip
              key={index}
              label={`#${tag}`}
              size="small"
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                fontSize: 11,
                height: 24,
                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }
              }}
            />
          ))}
        </Box>
      </Box>
    </Box>
  );
}

// Main VideoFeed Component
export default function VideoFeed() {
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);

  // Extract YouTube video ID from URL
  const extractYouTubeId = (url) => {
    if (!url) return null;
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null; // Return null if invalid
  };

  // Load videos from admin database
  useEffect(() => {
    const loadVideos = async () => {
      setLoading(true);
      try {
        console.log('🎬 Loading videos from database...');
        console.log('🔗 API URL: http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');

        // Direct to localhost PHP API (more reliable)
        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');

        console.log('📡 Response status:', response.status);
        console.log('📡 Response ok:', response.ok);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📊 API Response:', data);
        console.log('📊 Data type:', typeof data);
        console.log('📊 Data.success:', data.success);
        console.log('📊 Data.data:', data.data);
        console.log('📊 Is data.data array:', Array.isArray(data.data));
        console.log('📊 Data count:', data.count);

        if (data.success && Array.isArray(data.data)) {
          console.log('Raw data from API:', data.data);
          console.log('Number of videos in raw data:', data.data.length);

          // Convert database video data to component format
          const publishedVideos = data.data.filter(video => video.status === 'published');
          console.log('Published videos after filter:', publishedVideos);
          console.log('Number of published videos:', publishedVideos.length);

          const dbVideos = publishedVideos
            .map(video => {
              console.log('Processing video:', video);

              // Handle different video types
              const videoType = video.video_type || 'youtube';
              let youtubeId = null;
              let videoUrl = '';
              let thumbnail = '';

              if (videoType === 'youtube') {
                youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);
                videoUrl = video.youtube_url;
                thumbnail = youtubeId ? `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg` : '';

                // Skip YouTube videos without valid ID
                if (!youtubeId) {
                  console.warn('⚠️ Skipping YouTube video without valid ID:', video.title);
                  return null;
                }
              } else if (videoType === 'upload') {
                // Handle uploaded videos
                if (!video.video_path) {
                  console.warn('⚠️ Skipping upload video without file path:', video.title);
                  return null;
                }

                // Construct video URL for uploaded files
                const filename = video.video_path;
                videoUrl = `http://localhost/react-news/uploads/${filename}`;

                // Use a default thumbnail for uploaded videos
                thumbnail = 'data:image/svg+xml;base64,' + btoa(`
                  <svg xmlns="http://www.w3.org/2000/svg" width="320" height="180" viewBox="0 0 320 180" fill="#f3f4f6">
                    <rect width="320" height="180" fill="#e5e7eb"/>
                    <circle cx="160" cy="90" r="30" fill="#9ca3af"/>
                    <polygon points="150,75 150,105 175,90" fill="white"/>
                    <text x="160" y="130" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="12">Video Upload</text>
                  </svg>
                `);
              } else {
                console.warn('⚠️ Unknown video type:', videoType, video.title);
                return null;
              }

              console.log('Video type:', videoType, 'YouTube ID:', youtubeId, 'Video URL:', videoUrl);

              const processedVideo = {
                id: video.id,
                videoUrl: videoUrl,
                youtubeId: youtubeId,
                videoType: videoType,
                title: video.title || 'Untitled Video',
                description: video.description || video.content?.substring(0, 200) + '...' || 'No description available',
                tags: (video.tags && video.tags.trim()) ? video.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [video.category?.toLowerCase() || 'video'],
                author: {
                  name: video.created_by || 'News Reporter',
                  avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),
                  verified: true
                },
                stats: {
                  likes: parseInt(video.likes) || 0,
                  comments: parseInt(video.comments_count) || 0,
                  shares: parseInt(video.shares) || 0,
                  views: parseInt(video.views) || 0
                },
                uploadDate: video.created_at || new Date().toISOString(),
                duration: video.duration || '03:00',
                category: video.category || 'Video',
                thumbnail: thumbnail
              };
              console.log('Processed video result:', processedVideo);
              return processedVideo;
            })
            .filter(video => video !== null); // Remove null entries

          console.log('Processed videos:', dbVideos);

          // Use database videos if available, otherwise show empty state
          if (dbVideos.length > 0) {
            console.log('✅ Setting videos state with:', dbVideos.length, 'videos');
            console.log('📋 Videos to be set:', dbVideos);
            setVideos(dbVideos);
            setCurrentVideoIndex(0); // Reset to first video
            console.log('✅ Videos state updated successfully');

            // Force re-render by updating a dummy state
            setTimeout(() => {
              console.log('🔄 Force re-render check - videos length:', dbVideos.length);
            }, 100);
          } else {
            console.log('⚠️ No published videos found, showing empty state');
            setVideos([]);
          }
        } else {
          console.error('❌ API returned invalid data:', data);
          console.log('🔄 Trying fallback sample data...');

          // Fallback to sample data if API fails
          const fallbackVideos = [
            {
              id: 1,
              videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
              youtubeId: 'dQw4w9WgXcQ',
              videoType: 'youtube',
              title: 'Sample Video 1 - Breaking News',
              description: 'This is a sample video for testing the video player functionality. Lorem ipsum dolor sit amet consectetur adipiscing elit.',
              tags: ['news', 'breaking', 'sample'],
              author: {
                name: 'News Reporter',
                avatar: 'https://i.pravatar.cc/150?img=1',
                verified: true
              },
              stats: {
                likes: 125,
                comments: 23,
                shares: 45,
                views: 1250
              },
              uploadDate: new Date().toISOString(),
              duration: '02:45',
              category: 'News',
              thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
            },
            {
              id: 2,
              videoUrl: 'https://www.youtube.com/watch?v=M7lc1UVf-VE',
              youtubeId: 'M7lc1UVf-VE',
              videoType: 'youtube',
              title: 'Sample Video 2 - Technology Update',
              description: 'Latest technology updates and innovations in the digital world. Stay updated with the newest trends.',
              tags: ['technology', 'innovation', 'digital'],
              author: {
                name: 'Tech Reporter',
                avatar: 'https://i.pravatar.cc/150?img=2',
                verified: true
              },
              stats: {
                likes: 89,
                comments: 15,
                shares: 32,
                views: 890
              },
              uploadDate: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
              duration: '03:20',
              category: 'Technology',
              thumbnail: 'https://img.youtube.com/vi/M7lc1UVf-VE/maxresdefault.jpg'
            }
          ];

          console.log('✅ Using fallback sample videos:', fallbackVideos.length);
          setVideos(fallbackVideos);
        }
      } catch (error) {
        console.error('❌ Failed to load videos:', error);
        console.log('🔄 Using fallback sample data due to error...');

        // Fallback to sample data on error
        const fallbackVideos = [
          {
            id: 1,
            videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            youtubeId: 'dQw4w9WgXcQ',
            videoType: 'youtube',
            title: 'Sample Video - Error Fallback',
            description: 'This is a fallback video shown when the API fails to load. Please check your database connection.',
            tags: ['sample', 'fallback', 'error'],
            author: {
              name: 'System',
              avatar: 'https://i.pravatar.cc/150?img=1',
              verified: false
            },
            stats: {
              likes: 0,
              comments: 0,
              shares: 0,
              views: 0
            },
            uploadDate: new Date().toISOString(),
            duration: '02:45',
            category: 'System',
            thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
          }
        ];

        setVideos(fallbackVideos);
      } finally {
        setLoading(false);
      }
    };

    loadVideos();
  }, []);

  // Debug log for videos state changes
  useEffect(() => {
    console.log('🎬 Videos state changed:', videos.length, 'videos');
    console.log('📋 Current videos:', videos);
    console.log('🔄 Loading state:', loading);
    console.log('📍 Current video index:', currentVideoIndex);

    if (videos.length > 0) {
      console.log('✅ Videos are available for rendering');
      console.log('🎥 First video:', videos[0]);
    } else {
      console.log('⚠️ No videos available for rendering');
    }
  }, [videos, loading, currentVideoIndex]);

  // Handle scroll and touch to change videos (only if videos exist)
  useEffect(() => {
    if (videos.length === 0) return; // Don't add listeners if no videos

    let startY = 0;
    let isScrolling = false;

    const handleScroll = (e) => {
      if (isScrolling) return;
      isScrolling = true;

      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {
        setCurrentVideoIndex(prev => prev + 1);
      } else if (e.deltaY < 0 && currentVideoIndex > 0) {
        setCurrentVideoIndex(prev => prev - 1);
      }

      setTimeout(() => { isScrolling = false; }, 300);
    };

    const handleTouchStart = (e) => {
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e) => {
      if (isScrolling) return;
      const endY = e.changedTouches[0].clientY;
      const diffY = startY - endY;

      if (Math.abs(diffY) > 50) { // Minimum swipe distance
        isScrolling = true;

        if (diffY > 0 && currentVideoIndex < videos.length - 1) {
          // Swipe up - next video
          setCurrentVideoIndex(prev => prev + 1);
        } else if (diffY < 0 && currentVideoIndex > 0) {
          // Swipe down - previous video
          setCurrentVideoIndex(prev => prev - 1);
        }

        setTimeout(() => { isScrolling = false; }, 300);
      }
    };

    window.addEventListener('wheel', handleScroll);
    window.addEventListener('touchstart', handleTouchStart);
    window.addEventListener('touchend', handleTouchEnd);

    return () => {
      window.removeEventListener('wheel', handleScroll);
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchend', handleTouchEnd);
    };
  }, [currentVideoIndex, videos.length]);

  // Function to reload videos
  const reloadVideos = () => {
    setLoading(true);
    // Trigger the useEffect to reload videos
    window.location.reload();
  };

  // Empty state placeholder when no videos are available
  const EmptyVideoState = () => (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      padding: { xs: 3, sm: 4 },
      textAlign: 'center',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Background Animation */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)',
        animation: 'pulse 4s ease-in-out infinite alternate'
      }} />

      {/* Main Content */}
      <Box sx={{ position: 'relative', zIndex: 1 }}>
        {/* Video Icon with Animation */}
        <Box sx={{
          width: { xs: 100, sm: 120 },
          height: { xs: 100, sm: 120 },
          borderRadius: '50%',
          background: 'linear-gradient(45deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',
          backdropFilter: 'blur(10px)',
          border: '2px solid rgba(255,255,255,0.3)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 4,
          mx: 'auto',
          boxShadow: '0 8px 32px rgba(0,0,0,0.3)'
        }}>
          <PlayArrowIcon sx={{
            fontSize: { xs: 50, sm: 60 },
            opacity: 0.9,
            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
          }} />
        </Box>

        {/* Title */}
        <Typography
          variant="h4"
          sx={{
            mb: 2,
            fontWeight: 'bold',
            fontSize: { xs: '1.8rem', sm: '2.2rem' },
            background: 'linear-gradient(45deg, #fff 30%, #f0f0f0 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)'
          }}
        >
          Maaf, Saat Ini Belum Ada Video
        </Typography>

        {/* Description */}
        <Typography
          variant="body1"
          sx={{
            opacity: 0.9,
            maxWidth: { xs: 280, sm: 400 },
            mb: 5,
            lineHeight: 1.6,
            fontSize: { xs: '0.95rem', sm: '1.1rem' },
            textShadow: '0 1px 2px rgba(0,0,0,0.2)'
          }}
        >
          Video menarik akan segera hadir! Silakan kembali lagi nanti untuk menikmati konten video terbaru dari kami.
        </Typography>

        {/* Refresh Button */}
        <Box sx={{ mt: 2 }}>
          <IconButton
            onClick={reloadVideos}
            sx={{
              color: 'white',
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.3)',
                transform: 'scale(1.05)'
              },
              transition: 'all 0.3s ease',
              mb: 2,
              width: { xs: 56, sm: 64 },
              height: { xs: 56, sm: 64 }
            }}
          >
            <i className="fas fa-refresh" style={{ fontSize: 24 }} />
          </IconButton>

          <Typography
            variant="caption"
            sx={{
              opacity: 0.8,
              display: 'block',
              fontSize: { xs: '0.75rem', sm: '0.85rem' }
            }}
          >
            Ketuk untuk memuat ulang
          </Typography>
        </Box>

        {/* Decorative Elements */}
        <Box sx={{
          display: 'flex',
          gap: { xs: 3, sm: 4 },
          mt: 4,
          opacity: 0.6
        }}>
          {/* Like Button */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <IconButton
              disabled
              sx={{
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                '&.Mui-disabled': { opacity: 0.5 },
                width: { xs: 48, sm: 56 },
                height: { xs: 48, sm: 56 }
              }}
            >
              <FavoriteBorderIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />
            </IconButton>
            <Typography sx={{ color: 'white', fontSize: { xs: 10, sm: 12 }, mt: 0.5, opacity: 0.7 }}>
              0
            </Typography>
          </Box>

          {/* Comment Button */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <IconButton
              disabled
              sx={{
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                '&.Mui-disabled': { opacity: 0.5 },
                width: { xs: 48, sm: 56 },
                height: { xs: 48, sm: 56 }
              }}
            >
              <ChatBubbleOutlineIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />
            </IconButton>
            <Typography sx={{ color: 'white', fontSize: { xs: 10, sm: 12 }, mt: 0.5, opacity: 0.7 }}>
              0
            </Typography>
          </Box>

          {/* Share Button */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <IconButton
              disabled
              sx={{
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
                '&.Mui-disabled': { opacity: 0.5 },
                width: { xs: 48, sm: 56 },
                height: { xs: 48, sm: 56 }
              }}
            >
              <ShareIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />
            </IconButton>
            <Typography sx={{ color: 'white', fontSize: { xs: 10, sm: 12 }, mt: 0.5, opacity: 0.7 }}>
              0
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );

  // Loading state
  const LoadingVideoState = () => (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: '#000',
      color: 'white'
    }}>
      <Box sx={{
        width: 60,
        height: 60,
        borderRadius: '50%',
        border: '3px solid rgba(255,255,255,0.3)',
        borderTop: '3px solid white',
        animation: 'spin 1s linear infinite',
        mb: 2,
        '@keyframes spin': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' }
        }
      }} />

      <Typography variant="body1">
        Memuat Video...
      </Typography>
    </Box>
  );

  console.log('VideoFeed render - Loading:', loading, 'Videos count:', videos.length);

  return (
    <Box sx={{
      height: '100vh',
      overflow: 'hidden',
      position: 'relative',
      bgcolor: '#000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      {/* Debug Info */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        color: 'white',
        fontSize: 12,
        zIndex: 100,
        bgcolor: 'rgba(255,0,0,0.7)',
        p: 1,
        textAlign: 'center'
      }}>
        Loading: {loading ? 'Yes' : 'No'} | Videos: {videos.length} | Current: {currentVideoIndex}
      </Box>

      {loading ? (
        <LoadingVideoState />
      ) : videos && videos.length > 0 ? (
        <>
          <Box sx={{
            position: 'absolute',
            top: 50,
            left: 10,
            color: 'yellow',
            fontSize: 14,
            zIndex: 200,
            bgcolor: 'rgba(0,0,0,0.8)',
            p: 1
          }}>
            Rendering {videos.length} videos, current: {currentVideoIndex}
          </Box>

          {videos.map((video, index) => {
            console.log(`Rendering video ${index}:`, video.title, 'isActive:', index === currentVideoIndex);
            return (
              <Box
                key={video.id}
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transform: `translateY(${(index - currentVideoIndex) * 100}%)`,
                  transition: 'transform 0.3s ease-in-out',
                  zIndex: index === currentVideoIndex ? 10 : 1
                }}
              >
                <VideoItem
                  video={video}
                  isActive={index === currentVideoIndex}
                />
              </Box>
            );
          })}

          {/* Video Counter */}
          <Box sx={{
            position: 'absolute',
            top: 20,
            right: 20,
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            borderRadius: 2,
            px: 2,
            py: 1,
            zIndex: 4
          }}>
            <Typography sx={{ color: 'white', fontSize: 12 }}>
              {currentVideoIndex + 1} / {videos.length}
            </Typography>
          </Box>
        </>
      ) : (
        <EmptyVideoState />
      )}
    </Box>
  );
}
