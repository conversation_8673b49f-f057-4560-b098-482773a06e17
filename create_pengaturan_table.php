<?php
require_once 'frontend/src/pages/admin/connect.php';

try {
    $pdo = getConnection();
    
    // Read SQL file
    $sql = file_get_contents('create_pengaturan_table.sql');
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $pdo->exec($statement);
            echo '✅ Executed: ' . substr($statement, 0, 50) . '...' . PHP_EOL;
        }
    }
    
    echo '🎉 Table pengaturan created successfully!' . PHP_EOL;
    
    // Check if data exists
    $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo '📋 Default data found:' . PHP_EOL;
        echo '  - Nama Website: ' . $result['nama_website'] . PHP_EOL;
        echo '  - Logo Path: ' . $result['logo_file_path'] . PHP_EOL;
        echo '  - <PERSON>na Sidebar: ' . $result['warna_sidebar'] . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo '❌ Error: ' . $e->getMessage() . PHP_EOL;
}
?>
