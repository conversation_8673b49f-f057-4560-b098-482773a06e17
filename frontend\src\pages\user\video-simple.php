<?php
// Simple video page for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once __DIR__ . '/../admin/config.php';
    $pdo = getConnection();
    
    // Create videos table if it doesn't exist
    $pdo->exec("CREATE TABLE IF NOT EXISTS `videos` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `description` text,
        `content` longtext,
        `youtube_url` varchar(500) DEFAULT NULL,
        `youtube_id` varchar(20) DEFAULT NULL,
        `video_type` ENUM('youtube', 'upload') DEFAULT 'youtube',
        `category` varchar(100) DEFAULT 'Umum',
        `tags` text,
        `duration` varchar(10) DEFAULT '00:00',
        `status` enum('draft','published') DEFAULT 'published',
        `views` int(11) DEFAULT 0,
        `likes` int(11) DEFAULT 0,
        `shares` int(11) DEFAULT 0,
        `comments_count` int(11) DEFAULT 0,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
    
    // Get videos
    $stmt = $pdo->prepare("SELECT * FROM videos WHERE status = 'published' ORDER BY created_at DESC");
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // If no videos, create sample
    if (empty($videos)) {
        $pdo->exec("INSERT INTO videos (title, description, youtube_url, youtube_id, video_type, category, status) VALUES 
            ('Sample Video 1', 'Test video 1', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'youtube', 'Test', 'published'),
            ('Sample Video 2', 'Test video 2', 'https://www.youtube.com/watch?v=M7lc1UVf-VE', 'M7lc1UVf-VE', 'youtube', 'Test', 'published')");
        
        $stmt->execute();
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Video Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .video-container {
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            aspect-ratio: 16/9;
        }
        
        .video-scroll {
            scroll-snap-type: y mandatory;
            overflow-y: scroll;
            height: 100vh;
        }
        
        .video-item {
            scroll-snap-align: start;
            height: 100vh;
        }
    </style>
</head>
<body class="bg-black text-white">
    <div class="fixed top-4 left-4 z-50 bg-blue-600 text-white p-2 rounded text-sm">
        Videos loaded: <?= count($videos) ?>
    </div>
    
    <div class="video-scroll">
        <?php if (empty($videos)): ?>
            <div class="video-item flex items-center justify-center">
                <div class="text-center">
                    <h2 class="text-2xl mb-4">No Videos Found</h2>
                    <p>Database connection or video data issue</p>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($videos as $index => $video): ?>
                <div class="video-item flex items-center justify-center p-4">
                    <div class="max-w-4xl w-full">
                        <div class="video-container">
                            <?php if ($video['video_type'] === 'youtube' && !empty($video['youtube_id'])): ?>
                                <iframe
                                    src="https://www.youtube.com/embed/<?= htmlspecialchars($video['youtube_id']) ?>?autoplay=0&mute=0&controls=1&rel=0"
                                    class="w-full h-full"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen
                                    title="<?= htmlspecialchars($video['title']) ?>"
                                ></iframe>
                            <?php else: ?>
                                <div class="w-full h-full flex items-center justify-center bg-gray-800">
                                    <div class="text-center">
                                        <i class="fas fa-exclamation-triangle text-4xl mb-4 text-yellow-500"></i>
                                        <h3 class="text-lg mb-2">Video Not Available</h3>
                                        <p class="text-sm text-gray-300">Type: <?= $video['video_type'] ?></p>
                                        <p class="text-sm text-gray-300">ID: <?= $video['youtube_id'] ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <h2 class="text-xl font-bold mb-2"><?= htmlspecialchars($video['title']) ?></h2>
                            <p class="text-gray-300 mb-2"><?= htmlspecialchars($video['description']) ?></p>
                            <div class="text-sm text-gray-400">
                                Video <?= $index + 1 ?> of <?= count($videos) ?> | 
                                Views: <?= $video['views'] ?> | 
                                Likes: <?= $video['likes'] ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Bottom Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-700 z-40">
        <div class="flex justify-around items-center py-2">
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">Home</span>
            </a>
            <a href="#" class="flex flex-col items-center py-2 px-4 text-blue-500">
                <i class="fas fa-play text-xl mb-1"></i>
                <span class="text-xs">Video</span>
            </a>
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white">
                <i class="fas fa-search text-xl mb-1"></i>
                <span class="text-xs">Search</span>
            </a>
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white">
                <i class="fas fa-bookmark text-xl mb-1"></i>
                <span class="text-xs">Saved</span>
            </a>
        </div>
    </div>
    
    <script>
        console.log('🎬 Simple video page loaded');
        console.log('📊 Videos:', <?= json_encode($videos) ?>);
        
        // Check iframe loading
        document.addEventListener('DOMContentLoaded', function() {
            const iframes = document.querySelectorAll('iframe');
            console.log('📺 Found', iframes.length, 'iframes');
            
            iframes.forEach((iframe, index) => {
                console.log(`📺 Iframe ${index + 1}:`, iframe.src);
                
                iframe.onload = function() {
                    console.log(`✅ Iframe ${index + 1} loaded successfully`);
                };
                
                iframe.onerror = function() {
                    console.error(`❌ Iframe ${index + 1} failed to load`);
                };
            });
        });
    </script>
</body>
</html>
