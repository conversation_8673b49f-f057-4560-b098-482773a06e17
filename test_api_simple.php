<?php
// Simple API test
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once 'frontend/src/pages/admin/connect.php';
    
    $pdo = getConnection();
    
    // Test get_pengaturan directly
    $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
    $stmt->execute();
    $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$pengaturan) {
        // Create default if not exists
        $stmt = $pdo->prepare("INSERT INTO pengaturan (nama_website, deskripsi_website, logo_file_path) VALUES (?, ?, ?)");
        $stmt->execute(['React News Portal', 'Portal berita terkini dan terpercaya', 'uploads/default-logo.png']);
        
        $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
        $stmt->execute();
        $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    echo json_encode(['success' => true, 'data' => $pengaturan]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
