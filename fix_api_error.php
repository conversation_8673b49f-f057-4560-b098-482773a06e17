<?php
// Fix API Error - Debug and repair
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>Fix API Error</h2>";

// Test 1: Check if we can include the API file without errors
echo "<h3>1. Testing API File Include</h3>";

try {
    // Set up the environment like a real API call
    $_GET['action'] = 'get_pengaturan';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    // Capture output and errors
    ob_start();
    
    // Set custom error handler to catch all errors
    set_error_handler(function($severity, $message, $file, $line) {
        throw new ErrorException($message, 0, $severity, $file, $line);
    });
    
    // Include the API file
    include 'frontend/src/pages/admin/api.php';
    
    $output = ob_get_clean();
    
    echo "✅ API file included successfully<br>";
    echo "Output: <pre>" . htmlspecialchars($output) . "</pre>";
    
} catch (ParseError $e) {
    ob_end_clean();
    echo "❌ Parse Error in API file:<br>";
    echo "Message: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    
} catch (ErrorException $e) {
    ob_end_clean();
    echo "❌ Runtime Error in API file:<br>";
    echo "Message: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception in API file:<br>";
    echo "Message: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}

// Test 2: Check syntax of API file
echo "<h3>2. Checking API File Syntax</h3>";
$apiFile = 'frontend/src/pages/admin/api.php';
$syntaxCheck = shell_exec("php -l $apiFile 2>&1");
echo "Syntax check result:<br>";
echo "<pre>" . htmlspecialchars($syntaxCheck) . "</pre>";

// Test 3: Check if functions are defined after include
echo "<h3>3. Checking Function Definitions</h3>";
if (function_exists('getNotifications')) {
    echo "✅ getNotifications function exists<br>";
} else {
    echo "❌ getNotifications function missing<br>";
}

if (function_exists('getUnreadNotificationCount')) {
    echo "✅ getUnreadNotificationCount function exists<br>";
} else {
    echo "❌ getUnreadNotificationCount function missing<br>";
}

if (function_exists('sendJsonResponse')) {
    echo "✅ sendJsonResponse function exists<br>";
} else {
    echo "❌ sendJsonResponse function missing<br>";
}

// Test 4: Manual test of get_pengaturan logic
echo "<h3>4. Manual Test of get_pengaturan Logic</h3>";
try {
    require_once 'frontend/src/pages/admin/connect.php';
    $pdo = getConnection();
    
    $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
    $stmt->execute();
    $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($pengaturan) {
        echo "✅ Data retrieved successfully<br>";
        echo "Data: <pre>" . print_r($pengaturan, true) . "</pre>";
        
        // Test JSON encoding
        $json = json_encode(['success' => true, 'data' => $pengaturan]);
        if ($json === false) {
            echo "❌ JSON encoding failed: " . json_last_error_msg() . "<br>";
        } else {
            echo "✅ JSON encoding successful<br>";
            echo "JSON: <pre>" . htmlspecialchars($json) . "</pre>";
        }
    } else {
        echo "❌ No data found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}
?>
