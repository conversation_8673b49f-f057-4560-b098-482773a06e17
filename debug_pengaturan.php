<?php
header('Content-Type: application/json');

try {
    require_once 'frontend/src/pages/admin/connect.php';
    
    $pdo = getConnection();
    
    // Check if table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'pengaturan'");
    $stmt->execute();
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo json_encode([
            'success' => false, 
            'message' => 'Table pengaturan does not exist',
            'debug' => 'Please run create_pengaturan_table.sql'
        ]);
        exit;
    }
    
    // Get data
    $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
    $stmt->execute();
    $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$pengaturan) {
        // Insert default data
        $stmt = $pdo->prepare("INSERT INTO pengaturan (nama_website, deskripsi_website, logo_file_path) VALUES (?, ?, ?)");
        $stmt->execute(['React News Portal', 'Portal berita terkini dan terpercaya', 'uploads/default-logo.png']);
        
        // Get the inserted data
        $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
        $stmt->execute();
        $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    echo json_encode([
        'success' => true,
        'data' => $pengaturan,
        'debug' => 'Data loaded successfully'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'debug' => 'Exception occurred'
    ]);
}
?>
