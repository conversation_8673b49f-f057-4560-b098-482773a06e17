{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\LandingPage.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Drawer from '@mui/material/Drawer';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Typography from '@mui/material/Typography';\nimport Box from '@mui/material/Box';\nimport Button from '@mui/material/Button';\nimport Chip from '@mui/material/Chip';\nimport Stack from '@mui/material/Stack';\nimport Card from '@mui/material/Card';\nimport CardContent from '@mui/material/CardContent';\nimport CardMedia from '@mui/material/CardMedia';\nimport Avatar from '@mui/material/Avatar';\nimport Divider from '@mui/material/Divider';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport Footer from './components/Footer';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme, createTheme, ThemeProvider } from '@mui/material/styles';\nimport ShareIcon from '@mui/icons-material/Share';\nimport BookmarkAddIcon from '@mui/icons-material/BookmarkAdd';\nimport BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport IconButton from '@mui/material/IconButton';\nimport Tooltip from '@mui/material/Tooltip';\nimport CloseIcon from '@mui/icons-material/Close';\nimport TextField from '@mui/material/TextField';\nimport SearchIcon from '@mui/icons-material/Search';\nimport NavigateNextIcon from '@mui/icons-material/NavigateNext';\nimport NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport LoginIcon from '@mui/icons-material/Login';\nimport PersonAddIcon from '@mui/icons-material/PersonAdd';\nimport LogoutIcon from '@mui/icons-material/Logout';\nimport AccountCircleIcon from '@mui/icons-material/AccountCircle';\n\n// Helper function to get correct image URL - Use standardized uploads folder\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getImageUrl = imagePath => {\n  if (!imagePath) {\n    return 'https://source.unsplash.com/300x200/?news'; // Fallback to Unsplash\n  }\n  console.log('LandingPage - Processing image path:', imagePath);\n\n  // If it's already a full URL, return as is\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n    console.log('LandingPage - Using full URL:', imagePath);\n    return imagePath;\n  }\n\n  // Extract filename from any path format\n  let filename = '';\n  if (imagePath.startsWith('/react-news/uploads/')) {\n    filename = imagePath.replace('/react-news/uploads/', '');\n  } else if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n    filename = imagePath.replace('/react-news/frontend/uploads/', '');\n  } else if (imagePath.startsWith('/uploads/')) {\n    filename = imagePath.replace('/uploads/', '');\n  } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {\n    filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');\n  } else if (imagePath.startsWith('uploads/')) {\n    filename = imagePath.replace('uploads/', '');\n  } else if (imagePath.startsWith('assets/news/')) {\n    filename = imagePath.replace('assets/news/', '');\n  } else if (!imagePath.includes('/')) {\n    // Just filename\n    filename = imagePath;\n  } else {\n    // Extract filename from any other path\n    filename = imagePath.split('/').pop();\n  }\n\n  // Try multiple possible paths for image location\n  const possiblePaths = [`http://localhost/uploads/${filename}`,\n  // Root uploads (standardized)\n  `http://localhost/react-news/uploads/${filename}`,\n  // React-news uploads\n  `http://localhost/react-news/frontend/uploads/${filename}` // Frontend uploads\n  ];\n  console.log('LandingPage - Trying paths for filename:', filename);\n  console.log('LandingPage - Possible paths:', possiblePaths);\n\n  // For now, return the first path (root uploads) as the primary choice\n  const finalUrl = possiblePaths[0];\n  console.log('LandingPage - Final URL:', finalUrl);\n  return finalUrl;\n};\n\n// Popular news will be fetched from database\n\nconst categories = ['Semua', 'Umum', 'Teknologi', 'Bisnis', 'Olahraga', 'Hiburan', 'Politik', 'Kesehatan'];\nfunction Toast({\n  message,\n  isVisible,\n  onClose\n}) {\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'fixed',\n      top: {\n        xs: 80,\n        md: 100\n      },\n      right: {\n        xs: 16,\n        md: 24\n      },\n      zIndex: 9999,\n      bgcolor: '#10b981',\n      color: 'white',\n      px: 3,\n      py: 2,\n      borderRadius: 2,\n      boxShadow: 4,\n      display: 'flex',\n      alignItems: 'center',\n      gap: 1,\n      transform: 'translateX(0)',\n      transition: 'transform 0.3s ease-in-out',\n      maxWidth: {\n        xs: 'calc(100vw - 32px)',\n        md: 400\n      },\n      minWidth: {\n        xs: 280,\n        md: 320\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n      sx: {\n        fontSize: 20\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        fontWeight: 600,\n        fontSize: {\n          xs: 13,\n          md: 14\n        }\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n}\n_c = Toast;\nfunction PopularTags({\n  selectedCategory,\n  setSelectedCategory,\n  categoriesData\n}) {\n  const popularTags = categoriesData.length > 0 ? categoriesData.map(cat => ({\n    name: cat.name,\n    count: cat.post_count || 0,\n    color: cat.color || '#3B82F6'\n  })) : [{\n    name: 'Umum',\n    count: 0,\n    color: '#6B7280'\n  }, {\n    name: 'Teknologi',\n    count: 0,\n    color: '#3B82F6'\n  }, {\n    name: 'Bisnis',\n    count: 0,\n    color: '#10B981'\n  }, {\n    name: 'Olahraga',\n    count: 0,\n    color: '#F59E0B'\n  }, {\n    name: 'Hiburan',\n    count: 0,\n    color: '#EF4444'\n  }, {\n    name: 'Politik',\n    count: 0,\n    color: '#8B5CF6'\n  }, {\n    name: 'Kesehatan',\n    count: 0,\n    color: '#06B6D4'\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      bgcolor: '#fff',\n      borderRadius: 3,\n      p: 3,\n      mb: 3,\n      boxShadow: 2,\n      border: '1px solid',\n      borderColor: 'grey.200'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 4,\n          height: 20,\n          bgcolor: 'primary.main',\n          borderRadius: 2,\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 700,\n          color: 'grey.800',\n          fontSize: {\n            xs: 16,\n            md: 18\n          }\n        },\n        children: \"Tag Terpopuler\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: 1.5,\n        justifyContent: {\n          xs: 'center',\n          md: 'flex-start'\n        }\n      },\n      children: popularTags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n        label: `${tag.name} (${tag.count})`,\n        onClick: () => setSelectedCategory(tag.name),\n        sx: {\n          bgcolor: selectedCategory === tag.name ? tag.color : 'grey.100',\n          color: selectedCategory === tag.name ? 'white' : 'grey.700',\n          fontWeight: 600,\n          fontSize: {\n            xs: 12,\n            md: 13\n          },\n          px: 2,\n          py: 1,\n          cursor: 'pointer',\n          transition: 'all 0.3s ease',\n          '&:hover': {\n            bgcolor: selectedCategory === tag.name ? tag.color : 'grey.200',\n            transform: 'translateY(-2px)',\n            boxShadow: 2\n          },\n          border: selectedCategory === tag.name ? 'none' : '1px solid',\n          borderColor: 'grey.300'\n        }\n      }, tag.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n}\n_c2 = PopularTags;\nfunction LatestPosts({\n  newsData\n}) {\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Get latest 4 posts\n  const latestPosts = newsData.slice(0, 4);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      bgcolor: '#fff',\n      borderRadius: 3,\n      p: 3,\n      mb: 3,\n      boxShadow: 2,\n      border: '1px solid',\n      borderColor: 'grey.200'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 4,\n          height: 20,\n          bgcolor: 'primary.main',\n          borderRadius: 2,\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 700,\n          color: 'grey.800',\n          fontSize: {\n            xs: 16,\n            md: 18\n          }\n        },\n        children: \"Latest Posts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n      spacing: 2,\n      children: latestPosts.map((post, index) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2,\n          p: 2,\n          borderRadius: 2,\n          transition: 'all 0.3s ease',\n          cursor: 'pointer',\n          '&:hover': {\n            bgcolor: 'grey.50',\n            transform: 'translateX(4px)'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'grey.900',\n              lineHeight: 1.3,\n              mb: 0.5,\n              fontSize: {\n                xs: 13,\n                md: 14\n              },\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              display: '-webkit-box',\n              WebkitLineClamp: 2,\n              WebkitBoxOrient: 'vertical'\n            },\n            children: post.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'primary.main',\n                fontWeight: 600,\n                fontSize: {\n                  xs: 10,\n                  md: 11\n                }\n              },\n              children: post.category_name || post.category || 'Umum'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'grey.500',\n                fontSize: {\n                  xs: 10,\n                  md: 11\n                }\n              },\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'grey.600',\n                fontSize: {\n                  xs: 10,\n                  md: 11\n                }\n              },\n              children: formatTime(post.created_at || post.date)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: {\n              xs: 60,\n              md: 80\n            },\n            height: {\n              xs: 60,\n              md: 80\n            },\n            borderRadius: 2,\n            overflow: 'hidden',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            component: \"img\",\n            src: getImageUrl(post.image),\n            alt: post.title,\n            sx: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'cover'\n            },\n            onError: e => {\n              e.target.src = 'https://source.unsplash.com/100x100/?news';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, post.id || index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n}\n_c3 = LatestPosts;\nfunction BreakingNewsSlider({\n  newsData,\n  kostum\n}) {\n  _s();\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [autoPlay, setAutoPlay] = useState(true);\n\n  // Add CSS animation for pulse effect\n  React.useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes pulse {\n        0% { opacity: 1; }\n        50% { opacity: 0.5; }\n        100% { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(style);\n    return () => document.head.removeChild(style);\n  }, []);\n\n  // Get latest 5 news for slider\n  const sliderNews = newsData.slice(0, 5);\n  React.useEffect(() => {\n    if (!autoPlay || sliderNews.length <= 1) return;\n    const interval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % sliderNews.length);\n    }, 5000); // Change slide every 5 seconds\n\n    return () => clearInterval(interval);\n  }, [autoPlay, sliderNews.length]);\n  const handleNext = () => {\n    setCurrentSlide(prev => (prev + 1) % sliderNews.length);\n    setAutoPlay(false);\n  };\n  const handlePrev = () => {\n    setCurrentSlide(prev => (prev - 1 + sliderNews.length) % sliderNews.length);\n    setAutoPlay(false);\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (sliderNews.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        width: '100%',\n        height: {\n          xs: 200,\n          md: 400\n        },\n        borderRadius: 3,\n        overflow: 'hidden',\n        mb: 2,\n        boxShadow: 3,\n        bgcolor: 'grey.100',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          color: 'grey.600'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1,\n            fontWeight: 600\n          },\n          children: \"Belum ada berita\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'grey.500'\n          },\n          children: \"Berita akan muncul di sini\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      width: '100%',\n      height: {\n        xs: 200,\n        md: 400\n      },\n      // 1920x1080 aspect ratio for desktop\n      borderRadius: 3,\n      overflow: 'hidden',\n      mb: 2,\n      boxShadow: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 16,\n        left: 16,\n        zIndex: 3,\n        bgcolor: 'error.main',\n        color: 'white',\n        px: 2,\n        py: 0.5,\n        borderRadius: 2,\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 8,\n          height: 8,\n          borderRadius: '50%',\n          bgcolor: 'white',\n          animation: 'pulse 1.5s infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          fontWeight: 700,\n          fontSize: 12\n        },\n        children: \"BREAKING NEWS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        width: '100%',\n        height: '100%'\n      },\n      children: sliderNews.map((news, index) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          opacity: index === currentSlide ? 1 : 0,\n          transition: 'opacity 0.5s ease-in-out',\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            background: `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6)), url(${getImageUrl(news.image)})`,\n            backgroundSize: 'cover',\n            backgroundPosition: 'center',\n            display: 'flex',\n            alignItems: 'flex-end',\n            p: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              color: 'white',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 700,\n                mb: 1,\n                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                fontSize: {\n                  xs: 16,\n                  sm: 18,\n                  md: 24\n                }\n              },\n              children: news.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'grey.300',\n                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                fontSize: {\n                  xs: 12,\n                  md: 14\n                }\n              },\n              children: [formatTime(news.date), \" \\u2022 \", news.category]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this)\n      }, news.id || index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), sliderNews.length > 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handlePrev,\n        sx: {\n          position: 'absolute',\n          left: 8,\n          top: '50%',\n          transform: 'translateY(-50%)',\n          color: 'white',\n          '&:hover': {\n            bgcolor: 'rgba(0,0,0,0.1)'\n          },\n          zIndex: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(NavigateBeforeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleNext,\n        sx: {\n          position: 'absolute',\n          right: 8,\n          top: '50%',\n          transform: 'translateY(-50%)',\n          color: 'white',\n          '&:hover': {\n            bgcolor: 'rgba(0,0,0,0.1)'\n          },\n          zIndex: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(NavigateNextIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), sliderNews.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 16,\n        left: '50%',\n        transform: 'translateX(-50%)',\n        display: 'flex',\n        gap: 1,\n        zIndex: 2\n      },\n      children: sliderNews.map((_, index) => /*#__PURE__*/_jsxDEV(Box, {\n        onClick: () => {\n          setCurrentSlide(index);\n          setAutoPlay(false);\n        },\n        sx: {\n          width: 8,\n          height: 8,\n          borderRadius: '50%',\n          bgcolor: index === currentSlide ? 'white' : 'rgba(255,255,255,0.5)',\n          cursor: 'pointer',\n          transition: 'all 0.3s ease'\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 518,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 396,\n    columnNumber: 5\n  }, this);\n}\n_s(BreakingNewsSlider, \"Y5JnQJuRbmpV4DtpHS9ZHFEhDk4=\");\n_c4 = BreakingNewsSlider;\nfunction NewsCard({\n  news,\n  expanded,\n  onExpand,\n  formatDate,\n  truncateText,\n  variant,\n  onShare,\n  onBookmark,\n  isBookmarked,\n  onNewsClick,\n  isAuthenticated,\n  onLike,\n  isLiked\n}) {\n  const aspectRatio = variant === 'desktop' ? '56.25%' : '100%';\n  const handleNewsClick = e => {\n    // Prevent navigation if clicking on buttons/icons\n    if (e.target.closest('button') || e.target.closest('[role=\"button\"]')) {\n      return;\n    }\n    onNewsClick && onNewsClick(news.id);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%',\n      position: 'relative',\n      minWidth: {\n        xs: '90vw',\n        sm: 320,\n        md: 340\n      },\n      maxWidth: {\n        xs: '100vw',\n        sm: 400,\n        md: 420\n      },\n      mx: 'auto',\n      borderRadius: 4,\n      boxShadow: 3,\n      p: 1,\n      bgcolor: '#fff',\n      transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',\n      '&:hover': {\n        transform: 'translateY(-4px)',\n        boxShadow: 6\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      onClick: handleNewsClick,\n      sx: {\n        position: 'relative',\n        width: '100%',\n        paddingTop: aspectRatio,\n        borderRadius: 3,\n        overflow: 'hidden',\n        cursor: 'pointer'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardMedia, {\n        component: \"img\",\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover'\n        },\n        image: getImageUrl(news.image),\n        alt: news.title,\n        onError: e => {\n          e.target.src = variant === 'desktop' ? 'https://source.unsplash.com/1920x1080/?news' : 'https://source.unsplash.com/900x900/?news';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 20,\n        bottom: 20,\n        zIndex: 2,\n        mt: 2,\n        display: 'flex',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: isLiked ? \"Hapus like\" : \"Suka berita ini\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: isLiked ? \"error\" : \"default\",\n          size: \"medium\",\n          onClick: e => {\n            e.stopPropagation(); // Prevent card click\n            onLike(news);\n          },\n          sx: {\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              transform: 'scale(1.1)',\n              color: '#e91e63'\n            },\n            '&:active': {\n              transform: 'scale(0.95)'\n            },\n            ...(isLiked && {\n              animation: 'heartBeat 0.6s ease-in-out',\n              '@keyframes heartBeat': {\n                '0%': {\n                  transform: 'scale(1)'\n                },\n                '14%': {\n                  transform: 'scale(1.3)'\n                },\n                '28%': {\n                  transform: 'scale(1)'\n                },\n                '42%': {\n                  transform: 'scale(1.3)'\n                },\n                '70%': {\n                  transform: 'scale(1)'\n                }\n              }\n            })\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {\n            fontSize: \"medium\",\n            sx: {\n              color: '#e91e63'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: !isAuthenticated ? \"Login untuk menyimpan berita\" : isBookmarked ? \"Hapus dari bookmark\" : \"Tambah ke bookmark\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: isBookmarked ? \"success\" : \"primary\",\n          size: \"medium\",\n          onClick: e => {\n            e.stopPropagation(); // Prevent card click\n            onBookmark(news);\n          },\n          sx: {\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              transform: 'scale(1.1)'\n            }\n          },\n          children: isBookmarked ? /*#__PURE__*/_jsxDEV(BookmarkAddedIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkAddIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Bagikan\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"primary\",\n          size: \"medium\",\n          onClick: e => {\n            e.stopPropagation(); // Prevent card click\n            onShare(news);\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 663,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      onClick: handleNewsClick,\n      sx: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        pb: 2,\n        pt: 2,\n        cursor: 'pointer'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 700,\n          color: 'primary.main',\n          mb: 0.5,\n          fontSize: {\n            xs: 18,\n            md: 22\n          }\n        },\n        children: news.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 688,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          color: 'grey.600',\n          mb: 1,\n          fontSize: {\n            xs: 13,\n            md: 15\n          }\n        },\n        children: [\"Kategori: \", /*#__PURE__*/_jsxDEV(Typography, {\n          component: \"span\",\n          sx: {\n            fontWeight: 600,\n            color: 'secondary.main'\n          },\n          children: news.category_name || news.category || 'Umum'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 21\n        }, this), \" \\u2022 \", formatDate(news.created_at || news.date)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2,\n          mr: 12\n        },\n        children: [\" \", /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'grey.800',\n            fontSize: {\n              xs: 15,\n              md: 16\n            },\n            lineHeight: 1.5,\n            display: '-webkit-box',\n            WebkitLineClamp: expanded ? 'none' : 2,\n            WebkitBoxOrient: 'vertical',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis',\n            mb: 1\n          },\n          children: news.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 11\n        }, this), news.description.length > 100 && /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          color: \"primary\",\n          onClick: e => {\n            e.stopPropagation(); // Prevent card click\n            onExpand();\n          },\n          sx: {\n            alignSelf: 'flex-start',\n            textTransform: 'none',\n            fontWeight: 600,\n            fontSize: '0.8rem',\n            p: 0.5,\n            minWidth: 'auto'\n          },\n          children: expanded ? 'Baca Lebih Sedikit' : 'Baca Selengkapnya'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 692,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 562,\n    columnNumber: 5\n  }, this);\n}\n_c5 = NewsCard;\nfunction MobileNewsLayout({\n  kostum,\n  newsData,\n  popularNews,\n  categoriesData,\n  selectedCategory,\n  setSelectedCategory,\n  expandedCards,\n  handleCardExpand,\n  loading,\n  bottomNav,\n  setBottomNav,\n  handleSidebar,\n  sidebarOpen,\n  handleSidebarClose,\n  handleShare,\n  onBookmark,\n  bookmarkedNews,\n  onSearchClick,\n  handleBottomNavChange,\n  onNewsClick,\n  openSearch,\n  navigate,\n  user,\n  isAuthenticated,\n  handleLogout,\n  onLike,\n  likedNews\n}) {\n  const truncateText = (text, maxLength = 100) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  };\n  const filteredNews = newsData.filter(news => selectedCategory === 'Semua' || news.category === selectedCategory || news.category_name === selectedCategory).slice(0, 6);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'background.default',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: 70,\n          px: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo || '/logo192.png',\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2,\n              cursor: 'pointer'\n            },\n            onError: e => {\n              console.log('Logo error, using fallback');\n              e.target.src = '/logo192.png';\n            },\n            onClick: () => navigate('/')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 22,\n              cursor: 'pointer'\n            },\n            onClick: () => navigate('/'),\n            children: kostum.title || 'React News Portal'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-player.php',\n          sx: {\n            mr: 1\n          },\n          title: \"Video\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-play-circle\",\n            style: {\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: onSearchClick,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: handleSidebar,\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      open: sidebarOpen,\n      onClose: handleSidebarClose,\n      ModalProps: {\n        keepMounted: true\n      },\n      sx: {\n        zIndex: 2000\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 260,\n          p: 3,\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSidebarClose,\n          sx: {\n            position: 'absolute',\n            top: 8,\n            right: 8,\n            zIndex: 10\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 32,\n              height: 32,\n              mr: 1\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 20\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 810,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            },\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 1.5,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                if (isAuthenticated) {\n                  window.location.href = '/saved';\n                } else {\n                  navigate('/auth/login');\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-bookmark\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 17\n              }, this), \"Berita Tersimpan\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                handleSidebarClose();\n                openSearch();\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-search\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 17\n              }, this), \"Cari Berita\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                handleSidebarClose();\n                window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-player.php';\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-play-circle\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 17\n              }, this), \"Video\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            },\n            children: \"Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: isAuthenticated ?\n            /*#__PURE__*/\n            // Logged in user\n            _jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 2,\n                  p: 2,\n                  bgcolor: 'grey.50',\n                  borderRadius: 2,\n                  border: '1px solid',\n                  borderColor: 'grey.200'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AccountCircleIcon, {\n                  sx: {\n                    color: 'primary.main',\n                    fontSize: 32\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: 600,\n                    children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                fullWidth: true,\n                startIcon: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 32\n                }, this),\n                sx: {\n                  textTransform: 'none',\n                  borderColor: 'error.main',\n                  color: 'error.main',\n                  '&:hover': {\n                    borderColor: 'error.main',\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                onClick: handleLogout,\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Not logged in\n            _jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"primary\",\n                fullWidth: true,\n                startIcon: /*#__PURE__*/_jsxDEV(LoginIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 958,\n                  columnNumber: 32\n                }, this),\n                sx: {\n                  textTransform: 'none',\n                  borderColor: 'primary.main',\n                  color: 'primary.main',\n                  '&:hover': {\n                    borderColor: 'primary.main',\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                onClick: () => navigate('/auth/login'),\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                fullWidth: true,\n                startIcon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 977,\n                  columnNumber: 32\n                }, this),\n                sx: {\n                  textTransform: 'none',\n                  backgroundColor: 'primary.main',\n                  '&:hover': {\n                    backgroundColor: 'primary.dark'\n                  }\n                },\n                onClick: () => navigate('/auth/register'),\n                children: \"Register\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        py: 2,\n        position: 'fixed',\n        top: 60,\n        left: 0,\n        right: 0,\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        sx: {\n          overflowX: 'auto',\n          px: 2,\n          '&::-webkit-scrollbar': {\n            display: 'none'\n          },\n          scrollbarWidth: 'none',\n          msOverflowStyle: 'none'\n        },\n        children: categories.map(cat => /*#__PURE__*/_jsxDEV(Chip, {\n          label: cat,\n          color: selectedCategory === cat ? 'primary' : 'default',\n          onClick: () => setSelectedCategory(cat),\n          sx: {\n            fontWeight: 600,\n            cursor: 'pointer',\n            flexShrink: 0,\n            whiteSpace: 'nowrap'\n          }\n        }, cat, false, {\n          fileName: _jsxFileName,\n          lineNumber: 999,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 997,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 996,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 112,\n        left: 0,\n        right: 0,\n        bottom: 70,\n        px: 1,\n        maxWidth: '100%',\n        mx: 'auto',\n        width: '100%',\n        display: 'flex',\n        flexDirection: 'column',\n        minHeight: 0,\n        zIndex: 1,\n        pt: 2,\n        pb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        component: \"main\",\n        sx: {\n          flex: 1,\n          height: 'calc(100vh - 112px - 70px)',\n          // 112px = navbar+kategori, 70px = bottom nav\n          overflowY: 'auto',\n          minHeight: 0,\n          pt: 0,\n          pb: 8,\n          // Add padding bottom for footer\n          '&::-webkit-scrollbar': {\n            display: 'none'\n          },\n          scrollbarWidth: 'none',\n          msOverflowStyle: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(BreakingNewsSlider, {\n          newsData: newsData,\n          kostum: kostum\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PopularTags, {\n          selectedCategory: selectedCategory,\n          setSelectedCategory: setSelectedCategory,\n          categoriesData: categoriesData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1029,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LatestPosts, {\n          newsData: newsData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1032,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 2,\n            overflowY: 'auto',\n            pb: {\n              xs: 10,\n              md: 0\n            },\n            // padding bawah ekstra di mobile\n            '&::-webkit-scrollbar': {\n              display: 'none'\n            },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n            px: 1,\n            pr: 2 // Add right padding to hide scrollbar\n          },\n          children: [loading ? Array.from({\n            length: 6\n          }).map((_, idx) => /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              height: '100%',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative',\n                width: '100%',\n                paddingTop: '100%',\n                bgcolor: 'grey.200'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flex: 1,\n                display: 'flex',\n                flexDirection: 'column',\n                pb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 24,\n                  bgcolor: 'grey.200',\n                  mb: 1,\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 16,\n                  bgcolor: 'grey.100',\n                  mb: 1,\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 60,\n                  bgcolor: 'grey.100',\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1052,\n              columnNumber: 19\n            }, this)]\n          }, idx, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 17\n          }, this)) : filteredNews.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              py: 8,\n              color: 'grey.500',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Belum ada berita\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1061,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Berita akan muncul di sini\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1060,\n            columnNumber: 15\n          }, this) : filteredNews.map((news, idx) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(NewsCard, {\n              news: news,\n              expanded: !!expandedCards[news.id],\n              onExpand: () => handleCardExpand(news.id),\n              formatDate: formatDate,\n              truncateText: truncateText,\n              variant: \"mobile\",\n              onShare: handleShare,\n              onBookmark: onBookmark,\n              isBookmarked: bookmarkedNews.has(news.id),\n              onNewsClick: onNewsClick,\n              isAuthenticated: isAuthenticated,\n              onLike: onLike,\n              isLiked: likedNews.has(news.id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 19\n            }, this)\n          }, news.id || idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1066,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 4,\n              py: 3,\n              borderTop: '1px solid #e0e0e0',\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Footer, {\n              title: kostum.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1088,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1087,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1034,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1011,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1010,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: 'block',\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(0),\n          className: `bottom-nav-item ${bottomNav === 0 ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-home bottom-nav-icon\",\n            style: {\n              color: bottomNav === 0 ? kostum.primary_color : '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: bottomNav === 0 ? 'primary.main' : 'text.secondary'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(1),\n          className: `bottom-nav-item ${bottomNav === 1 ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-play-circle bottom-nav-icon\",\n            style: {\n              color: bottomNav === 1 ? kostum.primary_color : '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: bottomNav === 1 ? 'primary.main' : 'text.secondary'\n            },\n            children: \"Video\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(2),\n          className: `bottom-nav-item ${bottomNav === 2 ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-search bottom-nav-icon\",\n            style: {\n              color: bottomNav === 2 ? kostum.primary_color : '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: bottomNav === 2 ? 'primary.main' : 'text.secondary'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(3),\n          className: `bottom-nav-item ${bottomNav === 3 ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bookmark bottom-nav-icon\",\n            style: {\n              color: bottomNav === 3 ? kostum.primary_color : '#6b7280'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: bottomNav === 3 ? 'primary.main' : 'text.secondary'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1094,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 750,\n    columnNumber: 5\n  }, this);\n}\n_c6 = MobileNewsLayout;\nfunction DesktopNewsLayout({\n  kostum,\n  newsData,\n  popularNews,\n  categoriesData,\n  selectedCategory,\n  setSelectedCategory,\n  expandedCards,\n  handleCardExpand,\n  loading,\n  handleShare,\n  onBookmark,\n  bookmarkedNews,\n  onSearchClick,\n  sidebarOpen,\n  handleSidebar,\n  handleSidebarClose,\n  onNewsClick,\n  openSearch,\n  navigate,\n  user,\n  isAuthenticated,\n  handleLogout,\n  onLike,\n  likedNews\n}) {\n  const truncateText = (text, maxLength = 100) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  };\n  const filteredNews = newsData.filter(news => selectedCategory === 'Semua' || news.category === selectedCategory || news.category_name === selectedCategory).slice(0, 6);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'background.default',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: 80,\n          px: 6\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 28\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: () => navigate('/video'),\n          sx: {\n            mr: 1\n          },\n          title: \"Video\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-play-circle\",\n            style: {\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: onSearchClick,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: handleSidebar,\n          sx: {\n            ml: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1174,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      open: sidebarOpen,\n      onClose: handleSidebarClose,\n      ModalProps: {\n        keepMounted: true\n      },\n      sx: {\n        zIndex: 2000\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 260,\n          p: 3,\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSidebarClose,\n          sx: {\n            position: 'absolute',\n            top: 8,\n            right: 8,\n            zIndex: 10\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 32,\n              height: 32,\n              mr: 1\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 20\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            },\n            children: \"Quick Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 1.5,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                if (isAuthenticated) {\n                  window.location.href = '/saved';\n                } else {\n                  navigate('/auth/login');\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-bookmark\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1243,\n                columnNumber: 17\n              }, this), \"Berita Tersimpan\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                handleSidebarClose();\n                openSearch();\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-search\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1262,\n                columnNumber: 17\n              }, this), \"Cari Berita\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              fullWidth: true,\n              sx: {\n                justifyContent: 'flex-start',\n                textTransform: 'none',\n                color: 'text.primary',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                handleSidebarClose();\n                navigate('/video');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-play-circle\",\n                style: {\n                  marginRight: 8,\n                  fontSize: 14\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1281,\n                columnNumber: 17\n              }, this), \"Video\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            },\n            children: \"Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: isAuthenticated ?\n            /*#__PURE__*/\n            // Logged in user\n            _jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 2,\n                  p: 2,\n                  bgcolor: 'grey.50',\n                  borderRadius: 2,\n                  border: '1px solid',\n                  borderColor: 'grey.200'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AccountCircleIcon, {\n                  sx: {\n                    color: 'primary.main',\n                    fontSize: 32\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1315,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: 600,\n                    children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1317,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1320,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1316,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                fullWidth: true,\n                startIcon: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1329,\n                  columnNumber: 32\n                }, this),\n                sx: {\n                  textTransform: 'none',\n                  borderColor: 'error.main',\n                  color: 'error.main',\n                  '&:hover': {\n                    borderColor: 'error.main',\n                    backgroundColor: 'error.main',\n                    color: 'white'\n                  }\n                },\n                onClick: handleLogout,\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) :\n            /*#__PURE__*/\n            // Not logged in\n            _jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"primary\",\n                fullWidth: true,\n                startIcon: /*#__PURE__*/_jsxDEV(LoginIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1352,\n                  columnNumber: 32\n                }, this),\n                sx: {\n                  textTransform: 'none',\n                  borderColor: 'primary.main',\n                  color: 'primary.main',\n                  '&:hover': {\n                    borderColor: 'primary.main',\n                    backgroundColor: 'primary.main',\n                    color: 'white'\n                  }\n                },\n                onClick: () => navigate('/auth/login'),\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                fullWidth: true,\n                startIcon: /*#__PURE__*/_jsxDEV(PersonAddIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1371,\n                  columnNumber: 32\n                }, this),\n                sx: {\n                  textTransform: 'none',\n                  backgroundColor: 'primary.main',\n                  '&:hover': {\n                    backgroundColor: 'primary.dark'\n                  }\n                },\n                onClick: () => navigate('/auth/register'),\n                children: \"Register\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1367,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        py: 2,\n        position: 'fixed',\n        top: 70,\n        left: 0,\n        right: 0,\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: '1200px',\n          mx: 'auto',\n          px: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          sx: {\n            overflowX: 'auto',\n            justifyContent: 'center',\n            '&::-webkit-scrollbar': {\n              display: 'none'\n            },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none'\n          },\n          children: categories.map(cat => /*#__PURE__*/_jsxDEV(Chip, {\n            label: cat,\n            color: selectedCategory === cat ? 'primary' : 'default',\n            onClick: () => setSelectedCategory(cat),\n            sx: {\n              fontWeight: 600,\n              cursor: 'pointer',\n              flexShrink: 0,\n              whiteSpace: 'nowrap'\n            }\n          }, cat, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1399,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1391,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1390,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1389,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 128,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        px: 4,\n        maxWidth: '1200px',\n        mx: 'auto',\n        width: '100%',\n        display: 'flex',\n        flexDirection: 'row',\n        gap: 4,\n        minHeight: 0,\n        zIndex: 1,\n        pt: 3,\n        pb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        component: \"main\",\n        sx: {\n          flex: 1,\n          height: 'calc(100vh - 128px)',\n          // 128px = navbar+kategori desktop\n          overflowY: 'auto',\n          minHeight: 0,\n          pt: 0,\n          pb: 8,\n          // Add padding bottom for footer\n          '&::-webkit-scrollbar': {\n            display: 'none'\n          },\n          scrollbarWidth: 'none',\n          msOverflowStyle: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(BreakingNewsSlider, {\n          newsData: newsData,\n          kostum: kostum\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PopularTags, {\n          selectedCategory: selectedCategory,\n          setSelectedCategory: setSelectedCategory,\n          categoriesData: categoriesData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LatestPosts, {\n          newsData: newsData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1432,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'row',\n            gap: 4,\n            width: '100%',\n            maxWidth: '100%',\n            overflowX: 'auto',\n            '&::-webkit-scrollbar': {\n              display: 'none'\n            },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n            px: 1,\n            mt: 2,\n            pb: 2 // Add padding bottom to hide scrollbar\n          },\n          children: loading ? Array.from({\n            length: 6\n          }).map((_, idx) => /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              height: '100%',\n              minWidth: 320,\n              flexShrink: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative',\n                width: '100%',\n                paddingTop: '100%',\n                bgcolor: 'grey.200'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1451,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flex: 1,\n                display: 'flex',\n                flexDirection: 'column',\n                pb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 24,\n                  bgcolor: 'grey.200',\n                  mb: 1,\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1453,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 16,\n                  bgcolor: 'grey.100',\n                  mb: 1,\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1454,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 60,\n                  bgcolor: 'grey.100',\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1455,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1452,\n              columnNumber: 19\n            }, this)]\n          }, idx, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1450,\n            columnNumber: 17\n          }, this)) : filteredNews.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              py: 8,\n              color: 'grey.500',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Belum ada berita\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Berita akan muncul di sini\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1462,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1460,\n            columnNumber: 15\n          }, this) : filteredNews.map((news, idx) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              minWidth: 320,\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(NewsCard, {\n              news: news,\n              expanded: !!expandedCards[news.id],\n              onExpand: () => handleCardExpand(news.id),\n              formatDate: formatDate,\n              truncateText: truncateText,\n              variant: \"desktop\",\n              onShare: handleShare,\n              onBookmark: onBookmark,\n              isBookmarked: bookmarkedNews.has(news.id),\n              onNewsClick: onNewsClick,\n              isAuthenticated: isAuthenticated,\n              onLike: onLike,\n              isLiked: likedNews.has(news.id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1467,\n              columnNumber: 35\n            }, this)\n          }, news.id || idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1466,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1434,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1411,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        component: \"aside\",\n        sx: {\n          width: 320,\n          mt: 0,\n          display: 'block',\n          mb: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#fff',\n            borderRadius: 2,\n            boxShadow: 1,\n            p: 3,\n            mb: 6,\n            mt: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              mb: 2\n            },\n            children: \"Berita Populer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: popularNews.length > 0 ? popularNews.map((item, idx) => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: getImageUrl(item.image),\n                alt: item.title,\n                sx: {\n                  width: 48,\n                  height: 48,\n                  mr: 1\n                },\n                onError: e => {\n                  e.target.src = 'https://source.unsplash.com/100x100/?news';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1496,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 600,\n                    color: 'grey.900',\n                    lineHeight: 1.2\n                  },\n                  children: item.title.length > 50 ? item.title.substring(0, 50) + '...' : item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1498,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: 'grey.600'\n                  },\n                  children: [formatDate(item.created_at || item.date), \" \\u2022 \", item.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1501,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1497,\n                columnNumber: 21\n              }, this)]\n            }, item.id || idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1495,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'grey.500',\n                textAlign: 'center'\n              },\n              children: \"Belum ada berita populer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1508,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1492,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1489,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1488,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4,\n          py: 3,\n          borderTop: '1px solid #e0e0e0',\n          textAlign: 'center',\n          px: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Footer, {\n          title: kostum.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1518,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1517,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1410,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1172,\n    columnNumber: 5\n  }, this);\n}\n\n// Create custom theme based on kostum colors\n_c7 = DesktopNewsLayout;\nconst createCustomTheme = kostum => {\n  return createTheme({\n    palette: {\n      primary: {\n        main: kostum.primary_color || '#1976d2'\n      },\n      secondary: {\n        main: kostum.secondary_color || '#dc004e'\n      },\n      background: {\n        default: kostum.background_color || '#f5f5f5'\n      },\n      text: {\n        primary: kostum.text_color || '#333333'\n      }\n    }\n  });\n};\nexport default function LandingPage() {\n  _s2();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout,\n    isAuthenticated\n  } = useAuth();\n  const [selectedCategory, setSelectedCategory] = useState('Semua');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [bottomNav, setBottomNav] = useState(0);\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: '',\n    primary_color: '#1976d2',\n    secondary_color: '#dc004e',\n    accent_color: '#ff9800',\n    background_color: '#f5f5f5',\n    text_color: '#333333'\n  });\n  const [newsData, setNewsData] = useState([]);\n  const [popularNews, setPopularNews] = useState([]);\n  const [categoriesData, setCategoriesData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [expandedCards, setExpandedCards] = useState({});\n  // Removed searchModalOpen - using searchActive overlay instead for consistency\n  const [searchActive, setSearchActive] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [bookmarkedNews, setBookmarkedNews] = useState(new Set());\n  const [likedNews, setLikedNews] = useState(new Set());\n  const [toast, setToast] = useState({\n    visible: false,\n    message: ''\n  });\n\n  // Check URL parameters on component mount\n  React.useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const kategori = urlParams.get('kategori');\n    const beritaId = urlParams.get('berita');\n    if (kategori && kategori !== 'Semua') {\n      setSelectedCategory(kategori);\n    }\n\n    // If there's a specific news ID, we could highlight it later\n    if (beritaId) {\n      // You can add logic here to highlight or scroll to the specific news\n      console.log('Shared news ID:', beritaId);\n    }\n  }, []);\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n  const handleSidebar = () => setSidebarOpen(true);\n  const handleSidebarClose = () => setSidebarOpen(false);\n  const handleShare = async news => {\n    try {\n      // Update share count in database\n      fetch('http://localhost:5000/api/posts/' + news.id + '/share', {\n        method: 'POST'\n      }).catch(err => console.error('Error updating share count:', err));\n\n      // Generate URL-friendly title for the link\n      const urlTitle = news.title.toLowerCase().replace(/[^a-z0-9\\s-]/g, '') // Remove special characters\n      .replace(/\\s+/g, '-') // Replace spaces with hyphens\n      .replace(/-+/g, '-') // Replace multiple hyphens with single\n      .trim();\n      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;\n\n      // Copy to clipboard\n      await navigator.clipboard.writeText(link);\n\n      // Update local state to reflect share count increase\n      setNewsData(prevData => prevData.map(item => item.id === news.id ? {\n        ...item,\n        share: (item.share || 0) + 1\n      } : item));\n\n      // Show success toast\n      setToast({\n        visible: true,\n        message: 'Link berita berhasil disalin ke clipboard!'\n      });\n\n      // Auto-hide toast after 3 seconds\n      setTimeout(() => {\n        setToast({\n          visible: false,\n          message: ''\n        });\n      }, 3000);\n    } catch (error) {\n      console.error('Failed to copy link:', error);\n      setToast({\n        visible: true,\n        message: 'Gagal menyalin link berita'\n      });\n      setTimeout(() => {\n        setToast({\n          visible: false,\n          message: ''\n        });\n      }, 3000);\n    }\n  };\n  const handleLike = async news => {\n    const isCurrentlyLiked = likedNews.has(news.id);\n    try {\n      // Optimistic update\n      const newLikedNews = new Set(likedNews);\n      if (isCurrentlyLiked) {\n        newLikedNews.delete(news.id);\n      } else {\n        newLikedNews.add(news.id);\n      }\n      setLikedNews(newLikedNews);\n\n      // Update like status in database\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_like&id=${news.id}`, {\n        method: 'POST'\n      });\n      const data = await response.json();\n      if (data.success) {\n        // Update liked state based on server response\n        const finalLikedNews = new Set(likedNews);\n        if (data.liked) {\n          finalLikedNews.add(news.id);\n        } else {\n          finalLikedNews.delete(news.id);\n        }\n        setLikedNews(finalLikedNews);\n\n        // Update news data with new likes count\n        setNewsData(prevNews => prevNews.map(item => item.id === news.id ? {\n          ...item,\n          likes: data.likes\n        } : item));\n        setPopularNews(prevNews => prevNews.map(item => item.id === news.id ? {\n          ...item,\n          likes: data.likes\n        } : item));\n        setToast({\n          visible: true,\n          message: data.liked ? 'Berita disukai!' : 'Like dihapus'\n        });\n      } else {\n        // Revert optimistic update on failure\n        setLikedNews(likedNews);\n        setToast({\n          visible: true,\n          message: 'Gagal memperbarui like'\n        });\n      }\n    } catch (error) {\n      // Revert optimistic update on error\n      setLikedNews(likedNews);\n      console.error('Failed to toggle like:', error);\n      setToast({\n        visible: true,\n        message: 'Gagal memperbarui like'\n      });\n    }\n\n    // Hide toast after 3 seconds\n    setTimeout(() => {\n      setToast({\n        visible: false,\n        message: ''\n      });\n    }, 3000);\n  };\n  const handleBookmark = async news => {\n    // Check authentication first\n    if (!isAuthenticated) {\n      navigate('/auth/login');\n      return;\n    }\n    const isCurrentlyBookmarked = bookmarkedNews.has(news.id);\n    try {\n      // Update bookmark status in database\n      const action = isCurrentlyBookmarked ? 'remove_saved_news' : 'add_saved_news';\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          action: action,\n          post_id: news.id\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      if (result.success) {\n        // Update local state\n        setBookmarkedNews(prev => {\n          const newSet = new Set(prev);\n          if (isCurrentlyBookmarked) {\n            newSet.delete(news.id);\n          } else {\n            newSet.add(news.id);\n          }\n          return newSet;\n        });\n\n        // Show toast notification\n        const message = isCurrentlyBookmarked ? 'Berita dihapus dari bookmark' : 'Berita ditambahkan ke bookmark';\n        setToast({\n          visible: true,\n          message\n        });\n        setTimeout(() => {\n          setToast({\n            visible: false,\n            message: ''\n          });\n        }, 3000);\n      } else {\n        setToast({\n          visible: true,\n          message: 'Gagal mengupdate bookmark: ' + (result.message || 'Unknown error')\n        });\n        setTimeout(() => {\n          setToast({\n            visible: false,\n            message: ''\n          });\n        }, 3000);\n      }\n    } catch (error) {\n      console.error('Error updating bookmark:', error);\n      setToast({\n        visible: true,\n        message: 'Error: ' + error.message\n      });\n      setTimeout(() => {\n        setToast({\n          visible: false,\n          message: ''\n        });\n      }, 3000);\n    }\n  };\n\n  // Handler untuk buka search overlay\n  const openSearch = () => {\n    setSearchActive(true);\n    setSearchQuery('');\n  };\n  // Handler untuk tutup search overlay\n  const closeSearch = () => {\n    setSearchActive(false);\n    setSearchQuery('');\n  };\n  // Filter berita sesuai query\n  const filteredSearch = searchQuery.trim() ? newsData.filter(n => n.title.toLowerCase().includes(searchQuery.toLowerCase()) || n.description.toLowerCase().includes(searchQuery.toLowerCase())).slice(0, 8) : [];\n  // Handler keyboard ESC\n  React.useEffect(() => {\n    if (!searchActive) return;\n    const handleKey = e => {\n      if (e.key === 'Escape') closeSearch();\n    };\n    window.addEventListener('keydown', handleKey);\n    return () => window.removeEventListener('keydown', handleKey);\n  }, [searchActive]);\n\n  // Load website settings from database\n  React.useEffect(() => {\n    // Get settings from database via correct PHP API\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_settings').then(res => res.json()).then(response => {\n      if (response.success && response.data) {\n        const data = response.data;\n\n        // Process logo path - use fallback SVG if database logo not accessible\n        let logoPath = 'data:image/svg+xml;base64,' + btoa(`\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"#3B82F6\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          `);\n        if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {\n          // Extract filename and use root/uploads path\n          const filename = data.website_logo.split('/').pop();\n          logoPath = `http://localhost/react-news/uploads/${filename}`;\n          // console.log('LandingPage - Logo path:', logoPath);\n        }\n        setKostum({\n          logo: logoPath,\n          title: data.website_name || 'React News Portal',\n          description: data.website_description || 'Portal berita terkini dan terpercaya',\n          primary_color: data.primary_color || '#1976d2',\n          secondary_color: data.secondary_color || '#dc004e',\n          accent_color: data.accent_color || '#ff9800',\n          background_color: '#f5f5f5',\n          text_color: '#333333',\n          footer_text: '© 2024 React News Portal. All rights reserved.',\n          contact_email: '<EMAIL>',\n          social_facebook: '',\n          social_twitter: '',\n          social_instagram: '',\n          social_youtube: '',\n          meta_keywords: 'berita, news, portal',\n          meta_description: data.website_description || 'Portal berita terkini'\n        });\n\n        // Update document title and meta tags\n        document.title = data.website_name || 'React News Portal';\n\n        // Update meta description\n        const metaDescription = document.querySelector('meta[name=\"description\"]');\n        if (metaDescription) {\n          metaDescription.setAttribute('content', data.website_description || 'Portal berita terkini');\n        }\n\n        // Update meta keywords\n        const metaKeywords = document.querySelector('meta[name=\"keywords\"]');\n        if (metaKeywords) {\n          metaKeywords.setAttribute('content', 'berita, news, portal');\n        }\n\n        // Update favicon\n        if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {\n          const filename = data.website_logo.split('/').pop();\n          const faviconUrl = `http://localhost/react-news/uploads/${filename}`;\n\n          // Update existing favicon or create new one\n          let favicon = document.querySelector('link[rel=\"icon\"]') || document.querySelector('link[rel=\"shortcut icon\"]');\n          if (!favicon) {\n            favicon = document.createElement('link');\n            favicon.rel = 'icon';\n            document.head.appendChild(favicon);\n          }\n          favicon.href = faviconUrl;\n\n          // Also update apple-touch-icon if exists\n          const appleTouchIcon = document.querySelector('link[rel=\"apple-touch-icon\"]');\n          if (appleTouchIcon) {\n            appleTouchIcon.href = faviconUrl;\n          }\n          console.log('LandingPage - Favicon updated:', faviconUrl);\n        }\n      } else {\n        throw new Error('Invalid API response');\n      }\n    }).catch(err => {\n      console.log('Database settings not available, using defaults:', err);\n      // Use default settings matching database.sql structure\n      setKostum({\n        logo: 'data:image/svg+xml;base64,' + btoa(`\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"#3B82F6\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          `),\n        title: 'React News Portal',\n        description: 'Portal berita terkini dan terpercaya',\n        primary_color: '#3B82F6',\n        secondary_color: '#10B981',\n        accent_color: '#F59E0B',\n        background_color: '#f5f5f5',\n        text_color: '#333333',\n        footer_text: '© 2024 React News Portal. All rights reserved.',\n        contact_email: '<EMAIL>',\n        social_facebook: '',\n        social_twitter: '',\n        social_instagram: '',\n        social_youtube: '',\n        meta_keywords: 'berita, news, portal, react',\n        meta_description: 'Portal berita terkini dengan teknologi React'\n      });\n\n      // Set default document title\n      document.title = 'React News Portal';\n    });\n  }, []);\n\n  // Function to refresh settings (can be called when admin updates settings)\n  const refreshSettings = React.useCallback(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_settings&t=' + Date.now()).then(res => res.json()).then(response => {\n      if (response.success && response.data) {\n        const data = response.data;\n\n        // Process logo path - use fallback SVG if database logo not accessible\n        let logoPath = 'data:image/svg+xml;base64,' + btoa(`\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"#3B82F6\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          `);\n        if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {\n          // Extract filename and use root/uploads path\n          const filename = data.website_logo.split('/').pop();\n          logoPath = `http://localhost/react-news/uploads/${filename}`;\n          // console.log('LandingPage - RefreshSettings Logo path:', logoPath);\n\n          // Update favicon in refreshSettings\n          let favicon = document.querySelector('link[rel=\"icon\"]') || document.querySelector('link[rel=\"shortcut icon\"]');\n          if (!favicon) {\n            favicon = document.createElement('link');\n            favicon.rel = 'icon';\n            document.head.appendChild(favicon);\n          }\n          favicon.href = logoPath;\n\n          // Also update apple-touch-icon if exists\n          const appleTouchIcon = document.querySelector('link[rel=\"apple-touch-icon\"]');\n          if (appleTouchIcon) {\n            appleTouchIcon.href = logoPath;\n          }\n\n          // console.log('LandingPage - RefreshSettings Favicon updated:', logoPath);\n        }\n        setKostum(prev => ({\n          ...prev,\n          logo: logoPath,\n          title: data.website_name || 'React News Portal',\n          description: data.website_description || 'Portal berita terkini dan terpercaya'\n        }));\n        document.title = data.website_name || 'React News Portal';\n      }\n    }).catch(err => console.log('Failed to refresh settings:', err));\n  }, []);\n\n  // Listen for settings updates (optional - for real-time updates)\n  React.useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === 'settings_updated') {\n        console.log('Settings updated, refreshing...');\n        refreshSettings();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, [refreshSettings]);\n\n  // Load categories from database\n  React.useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_categories').then(res => res.json()).then(data => {\n      if (data.success && Array.isArray(data.data)) {\n        // Map database structure to frontend format\n        const mappedCategories = data.data.map(cat => ({\n          id: cat.id,\n          name: cat.name,\n          slug: cat.slug,\n          description: cat.description,\n          color: cat.color || '#6B7280',\n          is_active: cat.is_active,\n          post_count: cat.post_count || 0,\n          created_at: cat.created_at,\n          updated_at: cat.updated_at\n        }));\n\n        // Filter only active categories\n        const activeCategories = mappedCategories.filter(cat => cat.is_active !== false);\n        setCategoriesData(activeCategories);\n      }\n    }).catch(err => {\n      // Fallback categories matching database.sql structure\n      setCategoriesData([{\n        id: 1,\n        name: 'Umum',\n        slug: 'umum',\n        description: 'Berita umum dan informasi terkini',\n        color: '#6B7280',\n        is_active: true,\n        post_count: 0\n      }, {\n        id: 2,\n        name: 'Teknologi',\n        slug: 'teknologi',\n        description: 'Berita teknologi dan inovasi digital',\n        color: '#3B82F6',\n        is_active: true,\n        post_count: 0\n      }, {\n        id: 3,\n        name: 'Bisnis',\n        slug: 'bisnis',\n        description: 'Berita bisnis dan ekonomi',\n        color: '#10B981',\n        is_active: true,\n        post_count: 0\n      }, {\n        id: 4,\n        name: 'Olahraga',\n        slug: 'olahraga',\n        description: 'Berita olahraga dan kompetisi',\n        color: '#F59E0B',\n        is_active: true,\n        post_count: 0\n      }, {\n        id: 5,\n        name: 'Hiburan',\n        slug: 'hiburan',\n        description: 'Berita hiburan dan selebriti',\n        color: '#EF4444',\n        is_active: true,\n        post_count: 4\n      }, {\n        id: 6,\n        name: 'Politik',\n        slug: 'politik',\n        description: 'Berita politik dan pemerintahan',\n        color: '#8B5CF6',\n        is_active: true,\n        post_count: 2\n      }, {\n        id: 7,\n        name: 'Kesehatan',\n        slug: 'kesehatan',\n        description: 'Berita kesehatan dan medis',\n        color: '#06B6D4',\n        is_active: true,\n        post_count: 7\n      }]);\n    });\n  }, []);\n\n  // Load news data from database\n  React.useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news').then(res => res.json()).then(data => {\n      if (data.success && Array.isArray(data.data)) {\n        // Map database structure to frontend format\n        const mappedData = data.data.map(item => {\n          var _item$content;\n          return {\n            id: item.id,\n            title: item.title,\n            slug: item.slug,\n            description: item.description || item.excerpt || ((_item$content = item.content) === null || _item$content === void 0 ? void 0 : _item$content.substring(0, 200)) + '...',\n            content: item.content,\n            image: item.image || '/uploads/default-news.jpg',\n            image_alt: item.image_alt || item.title,\n            category: item.category_name || 'Umum',\n            category_name: item.category_name || 'Umum',\n            category_id: item.category_id || 1,\n            category_color: item.category_color || '#6B7280',\n            status: item.status || 'published',\n            featured: item.featured || false,\n            tags: item.tags ? item.tags.split(',') : [],\n            views: item.views || 0,\n            share: item.share || 0,\n            likes: item.likes || 0,\n            comments_count: item.comments_count || 0,\n            reading_time: item.reading_time || 5,\n            date: item.date || item.created_at,\n            published_at: item.published_at,\n            created_at: item.created_at,\n            updated_at: item.updated_at,\n            author: item.author_name || item.full_name || 'Admin',\n            user_id: item.user_id\n          };\n        });\n\n        // Filter only published posts\n        const publishedPosts = mappedData.filter(item => item.status === 'published');\n        setNewsData(publishedPosts);\n\n        // Sort by engagement score for popular news\n        const popular = publishedPosts.sort((a, b) => {\n          const scoreA = a.views * 1 + a.share * 3 + a.likes * 2 + a.comments_count * 4;\n          const scoreB = b.views * 1 + b.share * 3 + b.likes * 2 + b.comments_count * 4;\n          return scoreB - scoreA;\n        }).slice(0, 5);\n        setPopularNews(popular);\n      } else {\n        setNewsData([]);\n        setPopularNews([]);\n      }\n      setLoading(false);\n    }).catch(() => {\n      // Fallback data matching database.sql structure\n      const dummyNews = [{\n        id: 1,\n        title: 'Selamat Datang di React News Portal',\n        slug: 'selamat-datang-di-react-news-portal',\n        description: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna.',\n        content: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna. Dengan desain responsif dan fitur-fitur canggih.',\n        image: '/uploads/default-tech.jpg',\n        image_alt: 'React News Portal',\n        category: 'Teknologi',\n        category_name: 'Teknologi',\n        category_id: 2,\n        category_color: '#3B82F6',\n        status: 'published',\n        featured: true,\n        tags: ['teknologi', 'react', 'portal', 'berita'],\n        views: 150,\n        share: 25,\n        likes: 45,\n        comments_count: 8,\n        reading_time: 3,\n        date: new Date().toISOString(),\n        published_at: new Date().toISOString(),\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n        author: 'Admin',\n        user_id: 1\n      }, {\n        id: 2,\n        title: 'Teknologi AI Terbaru Mengubah Dunia Digital',\n        slug: 'teknologi-ai-terbaru-mengubah-dunia-digital',\n        description: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari.',\n        content: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari. AI kini menjadi bagian integral dari transformasi digital.',\n        image: '/uploads/default-ai.jpg',\n        image_alt: 'Teknologi AI',\n        category: 'Teknologi',\n        category_name: 'Teknologi',\n        category_id: 2,\n        category_color: '#3B82F6',\n        status: 'published',\n        featured: false,\n        tags: ['ai', 'teknologi', 'digital', 'inovasi'],\n        views: 89,\n        share: 12,\n        likes: 23,\n        comments_count: 5,\n        reading_time: 4,\n        date: new Date(Date.now() - 86400000).toISOString(),\n        published_at: new Date(Date.now() - 86400000).toISOString(),\n        created_at: new Date(Date.now() - 86400000).toISOString(),\n        updated_at: new Date(Date.now() - 86400000).toISOString(),\n        author: 'Admin',\n        user_id: 1\n      }, {\n        id: 3,\n        title: 'Tips Investasi untuk Pemula di Era Digital',\n        slug: 'tips-investasi-untuk-pemula-di-era-digital',\n        description: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya.',\n        content: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya. Pelajari strategi investasi yang tepat untuk pemula.',\n        image: '/uploads/default-business.jpg',\n        image_alt: 'Investasi Digital',\n        category: 'Bisnis',\n        category_name: 'Bisnis',\n        category_id: 3,\n        category_color: '#10B981',\n        status: 'published',\n        featured: false,\n        tags: ['investasi', 'bisnis', 'digital', 'keuangan'],\n        views: 67,\n        share: 8,\n        likes: 15,\n        comments_count: 3,\n        reading_time: 6,\n        date: new Date(Date.now() - 172800000).toISOString(),\n        published_at: new Date(Date.now() - 172800000).toISOString(),\n        created_at: new Date(Date.now() - 172800000).toISOString(),\n        updated_at: new Date(Date.now() - 172800000).toISOString(),\n        author: 'Admin',\n        user_id: 1\n      }];\n      setNewsData(dummyNews);\n      setPopularNews(dummyNews);\n      setLoading(false);\n    });\n  }, []);\n\n  // Load bookmarked news from database (only if authenticated)\n  React.useEffect(() => {\n    if (!isAuthenticated) {\n      setBookmarkedNews(new Set()); // Clear bookmarks if not authenticated\n      return;\n    }\n    const loadBookmarkedNews = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_saved_news');\n        const data = await response.json();\n        if (data.success && Array.isArray(data.data)) {\n          // Extract news IDs that are bookmarked\n          const bookmarkedIds = new Set(data.data.map(news => news.id));\n          setBookmarkedNews(bookmarkedIds);\n        }\n      } catch (error) {\n        console.log('Could not load bookmarked news:', error);\n      }\n    };\n    loadBookmarkedNews();\n  }, [isAuthenticated]);\n\n  // Load liked posts from database (based on IP address)\n  React.useEffect(() => {\n    const loadLikedPosts = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_liked_posts');\n        const data = await response.json();\n        if (data.success && Array.isArray(data.data)) {\n          // Extract post IDs that are liked by this IP\n          const likedIds = new Set(data.data.map(id => parseInt(id)));\n          setLikedNews(likedIds);\n          console.log('Loaded liked posts for IP:', data.ip_address, 'Posts:', data.data);\n        }\n      } catch (error) {\n        console.log('Could not load liked posts:', error);\n      }\n    };\n    loadLikedPosts();\n  }, []); // Load once on component mount\n\n  // Update category post counts when both newsData and categoriesData are loaded\n  React.useEffect(() => {\n    if (newsData.length > 0 && categoriesData.length > 0) {\n      // Check if categories already have correct post_count from API\n      const hasApiCounts = categoriesData.some(cat => cat.post_count > 0);\n      if (!hasApiCounts) {\n        // Count posts per category from newsData\n        const postCounts = {};\n        newsData.forEach(news => {\n          const categoryId = news.category_id;\n          postCounts[categoryId] = (postCounts[categoryId] || 0) + 1;\n        });\n\n        // Update categories with actual post counts\n        const updatedCategories = categoriesData.map(cat => ({\n          ...cat,\n          post_count: postCounts[cat.id] || 0\n        }));\n        setCategoriesData(updatedCategories);\n      }\n    }\n  }, [newsData, categoriesData]);\n  const handleCardExpand = cardId => {\n    setExpandedCards(prev => ({\n      ...prev,\n      [cardId]: !prev[cardId]\n    }));\n  };\n  const handleNewsClick = async newsId => {\n    // Increment views when clicking news card\n    try {\n      await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_views&id=${newsId}`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.log('Could not increment views:', error);\n    }\n\n    // Navigate to news detail\n    navigate(`/data-news/${newsId}`);\n  };\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  const handleSearchClick = () => {\n    // Use the same search function as bottom nav for consistency\n    openSearch();\n    setSidebarOpen(false); // Close sidebar when search opens\n  };\n\n  // Handler untuk klik bottom nav\n  const handleBottomNavChange = newValue => {\n    if (newValue === 0) {\n      setBottomNav(0);\n      // Already on home page, just update state\n    } else if (newValue === 1) {\n      setBottomNav(1);\n      // Video - Navigate to new video player page\n      setBottomNav(1);\n      console.log('Navigating to video player page...');\n      // Direct navigation to new video player page\n      window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-player.php';\n    } else if (newValue === 2) {\n      setBottomNav(2);\n      openSearch(); // Search\n    } else if (newValue === 3) {\n      // Simpan - Check authentication first\n      if (isAuthenticated) {\n        window.location.href = '/saved'; // Navigate to saved page\n      } else {\n        // Redirect to login if not authenticated\n        navigate('/auth/login');\n      }\n    } else {\n      setBottomNav(newValue);\n    }\n  };\n  const customTheme = createCustomTheme(kostum);\n  if (isDesktop) {\n    return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: customTheme,\n      children: [/*#__PURE__*/_jsxDEV(DesktopNewsLayout, {\n        kostum: kostum,\n        newsData: newsData,\n        popularNews: popularNews,\n        categoriesData: categoriesData,\n        selectedCategory: selectedCategory,\n        setSelectedCategory: setSelectedCategory,\n        expandedCards: expandedCards,\n        handleCardExpand: handleCardExpand,\n        loading: loading,\n        handleShare: handleShare,\n        onBookmark: handleBookmark,\n        bookmarkedNews: bookmarkedNews,\n        onSearchClick: openSearch,\n        sidebarOpen: sidebarOpen,\n        handleSidebar: handleSidebar,\n        handleSidebarClose: handleSidebarClose,\n        onNewsClick: handleNewsClick,\n        openSearch: openSearch,\n        navigate: navigate,\n        user: user,\n        isAuthenticated: isAuthenticated,\n        handleLogout: handleLogout,\n        onLike: handleLike,\n        likedNews: likedNews\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Toast, {\n        message: toast.message,\n        isVisible: toast.visible,\n        onClose: () => setToast({\n          visible: false,\n          message: ''\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2381,\n        columnNumber: 9\n      }, this), searchActive && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: closeSearch,\n          sx: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 2100,\n            bgcolor: 'rgba(0,0,0,0.3)',\n            animation: 'fadeIn 0.3s ease-out',\n            '@keyframes fadeIn': {\n              '0%': {\n                opacity: 0\n              },\n              '100%': {\n                opacity: 1\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2390,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            zIndex: 2101,\n            bgcolor: 'rgba(255,255,255,0.98)',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',\n            px: 2,\n            pt: 10,\n            // Space for navbar\n            pb: 3,\n            animation: 'slideDown 0.3s ease-out',\n            '@keyframes slideDown': {\n              '0%': {\n                transform: 'translateY(-100%)',\n                opacity: 0\n              },\n              '100%': {\n                transform: 'translateY(0)',\n                opacity: 1\n              }\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: 600,\n              mx: 'auto',\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              autoFocus: true,\n              fullWidth: true,\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              placeholder: \"Cari berita di sini...\",\n              variant: \"outlined\",\n              sx: {\n                fontSize: 18,\n                bgcolor: '#fff',\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 3,\n                  fontSize: 16,\n                  '&:hover fieldset': {\n                    borderColor: 'primary.main'\n                  },\n                  '&.Mui-focused fieldset': {\n                    borderColor: 'primary.main',\n                    borderWidth: 2\n                  }\n                }\n              },\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                  sx: {\n                    color: 'grey.400',\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2457,\n                  columnNumber: 21\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: closeSearch,\n              sx: {\n                position: 'absolute',\n                top: 8,\n                right: 8,\n                zIndex: 10,\n                bgcolor: 'rgba(0,0,0,0.05)',\n                '&:hover': {\n                  bgcolor: 'rgba(0,0,0,0.1)'\n                }\n              },\n              \"aria-label\": \"Tutup\",\n              children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2475,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2461,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2432,\n            columnNumber: 13\n          }, this), searchQuery.trim() && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: 600,\n              mx: 'auto',\n              mt: 2,\n              bgcolor: '#fff',\n              borderRadius: 3,\n              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n              p: 2,\n              maxHeight: '60vh',\n              overflowY: 'auto'\n            },\n            children: filteredSearch.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'grey.500',\n                textAlign: 'center',\n                py: 3\n              },\n              children: [\"Tidak ada berita ditemukan untuk \\\"\", searchQuery, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2492,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'grey.600',\n                  mb: 2,\n                  fontWeight: 500\n                },\n                children: [\"Ditemukan \", filteredSearch.length, \" berita\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2497,\n                columnNumber: 21\n              }, this), filteredSearch.map(item => /*#__PURE__*/_jsxDEV(Box, {\n                onClick: () => {\n                  closeSearch();\n                  handleNewsClick(item.id);\n                },\n                sx: {\n                  py: 2,\n                  px: 2,\n                  borderBottom: '1px solid #f0f0f0',\n                  cursor: 'pointer',\n                  borderRadius: 2,\n                  '&:last-child': {\n                    borderBottom: 0\n                  },\n                  '&:hover': {\n                    bgcolor: 'rgba(25, 118, 210, 0.04)',\n                    transform: 'translateY(-1px)',\n                    transition: 'all 0.2s ease'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    color: 'primary.main',\n                    mb: 0.5\n                  },\n                  children: item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2521,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: 'grey.600',\n                    display: 'block',\n                    mb: 1\n                  },\n                  children: [item.category, \" \\u2022 \", new Date(item.date).toLocaleDateString('id-ID', {\n                    day: 'numeric',\n                    month: 'long',\n                    year: 'numeric'\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2524,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'grey.700',\n                    lineHeight: 1.4\n                  },\n                  children: item.description.length > 100 ? item.description.substring(0, 100) + '...' : item.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2527,\n                  columnNumber: 25\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2501,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2480,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2408,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2352,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: customTheme,\n    children: [/*#__PURE__*/_jsxDEV(MobileNewsLayout, {\n      kostum: kostum,\n      newsData: newsData,\n      popularNews: popularNews,\n      categoriesData: categoriesData,\n      selectedCategory: selectedCategory,\n      setSelectedCategory: setSelectedCategory,\n      expandedCards: expandedCards,\n      handleCardExpand: handleCardExpand,\n      loading: loading,\n      bottomNav: bottomNav,\n      setBottomNav: setBottomNav,\n      handleSidebar: handleSidebar,\n      sidebarOpen: sidebarOpen,\n      handleSidebarClose: handleSidebarClose,\n      handleShare: handleShare,\n      onBookmark: handleBookmark,\n      bookmarkedNews: bookmarkedNews,\n      onSearchClick: handleSearchClick,\n      handleBottomNavChange: handleBottomNavChange,\n      onNewsClick: handleNewsClick,\n      openSearch: openSearch,\n      navigate: navigate,\n      user: user,\n      isAuthenticated: isAuthenticated,\n      handleLogout: handleLogout,\n      onLike: handleLike,\n      likedNews: likedNews\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2544,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toast, {\n      message: toast.message,\n      isVisible: toast.visible,\n      onClose: () => setToast({\n        visible: false,\n        message: ''\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2575,\n      columnNumber: 7\n    }, this), searchActive && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        zIndex: 2101,\n        bgcolor: 'rgba(255,255,255,0.98)',\n        boxShadow: 3,\n        px: {\n          xs: 2,\n          md: 0\n        },\n        pt: {\n          xs: 2,\n          md: 0\n        },\n        // desktop: pt: 0 agar overlay tepat di atas\n        pb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: 500,\n          mx: 'auto',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          fullWidth: true,\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          placeholder: \"Cari berita di sini...\",\n          variant: \"outlined\",\n          sx: {\n            fontSize: 18,\n            bgcolor: '#fff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2595,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: closeSearch,\n          sx: {\n            position: 'absolute',\n            top: 8,\n            right: 8,\n            zIndex: 10\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2609,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2604,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2594,\n        columnNumber: 11\n      }, this), searchQuery.trim() && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: 500,\n          mx: 'auto',\n          mt: 1,\n          bgcolor: '#fff',\n          borderRadius: 2,\n          boxShadow: 1,\n          p: 1\n        },\n        children: filteredSearch.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'grey.500',\n            textAlign: 'center',\n            py: 2\n          },\n          children: \"Tidak ada berita ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2616,\n          columnNumber: 17\n        }, this) : filteredSearch.map(item => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            py: 1,\n            px: 1,\n            borderBottom: '1px solid #eee',\n            cursor: 'pointer',\n            '&:last-child': {\n              borderBottom: 0\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'primary.main'\n            },\n            children: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2622,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: 'grey.600'\n            },\n            children: [item.category, \" \\u2022 \", new Date(item.date).toLocaleDateString('id-ID', {\n              day: 'numeric',\n              month: 'long',\n              year: 'numeric'\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2623,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: 'grey.800'\n            },\n            children: item.description.length > 80 ? item.description.substring(0, 80) + '...' : item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2624,\n            columnNumber: 21\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2621,\n          columnNumber: 19\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2614,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2582,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 2543,\n    columnNumber: 5\n  }, this);\n}\n_s2(LandingPage, \"a8/YTx3V4hFvtXAjjEUd8fYE8nE=\", false, function () {\n  return [useNavigate, useAuth, useTheme, useMediaQuery];\n});\n_c8 = LandingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"Toast\");\n$RefreshReg$(_c2, \"PopularTags\");\n$RefreshReg$(_c3, \"LatestPosts\");\n$RefreshReg$(_c4, \"BreakingNewsSlider\");\n$RefreshReg$(_c5, \"NewsCard\");\n$RefreshReg$(_c6, \"MobileNewsLayout\");\n$RefreshReg$(_c7, \"DesktopNewsLayout\");\n$RefreshReg$(_c8, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "Drawer", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Avatar", "Divider", "MenuIcon", "Footer", "useMediaQuery", "useTheme", "createTheme", "ThemeProvider", "ShareIcon", "BookmarkAddIcon", "BookmarkAddedIcon", "FavoriteIcon", "FavoriteBorderIcon", "IconButton", "<PERSON><PERSON><PERSON>", "CloseIcon", "TextField", "SearchIcon", "NavigateNextIcon", "NavigateBeforeIcon", "CheckCircleIcon", "LoginIcon", "PersonAddIcon", "LogoutIcon", "AccountCircleIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getImageUrl", "imagePath", "console", "log", "startsWith", "filename", "replace", "includes", "split", "pop", "possiblePaths", "finalUrl", "categories", "Toast", "message", "isVisible", "onClose", "sx", "position", "top", "xs", "md", "right", "zIndex", "bgcolor", "color", "px", "py", "borderRadius", "boxShadow", "display", "alignItems", "gap", "transform", "transition", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "_c", "PopularTags", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "categoriesData", "popularTags", "length", "map", "cat", "name", "count", "post_count", "p", "mb", "border", "borderColor", "width", "height", "mr", "flexWrap", "justifyContent", "tag", "index", "label", "onClick", "cursor", "_c2", "LatestPosts", "newsData", "formatTime", "dateString", "date", "Date", "toLocaleTimeString", "hour", "minute", "latestPosts", "slice", "spacing", "post", "flex", "lineHeight", "overflow", "textOverflow", "WebkitLineClamp", "WebkitBoxOrient", "title", "category_name", "category", "created_at", "flexShrink", "component", "src", "image", "alt", "objectFit", "onError", "e", "target", "id", "_c3", "BreakingNewsSlider", "kostum", "_s", "currentSlide", "setCurrentSlide", "autoPlay", "setAutoPlay", "useEffect", "style", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "sliderNews", "interval", "setInterval", "prev", "clearInterval", "handleNext", "handlePrev", "textAlign", "left", "animation", "news", "opacity", "background", "backgroundSize", "backgroundPosition", "textShadow", "sm", "bottom", "_", "_c4", "NewsCard", "expanded", "onExpand", "formatDate", "truncateText", "onShare", "onBookmark", "isBookmarked", "onNewsClick", "isAuthenticated", "onLike", "isLiked", "aspectRatio", "handleNewsClick", "closest", "flexDirection", "mx", "paddingTop", "mt", "size", "stopPropagation", "pb", "pt", "description", "alignSelf", "textTransform", "_c5", "MobileNewsLayout", "popularNews", "expandedCards", "handleCardExpand", "loading", "bottomNav", "setBottomNav", "handleSidebar", "sidebarOpen", "handleSidebarClose", "handleShare", "bookmarkedNews", "onSearchClick", "handleBottomNavChange", "openSearch", "navigate", "user", "handleLogout", "likedNews", "text", "max<PERSON><PERSON><PERSON>", "substring", "toLocaleDateString", "day", "month", "year", "filteredNews", "filter", "minHeight", "elevation", "borderBottom", "flexGrow", "logo", "edge", "window", "location", "href", "className", "anchor", "open", "ModalProps", "keepMounted", "letterSpacing", "fullWidth", "backgroundColor", "marginRight", "email", "startIcon", "direction", "overflowX", "scrollbarWidth", "msOverflowStyle", "whiteSpace", "overflowY", "pr", "Array", "from", "idx", "has", "borderTop", "primary_color", "_c6", "DesktopNewsLayout", "ml", "item", "views", "_c7", "createCustomTheme", "palette", "primary", "main", "secondary", "secondary_color", "default", "background_color", "text_color", "LandingPage", "_s2", "logout", "setSidebarOpen", "setKostum", "accent_color", "setNewsData", "setPopularNews", "setCategoriesData", "setLoading", "setExpandedCards", "searchActive", "setSearchActive", "searchQuery", "setSearch<PERSON>uery", "setBookmarkedNews", "Set", "setLikedNews", "toast", "setToast", "visible", "urlParams", "URLSearchParams", "search", "kate<PERSON>i", "get", "beritaId", "theme", "isDesktop", "breakpoints", "up", "fetch", "method", "catch", "err", "error", "urlTitle", "toLowerCase", "trim", "link", "origin", "navigator", "clipboard", "writeText", "prevData", "share", "setTimeout", "handleLike", "isCurrentlyLiked", "newLikedNews", "delete", "add", "response", "data", "json", "success", "finalLikedNews", "liked", "prevNews", "likes", "handleBookmark", "isCurrentlyBookmarked", "action", "headers", "body", "JSON", "stringify", "post_id", "ok", "Error", "status", "result", "newSet", "closeSearch", "filteredSearch", "n", "handle<PERSON>ey", "key", "addEventListener", "removeEventListener", "then", "res", "logoPath", "btoa", "website_logo", "website_name", "website_description", "footer_text", "contact_email", "social_facebook", "social_twitter", "social_instagram", "social_youtube", "meta_keywords", "meta_description", "metaDescription", "querySelector", "setAttribute", "metaKeywords", "faviconUrl", "favicon", "rel", "appleTouchIcon", "refreshSettings", "useCallback", "now", "handleStorageChange", "isArray", "mappedCategories", "slug", "is_active", "updated_at", "activeCategories", "mappedData", "_item$content", "excerpt", "content", "image_alt", "category_id", "category_color", "featured", "tags", "comments_count", "reading_time", "published_at", "author", "author_name", "full_name", "user_id", "publishedPosts", "popular", "sort", "a", "b", "scoreA", "scoreB", "dummyNews", "toISOString", "loadBookmarkedNews", "bookmarkedIds", "loadLikedPosts", "likedIds", "parseInt", "ip_address", "hasApiCounts", "some", "postCounts", "for<PERSON>ach", "categoryId", "updatedCategories", "cardId", "newsId", "handleSearchClick", "newValue", "customTheme", "<PERSON><PERSON>ilter", "autoFocus", "value", "onChange", "placeholder", "borderWidth", "InputProps", "startAdornment", "maxHeight", "_c8", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/LandingPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Drawer from '@mui/material/Drawer';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Typography from '@mui/material/Typography';\nimport Box from '@mui/material/Box';\nimport Button from '@mui/material/Button';\nimport Chip from '@mui/material/Chip';\nimport Stack from '@mui/material/Stack';\nimport Card from '@mui/material/Card';\nimport CardContent from '@mui/material/CardContent';\nimport CardMedia from '@mui/material/CardMedia';\nimport Avatar from '@mui/material/Avatar';\nimport Divider from '@mui/material/Divider';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport Footer from './components/Footer';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme, createTheme, ThemeProvider } from '@mui/material/styles';\nimport ShareIcon from '@mui/icons-material/Share';\nimport BookmarkAddIcon from '@mui/icons-material/BookmarkAdd';\nimport BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport IconButton from '@mui/material/IconButton';\nimport Tooltip from '@mui/material/Tooltip';\nimport CloseIcon from '@mui/icons-material/Close';\nimport TextField from '@mui/material/TextField';\nimport SearchIcon from '@mui/icons-material/Search';\nimport NavigateNextIcon from '@mui/icons-material/NavigateNext';\nimport NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport LoginIcon from '@mui/icons-material/Login';\nimport PersonAddIcon from '@mui/icons-material/PersonAdd';\nimport LogoutIcon from '@mui/icons-material/Logout';\nimport AccountCircleIcon from '@mui/icons-material/AccountCircle';\n\n// Helper function to get correct image URL - Use standardized uploads folder\nconst getImageUrl = (imagePath) => {\n  if (!imagePath) {\n    return 'https://source.unsplash.com/300x200/?news'; // Fallback to Unsplash\n  }\n\n  console.log('LandingPage - Processing image path:', imagePath);\n\n  // If it's already a full URL, return as is\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n    console.log('LandingPage - Using full URL:', imagePath);\n    return imagePath;\n  }\n\n  // Extract filename from any path format\n  let filename = '';\n\n  if (imagePath.startsWith('/react-news/uploads/')) {\n    filename = imagePath.replace('/react-news/uploads/', '');\n  } else if (imagePath.startsWith('/react-news/frontend/uploads/')) {\n    filename = imagePath.replace('/react-news/frontend/uploads/', '');\n  } else if (imagePath.startsWith('/uploads/')) {\n    filename = imagePath.replace('/uploads/', '');\n  } else if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {\n    filename = imagePath.replace('frontend/src/pages/admin/uploads/', '');\n  } else if (imagePath.startsWith('uploads/')) {\n    filename = imagePath.replace('uploads/', '');\n  } else if (imagePath.startsWith('assets/news/')) {\n    filename = imagePath.replace('assets/news/', '');\n  } else if (!imagePath.includes('/')) {\n    // Just filename\n    filename = imagePath;\n  } else {\n    // Extract filename from any other path\n    filename = imagePath.split('/').pop();\n  }\n\n  // Try multiple possible paths for image location\n  const possiblePaths = [\n    `http://localhost/uploads/${filename}`,                    // Root uploads (standardized)\n    `http://localhost/react-news/uploads/${filename}`,        // React-news uploads\n    `http://localhost/react-news/frontend/uploads/${filename}` // Frontend uploads\n  ];\n\n  console.log('LandingPage - Trying paths for filename:', filename);\n  console.log('LandingPage - Possible paths:', possiblePaths);\n\n  // For now, return the first path (root uploads) as the primary choice\n  const finalUrl = possiblePaths[0];\n  console.log('LandingPage - Final URL:', finalUrl);\n  return finalUrl;\n};\n\n// Popular news will be fetched from database\n\nconst categories = [\n  'Semua', 'Umum', 'Teknologi', 'Bisnis', 'Olahraga', 'Hiburan', 'Politik', 'Kesehatan'\n];\n\nfunction Toast({ message, isVisible, onClose }) {\n  if (!isVisible) return null;\n  \n  return (\n    <Box\n      sx={{\n        position: 'fixed',\n        top: { xs: 80, md: 100 },\n        right: { xs: 16, md: 24 },\n        zIndex: 9999,\n        bgcolor: '#10b981',\n        color: 'white',\n        px: 3,\n        py: 2,\n        borderRadius: 2,\n        boxShadow: 4,\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1,\n        transform: 'translateX(0)',\n        transition: 'transform 0.3s ease-in-out',\n        maxWidth: { xs: 'calc(100vw - 32px)', md: 400 },\n        minWidth: { xs: 280, md: 320 }\n      }}\n    >\n      <CheckCircleIcon sx={{ fontSize: 20 }} />\n      <Typography variant=\"body2\" sx={{ fontWeight: 600, fontSize: { xs: 13, md: 14 } }}>\n        {message}\n      </Typography>\n    </Box>\n  );\n}\n\nfunction PopularTags({ selectedCategory, setSelectedCategory, categoriesData }) {\n  const popularTags = categoriesData.length > 0 ? categoriesData.map(cat => ({\n    name: cat.name,\n    count: cat.post_count || 0,\n    color: cat.color || '#3B82F6'\n  })) : [\n    { name: 'Umum', count: 0, color: '#6B7280' },\n    { name: 'Teknologi', count: 0, color: '#3B82F6' },\n    { name: 'Bisnis', count: 0, color: '#10B981' },\n    { name: 'Olahraga', count: 0, color: '#F59E0B' },\n    { name: 'Hiburan', count: 0, color: '#EF4444' },\n    { name: 'Politik', count: 0, color: '#8B5CF6' },\n    { name: 'Kesehatan', count: 0, color: '#06B6D4' }\n  ];\n\n  return (\n    <Box sx={{ \n      bgcolor: '#fff', \n      borderRadius: 3, \n      p: 3, \n      mb: 3, \n      boxShadow: 2,\n      border: '1px solid',\n      borderColor: 'grey.200'\n    }}>\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n        <Box sx={{ \n          width: 4, \n          height: 20, \n          bgcolor: 'primary.main', \n          borderRadius: 2, \n          mr: 2 \n        }} />\n        <Typography variant=\"h6\" sx={{ \n          fontWeight: 700, \n          color: 'grey.800',\n          fontSize: { xs: 16, md: 18 }\n        }}>\n          Tag Terpopuler\n        </Typography>\n      </Box>\n      \n      <Box sx={{ \n        display: 'flex', \n        flexWrap: 'wrap', \n        gap: 1.5,\n        justifyContent: { xs: 'center', md: 'flex-start' }\n      }}>\n        {popularTags.map((tag, index) => (\n          <Chip\n            key={tag.name}\n            label={`${tag.name} (${tag.count})`}\n            onClick={() => setSelectedCategory(tag.name)}\n            sx={{\n              bgcolor: selectedCategory === tag.name ? tag.color : 'grey.100',\n              color: selectedCategory === tag.name ? 'white' : 'grey.700',\n              fontWeight: 600,\n              fontSize: { xs: 12, md: 13 },\n              px: 2,\n              py: 1,\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                bgcolor: selectedCategory === tag.name ? tag.color : 'grey.200',\n                transform: 'translateY(-2px)',\n                boxShadow: 2\n              },\n              border: selectedCategory === tag.name ? 'none' : '1px solid',\n              borderColor: 'grey.300'\n            }}\n          />\n        ))}\n      </Box>\n    </Box>\n  );\n}\n\nfunction LatestPosts({ newsData }) {\n  const formatTime = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });\n  };\n\n  // Get latest 4 posts\n  const latestPosts = newsData.slice(0, 4);\n\n  return (\n    <Box sx={{ \n      bgcolor: '#fff', \n      borderRadius: 3, \n      p: 3, \n      mb: 3, \n      boxShadow: 2,\n      border: '1px solid',\n      borderColor: 'grey.200'\n    }}>\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ \n          width: 4, \n          height: 20, \n          bgcolor: 'primary.main', \n          borderRadius: 2, \n          mr: 2 \n        }} />\n        <Typography variant=\"h6\" sx={{ \n          fontWeight: 700, \n          color: 'grey.800',\n          fontSize: { xs: 16, md: 18 }\n        }}>\n          Latest Posts\n        </Typography>\n      </Box>\n      \n      <Stack spacing={2}>\n        {latestPosts.map((post, index) => (\n          <Box key={post.id || index} sx={{ \n            display: 'flex', \n            alignItems: 'center', \n            gap: 2,\n            p: 2,\n            borderRadius: 2,\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            '&:hover': {\n              bgcolor: 'grey.50',\n              transform: 'translateX(4px)'\n            }\n          }}>\n            {/* Text Content - Left Side */}\n            <Box sx={{ flex: 1, minWidth: 0 }}>\n              <Typography variant=\"subtitle2\" sx={{ \n                fontWeight: 600, \n                color: 'grey.900', \n                lineHeight: 1.3,\n                mb: 0.5,\n                fontSize: { xs: 13, md: 14 },\n                overflow: 'hidden',\n                textOverflow: 'ellipsis',\n                display: '-webkit-box',\n                WebkitLineClamp: 2,\n                WebkitBoxOrient: 'vertical'\n              }}>\n                {post.title}\n              </Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>\n                <Typography variant=\"caption\" sx={{\n                  color: 'primary.main',\n                  fontWeight: 600,\n                  fontSize: { xs: 10, md: 11 }\n                }}>\n                  {post.category_name || post.category || 'Umum'}\n                </Typography>\n                <Typography variant=\"caption\" sx={{ color: 'grey.500', fontSize: { xs: 10, md: 11 } }}>\n                  •\n                </Typography>\n                <Typography variant=\"caption\" sx={{\n                  color: 'grey.600',\n                  fontSize: { xs: 10, md: 11 }\n                }}>\n                  {formatTime(post.created_at || post.date)}\n                </Typography>\n              </Box>\n            </Box>\n            \n            {/* Image - Right Side */}\n            <Box sx={{ \n              width: { xs: 60, md: 80 }, \n              height: { xs: 60, md: 80 }, \n              borderRadius: 2, \n              overflow: 'hidden',\n              flexShrink: 0\n            }}>\n              <Box\n                component=\"img\"\n                src={getImageUrl(post.image)}\n                alt={post.title}\n                sx={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover'\n                }}\n                onError={(e) => { e.target.src = 'https://source.unsplash.com/100x100/?news'; }}\n              />\n            </Box>\n          </Box>\n        ))}\n      </Stack>\n    </Box>\n  );\n}\n\nfunction BreakingNewsSlider({ newsData, kostum }) {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [autoPlay, setAutoPlay] = useState(true);\n\n  // Add CSS animation for pulse effect\n  React.useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes pulse {\n        0% { opacity: 1; }\n        50% { opacity: 0.5; }\n        100% { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(style);\n    return () => document.head.removeChild(style);\n  }, []);\n\n  // Get latest 5 news for slider\n  const sliderNews = newsData.slice(0, 5);\n\n  React.useEffect(() => {\n    if (!autoPlay || sliderNews.length <= 1) return;\n    \n    const interval = setInterval(() => {\n      setCurrentSlide((prev) => (prev + 1) % sliderNews.length);\n    }, 5000); // Change slide every 5 seconds\n\n    return () => clearInterval(interval);\n  }, [autoPlay, sliderNews.length]);\n\n  const handleNext = () => {\n    setCurrentSlide((prev) => (prev + 1) % sliderNews.length);\n    setAutoPlay(false);\n  };\n\n  const handlePrev = () => {\n    setCurrentSlide((prev) => (prev - 1 + sliderNews.length) % sliderNews.length);\n    setAutoPlay(false);\n  };\n\n  const formatTime = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });\n  };\n\n  if (sliderNews.length === 0) {\n    return (\n      <Box sx={{ \n        position: 'relative', \n        width: '100%', \n        height: { xs: 200, md: 400 }, \n        borderRadius: 3, \n        overflow: 'hidden',\n        mb: 2,\n        boxShadow: 3,\n        bgcolor: 'grey.100',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      }}>\n        <Box sx={{ textAlign: 'center', color: 'grey.600' }}>\n          <Typography variant=\"h6\" sx={{ mb: 1, fontWeight: 600 }}>\n            Belum ada berita\n          </Typography>\n          <Typography variant=\"body2\" sx={{ color: 'grey.500' }}>\n            Berita akan muncul di sini\n          </Typography>\n        </Box>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ \n      position: 'relative', \n      width: '100%', \n      height: { xs: 200, md: 400 }, // 1920x1080 aspect ratio for desktop\n      borderRadius: 3, \n      overflow: 'hidden',\n      mb: 2,\n      boxShadow: 3\n    }}>\n      {/* Breaking News Badge */}\n      <Box sx={{\n        position: 'absolute',\n        top: 16,\n        left: 16,\n        zIndex: 3,\n        bgcolor: 'error.main',\n        color: 'white',\n        px: 2,\n        py: 0.5,\n        borderRadius: 2,\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1\n      }}>\n        <Box sx={{ \n          width: 8, \n          height: 8, \n          borderRadius: '50%', \n          bgcolor: 'white',\n          animation: 'pulse 1.5s infinite'\n        }} />\n        <Typography variant=\"caption\" sx={{ fontWeight: 700, fontSize: 12 }}>\n          BREAKING NEWS\n        </Typography>\n      </Box>\n\n      {/* Slides */}\n      <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>\n        {sliderNews.map((news, index) => (\n          <Box\n            key={news.id || index}\n            sx={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '100%',\n              opacity: index === currentSlide ? 1 : 0,\n              transition: 'opacity 0.5s ease-in-out',\n              display: 'flex',\n              alignItems: 'center'\n            }}\n          >\n            <Box sx={{ \n              position: 'relative', \n              width: '100%', \n              height: '100%',\n              background: `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6)), url(${getImageUrl(news.image)})`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              display: 'flex',\n              alignItems: 'flex-end',\n              p: 3\n            }}>\n              <Box sx={{ color: 'white', width: '100%' }}>\n                <Typography variant=\"h6\" sx={{ \n                  fontWeight: 700, \n                  mb: 1,\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontSize: { xs: 16, sm: 18, md: 24 }\n                }}>\n                  {news.title}\n                </Typography>\n                <Typography variant=\"caption\" sx={{ \n                  color: 'grey.300',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontSize: { xs: 12, md: 14 }\n                }}>\n                  {formatTime(news.date)} • {news.category}\n                </Typography>\n              </Box>\n            </Box>\n          </Box>\n        ))}\n      </Box>\n\n      {/* Navigation Arrows */}\n      {sliderNews.length > 1 && (\n        <>\n          <IconButton\n            onClick={handlePrev}\n            sx={{\n              position: 'absolute',\n              left: 8,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: 'white',\n              '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },\n              zIndex: 2\n            }}\n          >\n            <NavigateBeforeIcon />\n          </IconButton>\n          <IconButton\n            onClick={handleNext}\n            sx={{\n              position: 'absolute',\n              right: 8,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: 'white',\n              '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },\n              zIndex: 2\n            }}\n          >\n            <NavigateNextIcon />\n          </IconButton>\n        </>\n      )}\n\n      {/* Dots Indicator */}\n      {sliderNews.length > 1 && (\n        <Box sx={{\n          position: 'absolute',\n          bottom: 16,\n          left: '50%',\n          transform: 'translateX(-50%)',\n          display: 'flex',\n          gap: 1,\n          zIndex: 2\n        }}>\n          {sliderNews.map((_, index) => (\n            <Box\n              key={index}\n              onClick={() => {\n                setCurrentSlide(index);\n                setAutoPlay(false);\n              }}\n              sx={{\n                width: 8,\n                height: 8,\n                borderRadius: '50%',\n                bgcolor: index === currentSlide ? 'white' : 'rgba(255,255,255,0.5)',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              }}\n            />\n          ))}\n        </Box>\n      )}\n    </Box>\n  );\n}\n\nfunction NewsCard({ news, expanded, onExpand, formatDate, truncateText, variant, onShare, onBookmark, isBookmarked, onNewsClick, isAuthenticated, onLike, isLiked }) {\n  const aspectRatio = variant === 'desktop' ? '56.25%' : '100%';\n\n  const handleNewsClick = (e) => {\n    // Prevent navigation if clicking on buttons/icons\n    if (e.target.closest('button') || e.target.closest('[role=\"button\"]')) {\n      return;\n    }\n    onNewsClick && onNewsClick(news.id);\n  };\n\n  return (\n    <Card\n      sx={{\n        display: 'flex',\n        flexDirection: 'column',\n        height: '100%',\n        position: 'relative',\n        minWidth: { xs: '90vw', sm: 320, md: 340 },\n        maxWidth: { xs: '100vw', sm: 400, md: 420 },\n        mx: 'auto',\n        borderRadius: 4,\n        boxShadow: 3,\n        p: 1,\n        bgcolor: '#fff',\n        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',\n        '&:hover': {\n          transform: 'translateY(-4px)',\n          boxShadow: 6,\n        },\n      }}\n    >\n      {/* Clickable Image Area */}\n      <Box\n        onClick={handleNewsClick}\n        sx={{\n          position: 'relative',\n          width: '100%',\n          paddingTop: aspectRatio,\n          borderRadius: 3,\n          overflow: 'hidden',\n          cursor: 'pointer'\n        }}\n      >\n        <CardMedia\n          component=\"img\"\n          sx={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', objectFit: 'cover' }}\n          image={getImageUrl(news.image)}\n          alt={news.title}\n          onError={(e) => { e.target.src = variant === 'desktop' ? 'https://source.unsplash.com/1920x1080/?news' : 'https://source.unsplash.com/900x900/?news'; }}\n        />\n      </Box>\n      {/* Like, Bookmark and Share icons kanan bawah */}\n      <Box sx={{ position: 'absolute', right: 20, bottom: 20, zIndex: 2, mt: 2, display: 'flex', gap: 1 }}>\n        <Tooltip title={isLiked ? \"Hapus like\" : \"Suka berita ini\"}>\n          <IconButton\n            color={isLiked ? \"error\" : \"default\"}\n            size=\"medium\"\n            onClick={(e) => {\n              e.stopPropagation(); // Prevent card click\n              onLike(news);\n            }}\n            sx={{\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                transform: 'scale(1.1)',\n                color: '#e91e63'\n              },\n              '&:active': {\n                transform: 'scale(0.95)',\n              },\n              ...(isLiked && {\n                animation: 'heartBeat 0.6s ease-in-out',\n                '@keyframes heartBeat': {\n                  '0%': { transform: 'scale(1)' },\n                  '14%': { transform: 'scale(1.3)' },\n                  '28%': { transform: 'scale(1)' },\n                  '42%': { transform: 'scale(1.3)' },\n                  '70%': { transform: 'scale(1)' }\n                }\n              })\n            }}\n          >\n            {isLiked ? (\n              <FavoriteIcon fontSize=\"medium\" sx={{ color: '#e91e63' }} />\n            ) : (\n              <FavoriteBorderIcon fontSize=\"medium\" />\n            )}\n          </IconButton>\n        </Tooltip>\n        <Tooltip title={\n          !isAuthenticated ? \"Login untuk menyimpan berita\" :\n          isBookmarked ? \"Hapus dari bookmark\" : \"Tambah ke bookmark\"\n        }>\n          <IconButton\n            color={isBookmarked ? \"success\" : \"primary\"}\n            size=\"medium\"\n            onClick={(e) => {\n              e.stopPropagation(); // Prevent card click\n              onBookmark(news);\n            }}\n            sx={{\n              transition: 'all 0.3s ease',\n              '&:hover': { transform: 'scale(1.1)' }\n            }}\n          >\n            {isBookmarked ? (\n              <BookmarkAddedIcon fontSize=\"medium\" />\n            ) : (\n              <BookmarkAddIcon fontSize=\"medium\" />\n            )}\n          </IconButton>\n        </Tooltip>\n        <Tooltip title=\"Bagikan\">\n          <IconButton\n            color=\"primary\"\n            size=\"medium\"\n            onClick={(e) => {\n              e.stopPropagation(); // Prevent card click\n              onShare(news);\n            }}\n          >\n            <ShareIcon fontSize=\"medium\" />\n          </IconButton>\n        </Tooltip>\n      </Box>\n      {/* Clickable Content Area */}\n      <CardContent\n        onClick={handleNewsClick}\n        sx={{\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          pb: 2,\n          pt: 2,\n          cursor: 'pointer'\n        }}\n      >\n        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', mb: 0.5, fontSize: { xs: 18, md: 22 } }}>{news.title}</Typography>\n        <Typography variant=\"caption\" sx={{ color: 'grey.600', mb: 1, fontSize: { xs: 13, md: 15 } }}>\n          Kategori: <Typography component=\"span\" sx={{ fontWeight: 600, color: 'secondary.main' }}>{news.category_name || news.category || 'Umum'}</Typography> &bull; {formatDate(news.created_at || news.date)}\n        </Typography>\n        <Box sx={{ mb: 2, mr: 12 }}> {/* Add right margin to avoid icon overlap */}\n          <Typography\n            variant=\"body2\"\n            sx={{\n              color: 'grey.800',\n              fontSize: { xs: 15, md: 16 },\n              lineHeight: 1.5,\n              display: '-webkit-box',\n              WebkitLineClamp: expanded ? 'none' : 2,\n              WebkitBoxOrient: 'vertical',\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              mb: 1\n            }}\n          >\n            {news.description}\n          </Typography>\n          {news.description.length > 100 && (\n            <Button\n              size=\"small\"\n              color=\"primary\"\n              onClick={(e) => {\n                e.stopPropagation(); // Prevent card click\n                onExpand();\n              }}\n              sx={{\n                alignSelf: 'flex-start',\n                textTransform: 'none',\n                fontWeight: 600,\n                fontSize: '0.8rem',\n                p: 0.5,\n                minWidth: 'auto'\n              }}\n            >\n              {expanded ? 'Baca Lebih Sedikit' : 'Baca Selengkapnya'}\n            </Button>\n          )}\n        </Box>\n      </CardContent>\n    </Card>\n  );\n}\n\nfunction MobileNewsLayout({ kostum, newsData, popularNews, categoriesData, selectedCategory, setSelectedCategory, expandedCards, handleCardExpand, loading, bottomNav, setBottomNav, handleSidebar, sidebarOpen, handleSidebarClose, handleShare, onBookmark, bookmarkedNews, onSearchClick, handleBottomNavChange, onNewsClick, openSearch, navigate, user, isAuthenticated, handleLogout, onLike, likedNews }) {\n  const truncateText = (text, maxLength = 100) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });\n  };\n  const filteredNews = newsData.filter(news =>\n    selectedCategory === 'Semua' ||\n    news.category === selectedCategory ||\n    news.category_name === selectedCategory\n  ).slice(0, 6);\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', width: '100vw', overflow: 'hidden' }}>\n      {/* Navbar (fixed) */}\n      <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n        <Toolbar sx={{ minHeight: 70, px: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n            <Avatar\n              src={kostum.logo || '/logo192.png'}\n              alt=\"Logo\"\n              sx={{\n                width: 48,\n                height: 48,\n                mr: 2,\n                cursor: 'pointer'\n              }}\n              onError={(e) => {\n                console.log('Logo error, using fallback');\n                e.target.src = '/logo192.png';\n              }}\n              onClick={() => navigate('/')}\n            />\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontWeight: 700,\n                color: 'primary.main',\n                fontSize: 22,\n                cursor: 'pointer'\n              }}\n              onClick={() => navigate('/')}\n            >\n              {kostum.title || 'React News Portal'}\n            </Typography>\n          </Box>\n          <IconButton edge=\"end\" color=\"primary\" onClick={() => window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-player.php'} sx={{ mr: 1 }} title=\"Video\">\n            <i className=\"fas fa-play-circle\" style={{ fontSize: 24 }}></i>\n          </IconButton>\n          <IconButton edge=\"end\" color=\"primary\" onClick={onSearchClick} sx={{ mr: 1 }}>\n            <SearchIcon fontSize=\"large\" />\n          </IconButton>\n          <IconButton edge=\"end\" color=\"primary\" onClick={handleSidebar}>\n            <MenuIcon fontSize=\"large\" />\n          </IconButton>\n        </Toolbar>\n      </AppBar>\n      {/* Sidebar Drawer (Mobile) */}\n      <Drawer\n        anchor=\"right\"\n        open={sidebarOpen}\n        onClose={handleSidebarClose}\n        ModalProps={{ keepMounted: true }}\n        sx={{ zIndex: 2000 }}\n      >\n        <Box sx={{ width: 260, p: 3, position: 'relative' }}>\n          <IconButton\n            onClick={handleSidebarClose}\n            sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n            aria-label=\"Tutup\"\n          >\n            <CloseIcon />\n          </IconButton>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <Avatar src={kostum.logo} alt=\"Logo\" sx={{ width: 32, height: 32, mr: 1 }} onError={(e) => { e.target.src = '/logo192.png'; }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>{kostum.title}</Typography>\n          </Box>\n          <Divider sx={{ mb: 2 }} />\n\n          {/* Quick Actions */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle2\" sx={{\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            }}>\n              Quick Actions\n            </Typography>\n            <Stack spacing={1.5}>\n              <Button\n                variant=\"text\"\n                fullWidth\n                sx={{\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                }}\n                onClick={() => {\n                  if (isAuthenticated) {\n                    window.location.href = '/saved';\n                  } else {\n                    navigate('/auth/login');\n                  }\n                }}\n              >\n                <i className=\"fas fa-bookmark\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                Berita Tersimpan\n              </Button>\n              <Button\n                variant=\"text\"\n                fullWidth\n                sx={{\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                }}\n                onClick={() => {\n                  handleSidebarClose();\n                  openSearch();\n                }}\n              >\n                <i className=\"fas fa-search\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                Cari Berita\n              </Button>\n              <Button\n                variant=\"text\"\n                fullWidth\n                sx={{\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                }}\n                onClick={() => {\n                  handleSidebarClose();\n                  window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-player.php';\n                }}\n              >\n                <i className=\"fas fa-play-circle\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                Video\n              </Button>\n            </Stack>\n          </Box>\n\n          <Divider sx={{ mb: 2 }} />\n\n          {/* Authentication */}\n          <Box>\n            <Typography variant=\"subtitle2\" sx={{\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            }}>\n              Account\n            </Typography>\n            <Stack spacing={2}>\n              {isAuthenticated ? (\n                // Logged in user\n                <>\n                  <Box sx={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2,\n                    p: 2,\n                    bgcolor: 'grey.50',\n                    borderRadius: 2,\n                    border: '1px solid',\n                    borderColor: 'grey.200'\n                  }}>\n                    <AccountCircleIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n                    <Box>\n                      <Typography variant=\"subtitle2\" fontWeight={600}>\n                        {user?.name || 'User'}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {user?.email || '<EMAIL>'}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  <Button\n                    variant=\"outlined\"\n                    color=\"error\"\n                    fullWidth\n                    startIcon={<LogoutIcon />}\n                    sx={{\n                      textTransform: 'none',\n                      borderColor: 'error.main',\n                      color: 'error.main',\n                      '&:hover': {\n                        borderColor: 'error.main',\n                        backgroundColor: 'error.main',\n                        color: 'white'\n                      }\n                    }}\n                    onClick={handleLogout}\n                  >\n                    Logout\n                  </Button>\n                </>\n              ) : (\n                // Not logged in\n                <>\n                  <Button\n                    variant=\"outlined\"\n                    color=\"primary\"\n                    fullWidth\n                    startIcon={<LoginIcon />}\n                    sx={{\n                      textTransform: 'none',\n                      borderColor: 'primary.main',\n                      color: 'primary.main',\n                      '&:hover': {\n                        borderColor: 'primary.main',\n                        backgroundColor: 'primary.main',\n                        color: 'white'\n                      }\n                    }}\n                    onClick={() => navigate('/auth/login')}\n                  >\n                    Login\n                  </Button>\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    fullWidth\n                    startIcon={<PersonAddIcon />}\n                    sx={{\n                      textTransform: 'none',\n                      backgroundColor: 'primary.main',\n                      '&:hover': {\n                        backgroundColor: 'primary.dark'\n                      }\n                    }}\n                    onClick={() => navigate('/auth/register')}\n                  >\n                    Register\n                  </Button>\n                </>\n              )}\n            </Stack>\n          </Box>\n        </Box>\n      </Drawer>\n      {/* Category Slider (fixed) */}\n      <Box sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', py: 2, position: 'fixed', top: 60, left: 0, right: 0, zIndex: 1301 }}>\n        <Stack direction=\"row\" spacing={2} sx={{ overflowX: 'auto', px: 2, '&::-webkit-scrollbar': { display: 'none' }, scrollbarWidth: 'none', msOverflowStyle: 'none' }}>\n          {categories.map((cat) => (\n            <Chip\n              key={cat}\n              label={cat}\n              color={selectedCategory === cat ? 'primary' : 'default'}\n              onClick={() => setSelectedCategory(cat)}\n              sx={{ fontWeight: 600, cursor: 'pointer', flexShrink: 0, whiteSpace: 'nowrap' }}\n            />\n          ))}\n        </Stack>\n      </Box>\n      {/* Main Content */}\n      <Box sx={{ position: 'absolute', top: 112, left: 0, right: 0, bottom: 70, px: 1, maxWidth: '100%', mx: 'auto', width: '100%', display: 'flex', flexDirection: 'column', minHeight: 0, zIndex: 1, pt: 2, pb: 2 }}>\n        <Box\n          component=\"main\"\n          sx={{\n            flex: 1,\n            height: 'calc(100vh - 112px - 70px)', // 112px = navbar+kategori, 70px = bottom nav\n            overflowY: 'auto',\n            minHeight: 0,\n            pt: 0,\n            pb: 8, // Add padding bottom for footer\n            '&::-webkit-scrollbar': { display: 'none' },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n          }}\n        >\n          {/* Breaking News Slider */}\n          <BreakingNewsSlider newsData={newsData} kostum={kostum} />\n          \n          {/* Popular Tags */}\n          <PopularTags selectedCategory={selectedCategory} setSelectedCategory={setSelectedCategory} categoriesData={categoriesData} />\n\n          {/* Latest Posts */}\n          <LatestPosts newsData={newsData} />\n          \n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 2,\n              overflowY: 'auto',\n              pb: { xs: 10, md: 0 }, // padding bawah ekstra di mobile\n              '&::-webkit-scrollbar': { display: 'none' },\n              scrollbarWidth: 'none',\n              msOverflowStyle: 'none',\n              px: 1,\n              pr: 2, // Add right padding to hide scrollbar\n            }}\n          >\n            {loading ? (\n              Array.from({ length: 6 }).map((_, idx) => (\n                <Card key={idx} sx={{ display: 'flex', flexDirection: 'column', height: '100%', width: '100%' }}>\n                  <Box sx={{ position: 'relative', width: '100%', paddingTop: '100%', bgcolor: 'grey.200' }} />\n                  <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', pb: 2 }}>\n                    <Box sx={{ height: 24, bgcolor: 'grey.200', mb: 1, borderRadius: 1 }} />\n                    <Box sx={{ height: 16, bgcolor: 'grey.100', mb: 1, borderRadius: 1 }} />\n                    <Box sx={{ height: 60, bgcolor: 'grey.100', borderRadius: 1 }} />\n                  </CardContent>\n                </Card>\n              ))\n            ) : filteredNews.length === 0 ? (\n              <Box sx={{ textAlign: 'center', py: 8, color: 'grey.500', width: '100%' }}>\n                <Typography variant=\"h6\">Belum ada berita</Typography>\n                <Typography variant=\"body2\">Berita akan muncul di sini</Typography>\n              </Box>\n            ) : (\n              filteredNews.map((news, idx) => (\n                <Box key={news.id || idx} sx={{ width: '100%' }}>\n                  <NewsCard\n                    news={news}\n                    expanded={!!expandedCards[news.id]}\n                    onExpand={() => handleCardExpand(news.id)}\n                    formatDate={formatDate}\n                    truncateText={truncateText}\n                    variant=\"mobile\"\n                    onShare={handleShare}\n                    onBookmark={onBookmark}\n                    isBookmarked={bookmarkedNews.has(news.id)}\n                    onNewsClick={onNewsClick}\n                    isAuthenticated={isAuthenticated}\n                    onLike={onLike}\n                    isLiked={likedNews.has(news.id)}\n                  />\n                </Box>\n              ))\n            )}\n\n            {/* Footer */}\n            <Box sx={{ mt: 4, py: 3, borderTop: '1px solid #e0e0e0', textAlign: 'center' }}>\n              <Footer title={kostum.title} />\n            </Box>\n          </Box>\n        </Box>\n      </Box>\n      {/* Custom Bottom Navigation */}\n      <Box sx={{\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: 'block',\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      }}>\n        <Box sx={{\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        }}>\n          <Box\n            onClick={() => handleBottomNavChange(0)}\n            className={`bottom-nav-item ${bottomNav === 0 ? 'active' : ''}`}\n          >\n            <i className=\"fas fa-home bottom-nav-icon\" style={{ color: bottomNav === 0 ? kostum.primary_color : '#6b7280' }}></i>\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomNav === 0 ? 'primary.main' : 'text.secondary' }}>\n              Home\n            </Typography>\n          </Box>\n\n          <Box\n            onClick={() => handleBottomNavChange(1)}\n            className={`bottom-nav-item ${bottomNav === 1 ? 'active' : ''}`}\n          >\n            <i className=\"fas fa-play-circle bottom-nav-icon\" style={{ color: bottomNav === 1 ? kostum.primary_color : '#6b7280' }}></i>\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomNav === 1 ? 'primary.main' : 'text.secondary' }}>\n              Video\n            </Typography>\n          </Box>\n\n          <Box\n            onClick={() => handleBottomNavChange(2)}\n            className={`bottom-nav-item ${bottomNav === 2 ? 'active' : ''}`}\n          >\n            <i className=\"fas fa-search bottom-nav-icon\" style={{ color: bottomNav === 2 ? kostum.primary_color : '#6b7280' }}></i>\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomNav === 2 ? 'primary.main' : 'text.secondary' }}>\n              Cari\n            </Typography>\n          </Box>\n\n          <Box\n            onClick={() => handleBottomNavChange(3)}\n            className={`bottom-nav-item ${bottomNav === 3 ? 'active' : ''}`}\n          >\n            <i className=\"fas fa-bookmark bottom-nav-icon\" style={{ color: bottomNav === 3 ? kostum.primary_color : '#6b7280' }}></i>\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomNav === 3 ? 'primary.main' : 'text.secondary' }}>\n              Simpan\n            </Typography>\n          </Box>\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\nfunction DesktopNewsLayout({ kostum, newsData, popularNews, categoriesData, selectedCategory, setSelectedCategory, expandedCards, handleCardExpand, loading, handleShare, onBookmark, bookmarkedNews, onSearchClick, sidebarOpen, handleSidebar, handleSidebarClose, onNewsClick, openSearch, navigate, user, isAuthenticated, handleLogout, onLike, likedNews }) {\n  const truncateText = (text, maxLength = 100) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });\n  };\n  const filteredNews = newsData.filter(news =>\n    selectedCategory === 'Semua' ||\n    news.category === selectedCategory ||\n    news.category_name === selectedCategory\n  ).slice(0, 6);\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', width: '100vw', overflow: 'hidden' }}>\n      <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n        <Toolbar sx={{ minHeight: 80, px: 6 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n            <Avatar src={kostum.logo} alt=\"Logo\" sx={{ width: 48, height: 48, mr: 2 }} onError={(e) => { e.target.src = '/logo192.png'; }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 28 }}>{kostum.title}</Typography>\n          </Box>\n          <IconButton edge=\"end\" color=\"primary\" onClick={() => navigate('/video')} sx={{ mr: 1 }} title=\"Video\">\n            <i className=\"fas fa-play-circle\" style={{ fontSize: 24 }}></i>\n          </IconButton>\n          <IconButton edge=\"end\" color=\"primary\" onClick={onSearchClick} sx={{ mr: 1 }}>\n            <SearchIcon fontSize=\"large\" />\n          </IconButton>\n          <IconButton edge=\"end\" color=\"primary\" onClick={handleSidebar} sx={{ ml: 1 }}>\n            <MenuIcon fontSize=\"large\" />\n          </IconButton>\n        </Toolbar>\n      </AppBar>\n      <Drawer\n        anchor=\"right\"\n        open={sidebarOpen}\n        onClose={handleSidebarClose}\n        ModalProps={{ keepMounted: true }}\n        sx={{ zIndex: 2000 }}\n      >\n        <Box sx={{ width: 260, p: 3, position: 'relative' }}>\n          <IconButton\n            onClick={handleSidebarClose}\n            sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n            aria-label=\"Tutup\"\n          >\n            <CloseIcon />\n          </IconButton>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <Avatar src={kostum.logo} alt=\"Logo\" sx={{ width: 32, height: 32, mr: 1 }} onError={(e) => { e.target.src = '/logo192.png'; }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>{kostum.title}</Typography>\n          </Box>\n          <Divider sx={{ mb: 2 }} />\n\n          {/* Quick Actions */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle2\" sx={{\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            }}>\n              Quick Actions\n            </Typography>\n            <Stack spacing={1.5}>\n              <Button\n                variant=\"text\"\n                fullWidth\n                sx={{\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                }}\n                onClick={() => {\n                  if (isAuthenticated) {\n                    window.location.href = '/saved';\n                  } else {\n                    navigate('/auth/login');\n                  }\n                }}\n              >\n                <i className=\"fas fa-bookmark\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                Berita Tersimpan\n              </Button>\n              <Button\n                variant=\"text\"\n                fullWidth\n                sx={{\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                }}\n                onClick={() => {\n                  handleSidebarClose();\n                  openSearch();\n                }}\n              >\n                <i className=\"fas fa-search\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                Cari Berita\n              </Button>\n              <Button\n                variant=\"text\"\n                fullWidth\n                sx={{\n                  justifyContent: 'flex-start',\n                  textTransform: 'none',\n                  color: 'text.primary',\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                }}\n                onClick={() => {\n                  handleSidebarClose();\n                  navigate('/video');\n                }}\n              >\n                <i className=\"fas fa-play-circle\" style={{ marginRight: 8, fontSize: 14 }}></i>\n                Video\n              </Button>\n            </Stack>\n          </Box>\n\n          <Divider sx={{ mb: 2 }} />\n\n          {/* Authentication */}\n          <Box>\n            <Typography variant=\"subtitle2\" sx={{\n              fontWeight: 600,\n              color: 'text.secondary',\n              mb: 1.5,\n              fontSize: 12,\n              textTransform: 'uppercase',\n              letterSpacing: 0.5\n            }}>\n              Account\n            </Typography>\n            <Stack spacing={2}>\n              {isAuthenticated ? (\n                // Logged in user\n                <>\n                  <Box sx={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 2,\n                    p: 2,\n                    bgcolor: 'grey.50',\n                    borderRadius: 2,\n                    border: '1px solid',\n                    borderColor: 'grey.200'\n                  }}>\n                    <AccountCircleIcon sx={{ color: 'primary.main', fontSize: 32 }} />\n                    <Box>\n                      <Typography variant=\"subtitle2\" fontWeight={600}>\n                        {user?.name || 'User'}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {user?.email || '<EMAIL>'}\n                      </Typography>\n                    </Box>\n                  </Box>\n                  <Button\n                    variant=\"outlined\"\n                    color=\"error\"\n                    fullWidth\n                    startIcon={<LogoutIcon />}\n                    sx={{\n                      textTransform: 'none',\n                      borderColor: 'error.main',\n                      color: 'error.main',\n                      '&:hover': {\n                        borderColor: 'error.main',\n                        backgroundColor: 'error.main',\n                        color: 'white'\n                      }\n                    }}\n                    onClick={handleLogout}\n                  >\n                    Logout\n                  </Button>\n                </>\n              ) : (\n                // Not logged in\n                <>\n                  <Button\n                    variant=\"outlined\"\n                    color=\"primary\"\n                    fullWidth\n                    startIcon={<LoginIcon />}\n                    sx={{\n                      textTransform: 'none',\n                      borderColor: 'primary.main',\n                      color: 'primary.main',\n                      '&:hover': {\n                        borderColor: 'primary.main',\n                        backgroundColor: 'primary.main',\n                        color: 'white'\n                      }\n                    }}\n                    onClick={() => navigate('/auth/login')}\n                  >\n                    Login\n                  </Button>\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    fullWidth\n                    startIcon={<PersonAddIcon />}\n                    sx={{\n                      textTransform: 'none',\n                      backgroundColor: 'primary.main',\n                      '&:hover': {\n                        backgroundColor: 'primary.dark'\n                      }\n                    }}\n                    onClick={() => navigate('/auth/register')}\n                  >\n                    Register\n                  </Button>\n                </>\n              )}\n            </Stack>\n          </Box>\n        </Box>\n      </Drawer>\n      <Box sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', py: 2, position: 'fixed', top: 70, left: 0, right: 0, zIndex: 1301 }}>\n        <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 4 }}>\n          <Stack direction=\"row\" spacing={2} sx={{ \n            overflowX: 'auto', \n            justifyContent: 'center',\n            '&::-webkit-scrollbar': { display: 'none' }, \n            scrollbarWidth: 'none', \n            msOverflowStyle: 'none' \n          }}>\n            {categories.map((cat) => (\n              <Chip\n                key={cat}\n                label={cat}\n                color={selectedCategory === cat ? 'primary' : 'default'}\n                onClick={() => setSelectedCategory(cat)}\n                sx={{ fontWeight: 600, cursor: 'pointer', flexShrink: 0, whiteSpace: 'nowrap' }}\n              />\n            ))}\n          </Stack>\n        </Box>\n      </Box>\n      <Box sx={{ position: 'absolute', top: 128, left: 0, right: 0, bottom: 0, px: 4, maxWidth: '1200px', mx: 'auto', width: '100%', display: 'flex', flexDirection: 'row', gap: 4, minHeight: 0, zIndex: 1, pt: 3, pb: 3 }}>\n        <Box\n          component=\"main\"\n          sx={{\n            flex: 1,\n            height: 'calc(100vh - 128px)', // 128px = navbar+kategori desktop\n            overflowY: 'auto',\n            minHeight: 0,\n            pt: 0,\n            pb: 8, // Add padding bottom for footer\n            '&::-webkit-scrollbar': { display: 'none' },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n          }}\n        >\n          {/* Breaking News Slider */}\n          <BreakingNewsSlider newsData={newsData} kostum={kostum} />\n          \n          {/* Popular Tags */}\n          <PopularTags selectedCategory={selectedCategory} setSelectedCategory={setSelectedCategory} categoriesData={categoriesData} />\n\n          {/* Latest Posts */}\n          <LatestPosts newsData={newsData} />\n          \n          <Box sx={{ \n            display: 'flex',\n            flexDirection: 'row',\n            gap: 4,\n            width: '100%',\n            maxWidth: '100%',\n            overflowX: 'auto',\n            '&::-webkit-scrollbar': { display: 'none' },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n            px: 1,\n            mt: 2,\n            pb: 2, // Add padding bottom to hide scrollbar\n          }}>\n            {loading ? (\n              Array.from({ length: 6 }).map((_, idx) => (\n                <Card key={idx} sx={{ display: 'flex', flexDirection: 'column', height: '100%', minWidth: 320, flexShrink: 0 }}>\n                  <Box sx={{ position: 'relative', width: '100%', paddingTop: '100%', bgcolor: 'grey.200' }} />\n                  <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', pb: 2 }}>\n                    <Box sx={{ height: 24, bgcolor: 'grey.200', mb: 1, borderRadius: 1 }} />\n                    <Box sx={{ height: 16, bgcolor: 'grey.100', mb: 1, borderRadius: 1 }} />\n                    <Box sx={{ height: 60, bgcolor: 'grey.100', borderRadius: 1 }} />\n                  </CardContent>\n                </Card>\n              ))\n            ) : filteredNews.length === 0 ? (\n              <Box sx={{ textAlign: 'center', py: 8, color: 'grey.500', width: '100%' }}>\n                <Typography variant=\"h6\">Belum ada berita</Typography>\n                <Typography variant=\"body2\">Berita akan muncul di sini</Typography>\n              </Box>\n            ) : (\n              filteredNews.map((news, idx) => (\n                <Box key={news.id || idx} sx={{ minWidth: 320, flexShrink: 0 }}>\n                                  <NewsCard\n                  news={news}\n                  expanded={!!expandedCards[news.id]}\n                  onExpand={() => handleCardExpand(news.id)}\n                  formatDate={formatDate}\n                  truncateText={truncateText}\n                  variant=\"desktop\"\n                  onShare={handleShare}\n                  onBookmark={onBookmark}\n                  isBookmarked={bookmarkedNews.has(news.id)}\n                  onNewsClick={onNewsClick}\n                  isAuthenticated={isAuthenticated}\n                  onLike={onLike}\n                  isLiked={likedNews.has(news.id)}\n                />\n                </Box>\n              ))\n            )}\n          </Box>\n        </Box>\n        {/* Aside: Popular News */}\n        <Box component=\"aside\" sx={{ width: 320, mt: 0, display: 'block', mb: 0 }}>\n          <Box sx={{ bgcolor: '#fff', borderRadius: 2, boxShadow: 1, p: 3, mb: 6, mt: 0 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', mb: 2 }}>Berita Populer</Typography>\n            <Divider sx={{ mb: 2 }} />\n            <Stack spacing={2}>\n              {popularNews.length > 0 ? (\n                popularNews.map((item, idx) => (\n                  <Box key={item.id || idx} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Avatar src={getImageUrl(item.image)} alt={item.title} sx={{ width: 48, height: 48, mr: 1 }} onError={(e) => { e.target.src = 'https://source.unsplash.com/100x100/?news'; }} />\n                    <Box>\n                      <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: 'grey.900', lineHeight: 1.2 }}>\n                        {item.title.length > 50 ? item.title.substring(0, 50) + '...' : item.title}\n                      </Typography>\n                      <Typography variant=\"caption\" sx={{ color: 'grey.600' }}>\n                        {formatDate(item.created_at || item.date)} • {item.views || 0} views\n                      </Typography>\n                    </Box>\n                  </Box>\n                ))\n              ) : (\n                <Typography variant=\"body2\" sx={{ color: 'grey.500', textAlign: 'center' }}>\n                  Belum ada berita populer\n                </Typography>\n              )}\n            </Stack>\n          </Box>\n        </Box>\n\n        {/* Footer */}\n        <Box sx={{ mt: 4, py: 3, borderTop: '1px solid #e0e0e0', textAlign: 'center', px: 4 }}>\n          <Footer title={kostum.title} />\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Create custom theme based on kostum colors\nconst createCustomTheme = (kostum) => {\n  return createTheme({\n    palette: {\n      primary: {\n        main: kostum.primary_color || '#1976d2',\n      },\n      secondary: {\n        main: kostum.secondary_color || '#dc004e',\n      },\n      background: {\n        default: kostum.background_color || '#f5f5f5',\n      },\n      text: {\n        primary: kostum.text_color || '#333333',\n      },\n    },\n  });\n};\n\nexport default function LandingPage() {\n  const navigate = useNavigate();\n  const { user, logout, isAuthenticated } = useAuth();\n  const [selectedCategory, setSelectedCategory] = useState('Semua');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [bottomNav, setBottomNav] = useState(0);\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: '',\n    primary_color: '#1976d2',\n    secondary_color: '#dc004e',\n    accent_color: '#ff9800',\n    background_color: '#f5f5f5',\n    text_color: '#333333'\n  });\n  const [newsData, setNewsData] = useState([]);\n  const [popularNews, setPopularNews] = useState([]);\n  const [categoriesData, setCategoriesData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [expandedCards, setExpandedCards] = useState({});\n  // Removed searchModalOpen - using searchActive overlay instead for consistency\n  const [searchActive, setSearchActive] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [bookmarkedNews, setBookmarkedNews] = useState(new Set());\n  const [likedNews, setLikedNews] = useState(new Set());\n  const [toast, setToast] = useState({ visible: false, message: '' });\n\n  // Check URL parameters on component mount\n  React.useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const kategori = urlParams.get('kategori');\n    const beritaId = urlParams.get('berita');\n    \n    if (kategori && kategori !== 'Semua') {\n      setSelectedCategory(kategori);\n    }\n    \n    // If there's a specific news ID, we could highlight it later\n    if (beritaId) {\n      // You can add logic here to highlight or scroll to the specific news\n      console.log('Shared news ID:', beritaId);\n    }\n  }, []);\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n\n\n  const handleSidebar = () => setSidebarOpen(true);\n  const handleSidebarClose = () => setSidebarOpen(false);\n\n  const handleShare = async (news) => {\n    try {\n      // Update share count in database\n      fetch('http://localhost:5000/api/posts/' + news.id + '/share', {\n        method: 'POST'\n      }).catch(err => console.error('Error updating share count:', err));\n\n      // Generate URL-friendly title for the link\n      const urlTitle = news.title\n        .toLowerCase()\n        .replace(/[^a-z0-9\\s-]/g, '') // Remove special characters\n        .replace(/\\s+/g, '-') // Replace spaces with hyphens\n        .replace(/-+/g, '-') // Replace multiple hyphens with single\n        .trim();\n\n      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;\n\n      // Copy to clipboard\n      await navigator.clipboard.writeText(link);\n\n      // Update local state to reflect share count increase\n      setNewsData(prevData =>\n        prevData.map(item =>\n          item.id === news.id\n            ? { ...item, share: (item.share || 0) + 1 }\n            : item\n        )\n      );\n\n      // Show success toast\n      setToast({ visible: true, message: 'Link berita berhasil disalin ke clipboard!' });\n\n      // Auto-hide toast after 3 seconds\n      setTimeout(() => {\n        setToast({ visible: false, message: '' });\n      }, 3000);\n\n    } catch (error) {\n      console.error('Failed to copy link:', error);\n      setToast({ visible: true, message: 'Gagal menyalin link berita' });\n      setTimeout(() => {\n        setToast({ visible: false, message: '' });\n      }, 3000);\n    }\n  };\n\n  const handleLike = async (news) => {\n    const isCurrentlyLiked = likedNews.has(news.id);\n\n    try {\n      // Optimistic update\n      const newLikedNews = new Set(likedNews);\n      if (isCurrentlyLiked) {\n        newLikedNews.delete(news.id);\n      } else {\n        newLikedNews.add(news.id);\n      }\n      setLikedNews(newLikedNews);\n\n      // Update like status in database\n      const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_like&id=${news.id}`, {\n        method: 'POST'\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        // Update liked state based on server response\n        const finalLikedNews = new Set(likedNews);\n        if (data.liked) {\n          finalLikedNews.add(news.id);\n        } else {\n          finalLikedNews.delete(news.id);\n        }\n        setLikedNews(finalLikedNews);\n\n        // Update news data with new likes count\n        setNewsData(prevNews =>\n          prevNews.map(item =>\n            item.id === news.id\n              ? { ...item, likes: data.likes }\n              : item\n          )\n        );\n\n        setPopularNews(prevNews =>\n          prevNews.map(item =>\n            item.id === news.id\n              ? { ...item, likes: data.likes }\n              : item\n          )\n        );\n\n        setToast({\n          visible: true,\n          message: data.liked ? 'Berita disukai!' : 'Like dihapus'\n        });\n      } else {\n        // Revert optimistic update on failure\n        setLikedNews(likedNews);\n        setToast({ visible: true, message: 'Gagal memperbarui like' });\n      }\n    } catch (error) {\n      // Revert optimistic update on error\n      setLikedNews(likedNews);\n      console.error('Failed to toggle like:', error);\n      setToast({ visible: true, message: 'Gagal memperbarui like' });\n    }\n\n    // Hide toast after 3 seconds\n    setTimeout(() => {\n      setToast({ visible: false, message: '' });\n    }, 3000);\n  };\n\n  const handleBookmark = async (news) => {\n    // Check authentication first\n    if (!isAuthenticated) {\n      navigate('/auth/login');\n      return;\n    }\n\n    const isCurrentlyBookmarked = bookmarkedNews.has(news.id);\n\n    try {\n      // Update bookmark status in database\n      const action = isCurrentlyBookmarked ? 'remove_saved_news' : 'add_saved_news';\n\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: action,\n          post_id: news.id\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        // Update local state\n        setBookmarkedNews(prev => {\n          const newSet = new Set(prev);\n          if (isCurrentlyBookmarked) {\n            newSet.delete(news.id);\n          } else {\n            newSet.add(news.id);\n          }\n          return newSet;\n        });\n\n        // Show toast notification\n        const message = isCurrentlyBookmarked\n          ? 'Berita dihapus dari bookmark'\n          : 'Berita ditambahkan ke bookmark';\n        setToast({ visible: true, message });\n\n        setTimeout(() => {\n          setToast({ visible: false, message: '' });\n        }, 3000);\n      } else {\n        setToast({ visible: true, message: 'Gagal mengupdate bookmark: ' + (result.message || 'Unknown error') });\n        setTimeout(() => {\n          setToast({ visible: false, message: '' });\n        }, 3000);\n      }\n    } catch (error) {\n      console.error('Error updating bookmark:', error);\n      setToast({ visible: true, message: 'Error: ' + error.message });\n      setTimeout(() => {\n        setToast({ visible: false, message: '' });\n      }, 3000);\n    }\n  };\n\n  // Handler untuk buka search overlay\n  const openSearch = () => {\n    setSearchActive(true);\n    setSearchQuery('');\n  };\n  // Handler untuk tutup search overlay\n  const closeSearch = () => {\n    setSearchActive(false);\n    setSearchQuery('');\n  };\n  // Filter berita sesuai query\n  const filteredSearch = searchQuery.trim() ? newsData.filter(\n    n =>\n      n.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      n.description.toLowerCase().includes(searchQuery.toLowerCase())\n  ).slice(0, 8) : [];\n  // Handler keyboard ESC\n  React.useEffect(() => {\n    if (!searchActive) return;\n    const handleKey = (e) => {\n      if (e.key === 'Escape') closeSearch();\n    };\n    window.addEventListener('keydown', handleKey);\n    return () => window.removeEventListener('keydown', handleKey);\n  }, [searchActive]);\n\n  // Load website settings from database\n  React.useEffect(() => {\n    // Get settings from database via correct PHP API\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_settings')\n      .then(res => res.json())\n      .then(response => {\n        if (response.success && response.data) {\n          const data = response.data;\n\n          // Process logo path - use fallback SVG if database logo not accessible\n          let logoPath = 'data:image/svg+xml;base64,' + btoa(`\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"#3B82F6\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          `);\n\n          if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {\n            // Extract filename and use root/uploads path\n            const filename = data.website_logo.split('/').pop();\n            logoPath = `http://localhost/react-news/uploads/${filename}`;\n            // console.log('LandingPage - Logo path:', logoPath);\n          }\n\n          setKostum({\n            logo: logoPath,\n            title: data.website_name || 'React News Portal',\n            description: data.website_description || 'Portal berita terkini dan terpercaya',\n            primary_color: data.primary_color || '#1976d2',\n            secondary_color: data.secondary_color || '#dc004e',\n            accent_color: data.accent_color || '#ff9800',\n            background_color: '#f5f5f5',\n            text_color: '#333333',\n            footer_text: '© 2024 React News Portal. All rights reserved.',\n            contact_email: '<EMAIL>',\n            social_facebook: '',\n            social_twitter: '',\n            social_instagram: '',\n            social_youtube: '',\n            meta_keywords: 'berita, news, portal',\n            meta_description: data.website_description || 'Portal berita terkini'\n          });\n\n          // Update document title and meta tags\n          document.title = data.website_name || 'React News Portal';\n\n          // Update meta description\n          const metaDescription = document.querySelector('meta[name=\"description\"]');\n          if (metaDescription) {\n            metaDescription.setAttribute('content', data.website_description || 'Portal berita terkini');\n          }\n\n          // Update meta keywords\n          const metaKeywords = document.querySelector('meta[name=\"keywords\"]');\n          if (metaKeywords) {\n            metaKeywords.setAttribute('content', 'berita, news, portal');\n          }\n\n          // Update favicon\n          if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {\n            const filename = data.website_logo.split('/').pop();\n            const faviconUrl = `http://localhost/react-news/uploads/${filename}`;\n\n            // Update existing favicon or create new one\n            let favicon = document.querySelector('link[rel=\"icon\"]') || document.querySelector('link[rel=\"shortcut icon\"]');\n            if (!favicon) {\n              favicon = document.createElement('link');\n              favicon.rel = 'icon';\n              document.head.appendChild(favicon);\n            }\n            favicon.href = faviconUrl;\n\n            // Also update apple-touch-icon if exists\n            const appleTouchIcon = document.querySelector('link[rel=\"apple-touch-icon\"]');\n            if (appleTouchIcon) {\n              appleTouchIcon.href = faviconUrl;\n            }\n\n            console.log('LandingPage - Favicon updated:', faviconUrl);\n          }\n        } else {\n          throw new Error('Invalid API response');\n        }\n      })\n      .catch(err => {\n        console.log('Database settings not available, using defaults:', err);\n        // Use default settings matching database.sql structure\n        setKostum({\n          logo: 'data:image/svg+xml;base64,' + btoa(`\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"#3B82F6\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          `),\n          title: 'React News Portal',\n          description: 'Portal berita terkini dan terpercaya',\n          primary_color: '#3B82F6',\n          secondary_color: '#10B981',\n          accent_color: '#F59E0B',\n          background_color: '#f5f5f5',\n          text_color: '#333333',\n          footer_text: '© 2024 React News Portal. All rights reserved.',\n          contact_email: '<EMAIL>',\n          social_facebook: '',\n          social_twitter: '',\n          social_instagram: '',\n          social_youtube: '',\n          meta_keywords: 'berita, news, portal, react',\n          meta_description: 'Portal berita terkini dengan teknologi React'\n        });\n\n        // Set default document title\n        document.title = 'React News Portal';\n      });\n  }, []);\n\n  // Function to refresh settings (can be called when admin updates settings)\n  const refreshSettings = React.useCallback(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_settings&t=' + Date.now())\n      .then(res => res.json())\n      .then(response => {\n        if (response.success && response.data) {\n          const data = response.data;\n\n          // Process logo path - use fallback SVG if database logo not accessible\n          let logoPath = 'data:image/svg+xml;base64,' + btoa(`\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"#3B82F6\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          `);\n\n          if (data.website_logo && data.website_logo !== '/logo192.png' && data.website_logo !== 'assets/logo.png') {\n            // Extract filename and use root/uploads path\n            const filename = data.website_logo.split('/').pop();\n            logoPath = `http://localhost/react-news/uploads/${filename}`;\n            // console.log('LandingPage - RefreshSettings Logo path:', logoPath);\n\n            // Update favicon in refreshSettings\n            let favicon = document.querySelector('link[rel=\"icon\"]') || document.querySelector('link[rel=\"shortcut icon\"]');\n            if (!favicon) {\n              favicon = document.createElement('link');\n              favicon.rel = 'icon';\n              document.head.appendChild(favicon);\n            }\n            favicon.href = logoPath;\n\n            // Also update apple-touch-icon if exists\n            const appleTouchIcon = document.querySelector('link[rel=\"apple-touch-icon\"]');\n            if (appleTouchIcon) {\n              appleTouchIcon.href = logoPath;\n            }\n\n            // console.log('LandingPage - RefreshSettings Favicon updated:', logoPath);\n          }\n\n          setKostum(prev => ({\n            ...prev,\n            logo: logoPath,\n            title: data.website_name || 'React News Portal',\n            description: data.website_description || 'Portal berita terkini dan terpercaya'\n          }));\n\n          document.title = data.website_name || 'React News Portal';\n        }\n      })\n      .catch(err => console.log('Failed to refresh settings:', err));\n  }, []);\n\n  // Listen for settings updates (optional - for real-time updates)\n  React.useEffect(() => {\n    const handleStorageChange = (e) => {\n      if (e.key === 'settings_updated') {\n        console.log('Settings updated, refreshing...');\n        refreshSettings();\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, [refreshSettings]);\n\n  // Load categories from database\n  React.useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_categories')\n      .then(res => res.json())\n      .then(data => {\n        if (data.success && Array.isArray(data.data)) {\n          // Map database structure to frontend format\n          const mappedCategories = data.data.map(cat => ({\n            id: cat.id,\n            name: cat.name,\n            slug: cat.slug,\n            description: cat.description,\n            color: cat.color || '#6B7280',\n            is_active: cat.is_active,\n            post_count: cat.post_count || 0,\n            created_at: cat.created_at,\n            updated_at: cat.updated_at\n          }));\n\n          // Filter only active categories\n          const activeCategories = mappedCategories.filter(cat => cat.is_active !== false);\n          setCategoriesData(activeCategories);\n        }\n      })\n      .catch(err => {\n        // Fallback categories matching database.sql structure\n        setCategoriesData([\n          {\n            id: 1,\n            name: 'Umum',\n            slug: 'umum',\n            description: 'Berita umum dan informasi terkini',\n            color: '#6B7280',\n            is_active: true,\n            post_count: 0\n          },\n          {\n            id: 2,\n            name: 'Teknologi',\n            slug: 'teknologi',\n            description: 'Berita teknologi dan inovasi digital',\n            color: '#3B82F6',\n            is_active: true,\n            post_count: 0\n          },\n          {\n            id: 3,\n            name: 'Bisnis',\n            slug: 'bisnis',\n            description: 'Berita bisnis dan ekonomi',\n            color: '#10B981',\n            is_active: true,\n            post_count: 0\n          },\n          {\n            id: 4,\n            name: 'Olahraga',\n            slug: 'olahraga',\n            description: 'Berita olahraga dan kompetisi',\n            color: '#F59E0B',\n            is_active: true,\n            post_count: 0\n          },\n          {\n            id: 5,\n            name: 'Hiburan',\n            slug: 'hiburan',\n            description: 'Berita hiburan dan selebriti',\n            color: '#EF4444',\n            is_active: true,\n            post_count: 4\n          },\n          {\n            id: 6,\n            name: 'Politik',\n            slug: 'politik',\n            description: 'Berita politik dan pemerintahan',\n            color: '#8B5CF6',\n            is_active: true,\n            post_count: 2\n          },\n          {\n            id: 7,\n            name: 'Kesehatan',\n            slug: 'kesehatan',\n            description: 'Berita kesehatan dan medis',\n            color: '#06B6D4',\n            is_active: true,\n            post_count: 7\n          }\n        ]);\n      });\n  }, []);\n\n  // Load news data from database\n  React.useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news')\n      .then(res => res.json())\n      .then(data => {\n        if (data.success && Array.isArray(data.data)) {\n          // Map database structure to frontend format\n          const mappedData = data.data.map(item => ({\n            id: item.id,\n            title: item.title,\n            slug: item.slug,\n            description: item.description || item.excerpt || item.content?.substring(0, 200) + '...',\n            content: item.content,\n            image: item.image || '/uploads/default-news.jpg',\n            image_alt: item.image_alt || item.title,\n            category: item.category_name || 'Umum',\n            category_name: item.category_name || 'Umum',\n            category_id: item.category_id || 1,\n            category_color: item.category_color || '#6B7280',\n            status: item.status || 'published',\n            featured: item.featured || false,\n            tags: item.tags ? item.tags.split(',') : [],\n            views: item.views || 0,\n            share: item.share || 0,\n            likes: item.likes || 0,\n            comments_count: item.comments_count || 0,\n            reading_time: item.reading_time || 5,\n            date: item.date || item.created_at,\n            published_at: item.published_at,\n            created_at: item.created_at,\n            updated_at: item.updated_at,\n            author: item.author_name || item.full_name || 'Admin',\n            user_id: item.user_id\n          }));\n\n          // Filter only published posts\n          const publishedPosts = mappedData.filter(item => item.status === 'published');\n          setNewsData(publishedPosts);\n\n          // Sort by engagement score for popular news\n          const popular = publishedPosts\n            .sort((a, b) => {\n              const scoreA = (a.views * 1) + (a.share * 3) + (a.likes * 2) + (a.comments_count * 4);\n              const scoreB = (b.views * 1) + (b.share * 3) + (b.likes * 2) + (b.comments_count * 4);\n              return scoreB - scoreA;\n            })\n            .slice(0, 5);\n          setPopularNews(popular);\n        } else {\n          setNewsData([]);\n          setPopularNews([]);\n        }\n        setLoading(false);\n      })\n      .catch(() => {\n        // Fallback data matching database.sql structure\n        const dummyNews = [\n          {\n            id: 1,\n            title: 'Selamat Datang di React News Portal',\n            slug: 'selamat-datang-di-react-news-portal',\n            description: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna.',\n            content: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna. Dengan desain responsif dan fitur-fitur canggih.',\n            image: '/uploads/default-tech.jpg',\n            image_alt: 'React News Portal',\n            category: 'Teknologi',\n            category_name: 'Teknologi',\n            category_id: 2,\n            category_color: '#3B82F6',\n            status: 'published',\n            featured: true,\n            tags: ['teknologi', 'react', 'portal', 'berita'],\n            views: 150,\n            share: 25,\n            likes: 45,\n            comments_count: 8,\n            reading_time: 3,\n            date: new Date().toISOString(),\n            published_at: new Date().toISOString(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            author: 'Admin',\n            user_id: 1\n          },\n          {\n            id: 2,\n            title: 'Teknologi AI Terbaru Mengubah Dunia Digital',\n            slug: 'teknologi-ai-terbaru-mengubah-dunia-digital',\n            description: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari.',\n            content: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari. AI kini menjadi bagian integral dari transformasi digital.',\n            image: '/uploads/default-ai.jpg',\n            image_alt: 'Teknologi AI',\n            category: 'Teknologi',\n            category_name: 'Teknologi',\n            category_id: 2,\n            category_color: '#3B82F6',\n            status: 'published',\n            featured: false,\n            tags: ['ai', 'teknologi', 'digital', 'inovasi'],\n            views: 89,\n            share: 12,\n            likes: 23,\n            comments_count: 5,\n            reading_time: 4,\n            date: new Date(Date.now() - 86400000).toISOString(),\n            published_at: new Date(Date.now() - 86400000).toISOString(),\n            created_at: new Date(Date.now() - 86400000).toISOString(),\n            updated_at: new Date(Date.now() - 86400000).toISOString(),\n            author: 'Admin',\n            user_id: 1\n          },\n          {\n            id: 3,\n            title: 'Tips Investasi untuk Pemula di Era Digital',\n            slug: 'tips-investasi-untuk-pemula-di-era-digital',\n            description: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya.',\n            content: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya. Pelajari strategi investasi yang tepat untuk pemula.',\n            image: '/uploads/default-business.jpg',\n            image_alt: 'Investasi Digital',\n            category: 'Bisnis',\n            category_name: 'Bisnis',\n            category_id: 3,\n            category_color: '#10B981',\n            status: 'published',\n            featured: false,\n            tags: ['investasi', 'bisnis', 'digital', 'keuangan'],\n            views: 67,\n            share: 8,\n            likes: 15,\n            comments_count: 3,\n            reading_time: 6,\n            date: new Date(Date.now() - 172800000).toISOString(),\n            published_at: new Date(Date.now() - 172800000).toISOString(),\n            created_at: new Date(Date.now() - 172800000).toISOString(),\n            updated_at: new Date(Date.now() - 172800000).toISOString(),\n            author: 'Admin',\n            user_id: 1\n          }\n        ];\n        setNewsData(dummyNews);\n        setPopularNews(dummyNews);\n        setLoading(false);\n      });\n  }, []);\n\n  // Load bookmarked news from database (only if authenticated)\n  React.useEffect(() => {\n    if (!isAuthenticated) {\n      setBookmarkedNews(new Set()); // Clear bookmarks if not authenticated\n      return;\n    }\n\n    const loadBookmarkedNews = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_saved_news');\n        const data = await response.json();\n\n        if (data.success && Array.isArray(data.data)) {\n          // Extract news IDs that are bookmarked\n          const bookmarkedIds = new Set(data.data.map(news => news.id));\n          setBookmarkedNews(bookmarkedIds);\n        }\n      } catch (error) {\n        console.log('Could not load bookmarked news:', error);\n      }\n    };\n\n    loadBookmarkedNews();\n  }, [isAuthenticated]);\n\n  // Load liked posts from database (based on IP address)\n  React.useEffect(() => {\n    const loadLikedPosts = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_liked_posts');\n        const data = await response.json();\n\n        if (data.success && Array.isArray(data.data)) {\n          // Extract post IDs that are liked by this IP\n          const likedIds = new Set(data.data.map(id => parseInt(id)));\n          setLikedNews(likedIds);\n          console.log('Loaded liked posts for IP:', data.ip_address, 'Posts:', data.data);\n        }\n      } catch (error) {\n        console.log('Could not load liked posts:', error);\n      }\n    };\n\n    loadLikedPosts();\n  }, []); // Load once on component mount\n\n  // Update category post counts when both newsData and categoriesData are loaded\n  React.useEffect(() => {\n    if (newsData.length > 0 && categoriesData.length > 0) {\n      // Check if categories already have correct post_count from API\n      const hasApiCounts = categoriesData.some(cat => cat.post_count > 0);\n\n      if (!hasApiCounts) {\n        // Count posts per category from newsData\n        const postCounts = {};\n        newsData.forEach(news => {\n          const categoryId = news.category_id;\n          postCounts[categoryId] = (postCounts[categoryId] || 0) + 1;\n        });\n\n        // Update categories with actual post counts\n        const updatedCategories = categoriesData.map(cat => ({\n          ...cat,\n          post_count: postCounts[cat.id] || 0\n        }));\n\n        setCategoriesData(updatedCategories);\n      }\n    }\n  }, [newsData, categoriesData]);\n\n  const handleCardExpand = (cardId) => {\n    setExpandedCards(prev => ({ ...prev, [cardId]: !prev[cardId] }));\n  };\n\n  const handleNewsClick = async (newsId) => {\n    // Increment views when clicking news card\n    try {\n      await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_views&id=${newsId}`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.log('Could not increment views:', error);\n    }\n\n    // Navigate to news detail\n    navigate(`/data-news/${newsId}`);\n  };\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  const handleSearchClick = () => {\n    // Use the same search function as bottom nav for consistency\n    openSearch();\n    setSidebarOpen(false); // Close sidebar when search opens\n  };\n\n  // Handler untuk klik bottom nav\n  const handleBottomNavChange = (newValue) => {\n    if (newValue === 0) {\n      setBottomNav(0);\n      // Already on home page, just update state\n    } else if (newValue === 1) {\n      setBottomNav(1);\n      // Video - Navigate to new video player page\n      setBottomNav(1);\n      console.log('Navigating to video player page...');\n      // Direct navigation to new video player page\n      window.location.href = 'http://localhost/react-news/frontend/src/pages/user/video-player.php';\n    } else if (newValue === 2) {\n      setBottomNav(2);\n      openSearch(); // Search\n    } else if (newValue === 3) {\n      // Simpan - Check authentication first\n      if (isAuthenticated) {\n        window.location.href = '/saved'; // Navigate to saved page\n      } else {\n        // Redirect to login if not authenticated\n        navigate('/auth/login');\n      }\n    } else {\n      setBottomNav(newValue);\n    }\n  };\n\n  const customTheme = createCustomTheme(kostum);\n\n  if (isDesktop) {\n    return (\n      <ThemeProvider theme={customTheme}>\n        <DesktopNewsLayout\n          kostum={kostum}\n          newsData={newsData}\n          popularNews={popularNews}\n          categoriesData={categoriesData}\n          selectedCategory={selectedCategory}\n          setSelectedCategory={setSelectedCategory}\n          expandedCards={expandedCards}\n          handleCardExpand={handleCardExpand}\n          loading={loading}\n          handleShare={handleShare}\n          onBookmark={handleBookmark}\n          bookmarkedNews={bookmarkedNews}\n          onSearchClick={openSearch}\n          sidebarOpen={sidebarOpen}\n          handleSidebar={handleSidebar}\n          handleSidebarClose={handleSidebarClose}\n          onNewsClick={handleNewsClick}\n          openSearch={openSearch}\n          navigate={navigate}\n          user={user}\n          isAuthenticated={isAuthenticated}\n          handleLogout={handleLogout}\n          onLike={handleLike}\n          likedNews={likedNews}\n        />\n        \n        {/* Toast Notification */}\n        <Toast \n          message={toast.message} \n          isVisible={toast.visible} \n          onClose={() => setToast({ visible: false, message: '' })} \n        />\n        {/* Search Overlay for Desktop */}\n        {searchActive && (\n          <>\n            {/* Backdrop */}\n            <Box\n              onClick={closeSearch}\n              sx={{\n                position: 'fixed',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                zIndex: 2100,\n                bgcolor: 'rgba(0,0,0,0.3)',\n                animation: 'fadeIn 0.3s ease-out',\n                '@keyframes fadeIn': {\n                  '0%': { opacity: 0 },\n                  '100%': { opacity: 1 },\n                },\n              }}\n            />\n            {/* Search Content */}\n            <Box sx={{\n              position: 'fixed',\n              top: 0,\n              left: 0,\n              right: 0,\n              zIndex: 2101,\n              bgcolor: 'rgba(255,255,255,0.98)',\n              backdropFilter: 'blur(10px)',\n              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',\n              px: 2,\n              pt: 10, // Space for navbar\n              pb: 3,\n              animation: 'slideDown 0.3s ease-out',\n              '@keyframes slideDown': {\n                '0%': {\n                  transform: 'translateY(-100%)',\n                  opacity: 0,\n                },\n                '100%': {\n                  transform: 'translateY(0)',\n                  opacity: 1,\n                },\n              },\n            }}>\n            <Box sx={{ maxWidth: 600, mx: 'auto', position: 'relative' }}>\n              <TextField\n                autoFocus\n                fullWidth\n                value={searchQuery}\n                onChange={e => setSearchQuery(e.target.value)}\n                placeholder=\"Cari berita di sini...\"\n                variant=\"outlined\"\n                sx={{\n                  fontSize: 18,\n                  bgcolor: '#fff',\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 3,\n                    fontSize: 16,\n                    '&:hover fieldset': {\n                      borderColor: 'primary.main',\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: 'primary.main',\n                      borderWidth: 2,\n                    },\n                  },\n                }}\n                InputProps={{\n                  startAdornment: (\n                    <SearchIcon sx={{ color: 'grey.400', mr: 1 }} />\n                  ),\n                }}\n              />\n              <IconButton\n                onClick={closeSearch}\n                sx={{\n                  position: 'absolute',\n                  top: 8,\n                  right: 8,\n                  zIndex: 10,\n                  bgcolor: 'rgba(0,0,0,0.05)',\n                  '&:hover': {\n                    bgcolor: 'rgba(0,0,0,0.1)',\n                  },\n                }}\n                aria-label=\"Tutup\"\n              >\n                <CloseIcon />\n              </IconButton>\n            </Box>\n            {/* Hasil search */}\n            {searchQuery.trim() && (\n              <Box sx={{\n                maxWidth: 600,\n                mx: 'auto',\n                mt: 2,\n                bgcolor: '#fff',\n                borderRadius: 3,\n                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n                p: 2,\n                maxHeight: '60vh',\n                overflowY: 'auto',\n              }}>\n                {filteredSearch.length === 0 ? (\n                  <Typography variant=\"body2\" sx={{ color: 'grey.500', textAlign: 'center', py: 3 }}>\n                    Tidak ada berita ditemukan untuk \"{searchQuery}\"\n                  </Typography>\n                ) : (\n                  <>\n                    <Typography variant=\"body2\" sx={{ color: 'grey.600', mb: 2, fontWeight: 500 }}>\n                      Ditemukan {filteredSearch.length} berita\n                    </Typography>\n                    {filteredSearch.map((item) => (\n                      <Box\n                        key={item.id}\n                        onClick={() => {\n                          closeSearch();\n                          handleNewsClick(item.id);\n                        }}\n                        sx={{\n                          py: 2,\n                          px: 2,\n                          borderBottom: '1px solid #f0f0f0',\n                          cursor: 'pointer',\n                          borderRadius: 2,\n                          '&:last-child': { borderBottom: 0 },\n                          '&:hover': {\n                            bgcolor: 'rgba(25, 118, 210, 0.04)',\n                            transform: 'translateY(-1px)',\n                            transition: 'all 0.2s ease',\n                          },\n                        }}\n                      >\n                        <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, color: 'primary.main', mb: 0.5 }}>\n                          {item.title}\n                        </Typography>\n                        <Typography variant=\"caption\" sx={{ color: 'grey.600', display: 'block', mb: 1 }}>\n                          {item.category} • {new Date(item.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: 'grey.700', lineHeight: 1.4 }}>\n                          {item.description.length > 100 ? item.description.substring(0, 100) + '...' : item.description}\n                        </Typography>\n                      </Box>\n                    ))}\n                  </>\n                )}\n              </Box>\n            )}\n          </Box>\n          </>\n        )}\n      </ThemeProvider>\n    );\n  }\n  return (\n    <ThemeProvider theme={customTheme}>\n      <MobileNewsLayout\n        kostum={kostum}\n        newsData={newsData}\n        popularNews={popularNews}\n        categoriesData={categoriesData}\n        selectedCategory={selectedCategory}\n        setSelectedCategory={setSelectedCategory}\n        expandedCards={expandedCards}\n        handleCardExpand={handleCardExpand}\n        loading={loading}\n        bottomNav={bottomNav}\n        setBottomNav={setBottomNav}\n        handleSidebar={handleSidebar}\n        sidebarOpen={sidebarOpen}\n        handleSidebarClose={handleSidebarClose}\n        handleShare={handleShare}\n        onBookmark={handleBookmark}\n        bookmarkedNews={bookmarkedNews}\n        onSearchClick={handleSearchClick}\n        handleBottomNavChange={handleBottomNavChange}\n        onNewsClick={handleNewsClick}\n        openSearch={openSearch}\n        navigate={navigate}\n        user={user}\n        isAuthenticated={isAuthenticated}\n        handleLogout={handleLogout}\n        onLike={handleLike}\n        likedNews={likedNews}\n      />\n      \n      {/* Toast Notification */}\n      <Toast \n        message={toast.message} \n        isVisible={toast.visible} \n        onClose={() => setToast({ visible: false, message: '' })} \n      />\n      {/* Overlay input search */}\n      {searchActive && (\n        <Box sx={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          zIndex: 2101,\n          bgcolor: 'rgba(255,255,255,0.98)',\n          boxShadow: 3,\n          px: { xs: 2, md: 0 },\n          pt: { xs: 2, md: 0 }, // desktop: pt: 0 agar overlay tepat di atas\n          pb: 2,\n        }}>\n          <Box sx={{ maxWidth: 500, mx: 'auto', position: 'relative' }}>\n            <TextField\n              autoFocus\n              fullWidth\n              value={searchQuery}\n              onChange={e => setSearchQuery(e.target.value)}\n              placeholder=\"Cari berita di sini...\"\n              variant=\"outlined\"\n              sx={{ fontSize: 18, bgcolor: '#fff' }}\n            />\n            <IconButton\n              onClick={closeSearch}\n              sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n              aria-label=\"Tutup\"\n            >\n              <CloseIcon />\n            </IconButton>\n          </Box>\n          {/* Hasil search */}\n          {searchQuery.trim() && (\n            <Box sx={{ maxWidth: 500, mx: 'auto', mt: 1, bgcolor: '#fff', borderRadius: 2, boxShadow: 1, p: 1 }}>\n              {filteredSearch.length === 0 ? (\n                <Typography variant=\"body2\" sx={{ color: 'grey.500', textAlign: 'center', py: 2 }}>\n                  Tidak ada berita ditemukan\n                </Typography>\n              ) : (\n                filteredSearch.map((item) => (\n                  <Box key={item.id} sx={{ py: 1, px: 1, borderBottom: '1px solid #eee', cursor: 'pointer', '&:last-child': { borderBottom: 0 } }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: 'primary.main' }}>{item.title}</Typography>\n                    <Typography variant=\"caption\" sx={{ color: 'grey.600' }}>{item.category} &bull; {new Date(item.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}</Typography>\n                    <Typography variant=\"body2\" sx={{ color: 'grey.800' }}>\n                      {item.description.length > 80 ? item.description.substring(0, 80) + '...' : item.description}\n                    </Typography>\n                  </Box>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      )}\n      {/* Search modal removed - using consistent search overlay instead */}\n    </ThemeProvider>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,EAAEC,WAAW,EAAEC,aAAa,QAAQ,sBAAsB;AAC3E,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,iBAAiB,MAAM,mCAAmC;;AAEjE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,WAAW,GAAIC,SAAS,IAAK;EACjC,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,2CAA2C,CAAC,CAAC;EACtD;EAEAC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEF,SAAS,CAAC;;EAE9D;EACA,IAAIA,SAAS,CAACG,UAAU,CAAC,SAAS,CAAC,IAAIH,SAAS,CAACG,UAAU,CAAC,UAAU,CAAC,EAAE;IACvEF,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,SAAS,CAAC;IACvD,OAAOA,SAAS;EAClB;;EAEA;EACA,IAAII,QAAQ,GAAG,EAAE;EAEjB,IAAIJ,SAAS,CAACG,UAAU,CAAC,sBAAsB,CAAC,EAAE;IAChDC,QAAQ,GAAGJ,SAAS,CAACK,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;EAC1D,CAAC,MAAM,IAAIL,SAAS,CAACG,UAAU,CAAC,+BAA+B,CAAC,EAAE;IAChEC,QAAQ,GAAGJ,SAAS,CAACK,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC;EACnE,CAAC,MAAM,IAAIL,SAAS,CAACG,UAAU,CAAC,WAAW,CAAC,EAAE;IAC5CC,QAAQ,GAAGJ,SAAS,CAACK,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAC/C,CAAC,MAAM,IAAIL,SAAS,CAACG,UAAU,CAAC,mCAAmC,CAAC,EAAE;IACpEC,QAAQ,GAAGJ,SAAS,CAACK,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC;EACvE,CAAC,MAAM,IAAIL,SAAS,CAACG,UAAU,CAAC,UAAU,CAAC,EAAE;IAC3CC,QAAQ,GAAGJ,SAAS,CAACK,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EAC9C,CAAC,MAAM,IAAIL,SAAS,CAACG,UAAU,CAAC,cAAc,CAAC,EAAE;IAC/CC,QAAQ,GAAGJ,SAAS,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;EAClD,CAAC,MAAM,IAAI,CAACL,SAAS,CAACM,QAAQ,CAAC,GAAG,CAAC,EAAE;IACnC;IACAF,QAAQ,GAAGJ,SAAS;EACtB,CAAC,MAAM;IACL;IACAI,QAAQ,GAAGJ,SAAS,CAACO,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;EACvC;;EAEA;EACA,MAAMC,aAAa,GAAG,CACpB,4BAA4BL,QAAQ,EAAE;EAAqB;EAC3D,uCAAuCA,QAAQ,EAAE;EAAS;EAC1D,gDAAgDA,QAAQ,EAAE,CAAC;EAAA,CAC5D;EAEDH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEE,QAAQ,CAAC;EACjEH,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEO,aAAa,CAAC;;EAE3D;EACA,MAAMC,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC;EACjCR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEQ,QAAQ,CAAC;EACjD,OAAOA,QAAQ;AACjB,CAAC;;AAED;;AAEA,MAAMC,UAAU,GAAG,CACjB,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CACtF;AAED,SAASC,KAAKA,CAAC;EAAEC,OAAO;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAE;EAC9C,IAAI,CAACD,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACElB,OAAA,CAACjC,GAAG;IACFqD,EAAE,EAAE;MACFC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE;QAAEC,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAI,CAAC;MACxBC,KAAK,EAAE;QAAEF,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MACzBE,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE,OAAO;MACdC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,CAAC;MACNC,SAAS,EAAE,eAAe;MAC1BC,UAAU,EAAE,4BAA4B;MACxCC,QAAQ,EAAE;QAAEf,EAAE,EAAE,oBAAoB;QAAEC,EAAE,EAAE;MAAI,CAAC;MAC/Ce,QAAQ,EAAE;QAAEhB,EAAE,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAI;IAC/B,CAAE;IAAAgB,QAAA,gBAEFxC,OAAA,CAACN,eAAe;MAAC0B,EAAE,EAAE;QAAEqB,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzC7C,OAAA,CAAClC,UAAU;MAACgF,OAAO,EAAC,OAAO;MAAC1B,EAAE,EAAE;QAAE2B,UAAU,EAAE,GAAG;QAAEN,QAAQ,EAAE;UAAElB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG;MAAE,CAAE;MAAAgB,QAAA,EAC/EvB;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV;AAACG,EAAA,GA/BQhC,KAAK;AAiCd,SAASiC,WAAWA,CAAC;EAAEC,gBAAgB;EAAEC,mBAAmB;EAAEC;AAAe,CAAC,EAAE;EAC9E,MAAMC,WAAW,GAAGD,cAAc,CAACE,MAAM,GAAG,CAAC,GAAGF,cAAc,CAACG,GAAG,CAACC,GAAG,KAAK;IACzEC,IAAI,EAAED,GAAG,CAACC,IAAI;IACdC,KAAK,EAAEF,GAAG,CAACG,UAAU,IAAI,CAAC;IAC1B/B,KAAK,EAAE4B,GAAG,CAAC5B,KAAK,IAAI;EACtB,CAAC,CAAC,CAAC,GAAG,CACJ;IAAE6B,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAC5C;IAAE6B,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EACjD;IAAE6B,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAC9C;IAAE6B,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAChD;IAAE6B,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAE6B,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAE6B,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,CAClD;EAED,oBACE5B,OAAA,CAACjC,GAAG;IAACqD,EAAE,EAAE;MACPO,OAAO,EAAE,MAAM;MACfI,YAAY,EAAE,CAAC;MACf6B,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAC;MACL7B,SAAS,EAAE,CAAC;MACZ8B,MAAM,EAAE,WAAW;MACnBC,WAAW,EAAE;IACf,CAAE;IAAAvB,QAAA,gBACAxC,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QAAEa,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAE2B,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,gBACxDxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UACP4C,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,EAAE;UACVtC,OAAO,EAAE,cAAc;UACvBI,YAAY,EAAE,CAAC;UACfmC,EAAE,EAAE;QACN;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACL7C,OAAA,CAAClC,UAAU;QAACgF,OAAO,EAAC,IAAI;QAAC1B,EAAE,EAAE;UAC3B2B,UAAU,EAAE,GAAG;UACfnB,KAAK,EAAE,UAAU;UACjBa,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAC7B,CAAE;QAAAgB,QAAA,EAAC;MAEH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN7C,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QACPa,OAAO,EAAE,MAAM;QACfkC,QAAQ,EAAE,MAAM;QAChBhC,GAAG,EAAE,GAAG;QACRiC,cAAc,EAAE;UAAE7C,EAAE,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAa;MACnD,CAAE;MAAAgB,QAAA,EACCa,WAAW,CAACE,GAAG,CAAC,CAACc,GAAG,EAAEC,KAAK,kBAC1BtE,OAAA,CAAC/B,IAAI;QAEHsG,KAAK,EAAE,GAAGF,GAAG,CAACZ,IAAI,KAAKY,GAAG,CAACX,KAAK,GAAI;QACpCc,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAACkB,GAAG,CAACZ,IAAI,CAAE;QAC7CrC,EAAE,EAAE;UACFO,OAAO,EAAEuB,gBAAgB,KAAKmB,GAAG,CAACZ,IAAI,GAAGY,GAAG,CAACzC,KAAK,GAAG,UAAU;UAC/DA,KAAK,EAAEsB,gBAAgB,KAAKmB,GAAG,CAACZ,IAAI,GAAG,OAAO,GAAG,UAAU;UAC3DV,UAAU,EAAE,GAAG;UACfN,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC5BK,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACL2C,MAAM,EAAE,SAAS;UACjBpC,UAAU,EAAE,eAAe;UAC3B,SAAS,EAAE;YACTV,OAAO,EAAEuB,gBAAgB,KAAKmB,GAAG,CAACZ,IAAI,GAAGY,GAAG,CAACzC,KAAK,GAAG,UAAU;YAC/DQ,SAAS,EAAE,kBAAkB;YAC7BJ,SAAS,EAAE;UACb,CAAC;UACD8B,MAAM,EAAEZ,gBAAgB,KAAKmB,GAAG,CAACZ,IAAI,GAAG,MAAM,GAAG,WAAW;UAC5DM,WAAW,EAAE;QACf;MAAE,GAnBGM,GAAG,CAACZ,IAAI;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBd,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC6B,GAAA,GA3EQzB,WAAW;AA6EpB,SAAS0B,WAAWA,CAAC;EAAEC;AAAS,CAAC,EAAE;EACjC,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGR,QAAQ,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAExC,oBACErF,OAAA,CAACjC,GAAG;IAACqD,EAAE,EAAE;MACPO,OAAO,EAAE,MAAM;MACfI,YAAY,EAAE,CAAC;MACf6B,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAC;MACL7B,SAAS,EAAE,CAAC;MACZ8B,MAAM,EAAE,WAAW;MACnBC,WAAW,EAAE;IACf,CAAE;IAAAvB,QAAA,gBACAxC,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QAAEa,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAE2B,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,gBACxDxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UACP4C,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,EAAE;UACVtC,OAAO,EAAE,cAAc;UACvBI,YAAY,EAAE,CAAC;UACfmC,EAAE,EAAE;QACN;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACL7C,OAAA,CAAClC,UAAU;QAACgF,OAAO,EAAC,IAAI;QAAC1B,EAAE,EAAE;UAC3B2B,UAAU,EAAE,GAAG;UACfnB,KAAK,EAAE,UAAU;UACjBa,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAC7B,CAAE;QAAAgB,QAAA,EAAC;MAEH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN7C,OAAA,CAAC9B,KAAK;MAACoH,OAAO,EAAE,CAAE;MAAA9C,QAAA,EACf4C,WAAW,CAAC7B,GAAG,CAAC,CAACgC,IAAI,EAAEjB,KAAK,kBAC3BtE,OAAA,CAACjC,GAAG;QAAwBqD,EAAE,EAAE;UAC9Ba,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,CAAC;UACNyB,CAAC,EAAE,CAAC;UACJ7B,YAAY,EAAE,CAAC;UACfM,UAAU,EAAE,eAAe;UAC3BoC,MAAM,EAAE,SAAS;UACjB,SAAS,EAAE;YACT9C,OAAO,EAAE,SAAS;YAClBS,SAAS,EAAE;UACb;QACF,CAAE;QAAAI,QAAA,gBAEAxC,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YAAEoE,IAAI,EAAE,CAAC;YAAEjD,QAAQ,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAChCxC,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAClC2B,UAAU,EAAE,GAAG;cACfnB,KAAK,EAAE,UAAU;cACjB6D,UAAU,EAAE,GAAG;cACf5B,EAAE,EAAE,GAAG;cACPpB,QAAQ,EAAE;gBAAElB,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cAC5BkE,QAAQ,EAAE,QAAQ;cAClBC,YAAY,EAAE,UAAU;cACxB1D,OAAO,EAAE,aAAa;cACtB2D,eAAe,EAAE,CAAC;cAClBC,eAAe,EAAE;YACnB,CAAE;YAAArD,QAAA,EACC+C,IAAI,CAACO;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACb7C,OAAA,CAACjC,GAAG;YAACqD,EAAE,EAAE;cAAEa,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAEgC,QAAQ,EAAE;YAAO,CAAE;YAAA3B,QAAA,gBAC3ExC,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAChCQ,KAAK,EAAE,cAAc;gBACrBmB,UAAU,EAAE,GAAG;gBACfN,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAC7B,CAAE;cAAAgB,QAAA,EACC+C,IAAI,CAACQ,aAAa,IAAIR,IAAI,CAACS,QAAQ,IAAI;YAAM;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACb7C,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAAEQ,KAAK,EAAE,UAAU;gBAAEa,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAAE,CAAE;cAAAgB,QAAA,EAAC;YAEvF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7C,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAChCQ,KAAK,EAAE,UAAU;gBACjBa,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAC7B,CAAE;cAAAgB,QAAA,EACCqC,UAAU,CAACU,IAAI,CAACU,UAAU,IAAIV,IAAI,CAACR,IAAI;YAAC;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7C,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YACP4C,KAAK,EAAE;cAAEzC,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG,CAAC;YACzByC,MAAM,EAAE;cAAE1C,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG,CAAC;YAC1BO,YAAY,EAAE,CAAC;YACf2D,QAAQ,EAAE,QAAQ;YAClBQ,UAAU,EAAE;UACd,CAAE;UAAA1D,QAAA,eACAxC,OAAA,CAACjC,GAAG;YACFoI,SAAS,EAAC,KAAK;YACfC,GAAG,EAAEjG,WAAW,CAACoF,IAAI,CAACc,KAAK,CAAE;YAC7BC,GAAG,EAAEf,IAAI,CAACO,KAAM;YAChB1E,EAAE,EAAE;cACF4C,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdsC,SAAS,EAAE;YACb,CAAE;YACFC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,2CAA2C;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GApEE0C,IAAI,CAACoB,EAAE,IAAIrC,KAAK;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqErB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAAC+D,GAAA,GAhHQjC,WAAW;AAkHpB,SAASkC,kBAAkBA,CAAC;EAAEjC,QAAQ;EAAEkC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAChD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzJ,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC0J,QAAQ,EAAEC,WAAW,CAAC,GAAG3J,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACAD,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAACG,WAAW,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA,KAAK;IACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;IAChC,OAAO,MAAMC,QAAQ,CAACG,IAAI,CAACE,WAAW,CAACN,KAAK,CAAC;EAC/C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,UAAU,GAAGhD,QAAQ,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAEvC9H,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,IAAI,CAACF,QAAQ,IAAIU,UAAU,CAACtE,MAAM,IAAI,CAAC,EAAE;IAEzC,MAAMuE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCb,eAAe,CAAEc,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,UAAU,CAACtE,MAAM,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAM0E,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACX,QAAQ,EAAEU,UAAU,CAACtE,MAAM,CAAC,CAAC;EAEjC,MAAM2E,UAAU,GAAGA,CAAA,KAAM;IACvBhB,eAAe,CAAEc,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,UAAU,CAACtE,MAAM,CAAC;IACzD6D,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMe,UAAU,GAAGA,CAAA,KAAM;IACvBjB,eAAe,CAAEc,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,GAAGH,UAAU,CAACtE,MAAM,IAAIsE,UAAU,CAACtE,MAAM,CAAC;IAC7E6D,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMtC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;EAED,IAAIyC,UAAU,CAACtE,MAAM,KAAK,CAAC,EAAE;IAC3B,oBACEtD,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB2C,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;UAAE1C,EAAE,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAI,CAAC;QAC5BO,YAAY,EAAE,CAAC;QACf2D,QAAQ,EAAE,QAAQ;QAClB7B,EAAE,EAAE,CAAC;QACL7B,SAAS,EAAE,CAAC;QACZL,OAAO,EAAE,UAAU;QACnBM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBkC,cAAc,EAAE;MAClB,CAAE;MAAA5B,QAAA,eACAxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UAAE+G,SAAS,EAAE,QAAQ;UAAEvG,KAAK,EAAE;QAAW,CAAE;QAAAY,QAAA,gBAClDxC,OAAA,CAAClC,UAAU;UAACgF,OAAO,EAAC,IAAI;UAAC1B,EAAE,EAAE;YAAEyC,EAAE,EAAE,CAAC;YAAEd,UAAU,EAAE;UAAI,CAAE;UAAAP,QAAA,EAAC;QAEzD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7C,OAAA,CAAClC,UAAU;UAACgF,OAAO,EAAC,OAAO;UAAC1B,EAAE,EAAE;YAAEQ,KAAK,EAAE;UAAW,CAAE;UAAAY,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7C,OAAA,CAACjC,GAAG;IAACqD,EAAE,EAAE;MACPC,QAAQ,EAAE,UAAU;MACpB2C,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;QAAE1C,EAAE,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAI,CAAC;MAAE;MAC9BO,YAAY,EAAE,CAAC;MACf2D,QAAQ,EAAE,QAAQ;MAClB7B,EAAE,EAAE,CAAC;MACL7B,SAAS,EAAE;IACb,CAAE;IAAAQ,QAAA,gBAEAxC,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,EAAE;QACP8G,IAAI,EAAE,EAAE;QACR1G,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,YAAY;QACrBC,KAAK,EAAE,OAAO;QACdC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,GAAG;QACPC,YAAY,EAAE,CAAC;QACfE,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAK,QAAA,gBACAxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UACP4C,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTlC,YAAY,EAAE,KAAK;UACnBJ,OAAO,EAAE,OAAO;UAChB0G,SAAS,EAAE;QACb;MAAE;QAAA3F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACL7C,OAAA,CAAClC,UAAU;QAACgF,OAAO,EAAC,SAAS;QAAC1B,EAAE,EAAE;UAAE2B,UAAU,EAAE,GAAG;UAAEN,QAAQ,EAAE;QAAG,CAAE;QAAAD,QAAA,EAAC;MAErE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN7C,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAE2C,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAzB,QAAA,EAC9DoF,UAAU,CAACrE,GAAG,CAAC,CAAC+E,IAAI,EAAEhE,KAAK,kBAC1BtE,OAAA,CAACjC,GAAG;QAEFqD,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC;UACN8G,IAAI,EAAE,CAAC;UACPpE,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdsE,OAAO,EAAEjE,KAAK,KAAK0C,YAAY,GAAG,CAAC,GAAG,CAAC;UACvC3E,UAAU,EAAE,0BAA0B;UACtCJ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAE;QAAAM,QAAA,eAEFxC,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YACPC,QAAQ,EAAE,UAAU;YACpB2C,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACduE,UAAU,EAAE,0DAA0DrI,WAAW,CAACmI,IAAI,CAACjC,KAAK,CAAC,GAAG;YAChGoC,cAAc,EAAE,OAAO;YACvBC,kBAAkB,EAAE,QAAQ;YAC5BzG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,UAAU;YACtB0B,CAAC,EAAE;UACL,CAAE;UAAApB,QAAA,eACAxC,OAAA,CAACjC,GAAG;YAACqD,EAAE,EAAE;cAAEQ,KAAK,EAAE,OAAO;cAAEoC,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,gBACzCxC,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAC3B2B,UAAU,EAAE,GAAG;gBACfc,EAAE,EAAE,CAAC;gBACL8E,UAAU,EAAE,6BAA6B;gBACzClG,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEqH,EAAE,EAAE,EAAE;kBAAEpH,EAAE,EAAE;gBAAG;cACrC,CAAE;cAAAgB,QAAA,EACC8F,IAAI,CAACxC;YAAK;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACb7C,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAChCQ,KAAK,EAAE,UAAU;gBACjB+G,UAAU,EAAE,6BAA6B;gBACzClG,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAC7B,CAAE;cAAAgB,QAAA,GACCqC,UAAU,CAACyD,IAAI,CAACvD,IAAI,CAAC,EAAC,UAAG,EAACuD,IAAI,CAACtC,QAAQ;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAzCDyF,IAAI,CAAC3B,EAAE,IAAIrC,KAAK;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0ClB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL+E,UAAU,CAACtE,MAAM,GAAG,CAAC,iBACpBtD,OAAA,CAAAE,SAAA;MAAAsC,QAAA,gBACExC,OAAA,CAACb,UAAU;QACTqF,OAAO,EAAE0D,UAAW;QACpB9G,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpB+G,IAAI,EAAE,CAAC;UACP9G,GAAG,EAAE,KAAK;UACVc,SAAS,EAAE,kBAAkB;UAC7BR,KAAK,EAAE,OAAO;UACd,SAAS,EAAE;YAAED,OAAO,EAAE;UAAkB,CAAC;UACzCD,MAAM,EAAE;QACV,CAAE;QAAAc,QAAA,eAEFxC,OAAA,CAACP,kBAAkB;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACb7C,OAAA,CAACb,UAAU;QACTqF,OAAO,EAAEyD,UAAW;QACpB7G,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBI,KAAK,EAAE,CAAC;UACRH,GAAG,EAAE,KAAK;UACVc,SAAS,EAAE,kBAAkB;UAC7BR,KAAK,EAAE,OAAO;UACd,SAAS,EAAE;YAAED,OAAO,EAAE;UAAkB,CAAC;UACzCD,MAAM,EAAE;QACV,CAAE;QAAAc,QAAA,eAEFxC,OAAA,CAACR,gBAAgB;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACb,CACH,EAGA+E,UAAU,CAACtE,MAAM,GAAG,CAAC,iBACpBtD,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBwH,MAAM,EAAE,EAAE;QACVT,IAAI,EAAE,KAAK;QACXhG,SAAS,EAAE,kBAAkB;QAC7BH,OAAO,EAAE,MAAM;QACfE,GAAG,EAAE,CAAC;QACNT,MAAM,EAAE;MACV,CAAE;MAAAc,QAAA,EACCoF,UAAU,CAACrE,GAAG,CAAC,CAACuF,CAAC,EAAExE,KAAK,kBACvBtE,OAAA,CAACjC,GAAG;QAEFyG,OAAO,EAAEA,CAAA,KAAM;UACbyC,eAAe,CAAC3C,KAAK,CAAC;UACtB6C,WAAW,CAAC,KAAK,CAAC;QACpB,CAAE;QACF/F,EAAE,EAAE;UACF4C,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTlC,YAAY,EAAE,KAAK;UACnBJ,OAAO,EAAE2C,KAAK,KAAK0C,YAAY,GAAG,OAAO,GAAG,uBAAuB;UACnEvC,MAAM,EAAE,SAAS;UACjBpC,UAAU,EAAE;QACd;MAAE,GAZGiC,KAAK;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACkE,EAAA,CAlOQF,kBAAkB;AAAAkC,GAAA,GAAlBlC,kBAAkB;AAoO3B,SAASmC,QAAQA,CAAC;EAAEV,IAAI;EAAEW,QAAQ;EAAEC,QAAQ;EAAEC,UAAU;EAAEC,YAAY;EAAEtG,OAAO;EAAEuG,OAAO;EAAEC,UAAU;EAAEC,YAAY;EAAEC,WAAW;EAAEC,eAAe;EAAEC,MAAM;EAAEC;AAAQ,CAAC,EAAE;EACnK,MAAMC,WAAW,GAAG9G,OAAO,KAAK,SAAS,GAAG,QAAQ,GAAG,MAAM;EAE7D,MAAM+G,eAAe,GAAIpD,CAAC,IAAK;IAC7B;IACA,IAAIA,CAAC,CAACC,MAAM,CAACoD,OAAO,CAAC,QAAQ,CAAC,IAAIrD,CAAC,CAACC,MAAM,CAACoD,OAAO,CAAC,iBAAiB,CAAC,EAAE;MACrE;IACF;IACAN,WAAW,IAAIA,WAAW,CAAClB,IAAI,CAAC3B,EAAE,CAAC;EACrC,CAAC;EAED,oBACE3G,OAAA,CAAC7B,IAAI;IACHiD,EAAE,EAAE;MACFa,OAAO,EAAE,MAAM;MACf8H,aAAa,EAAE,QAAQ;MACvB9F,MAAM,EAAE,MAAM;MACd5C,QAAQ,EAAE,UAAU;MACpBkB,QAAQ,EAAE;QAAEhB,EAAE,EAAE,MAAM;QAAEqH,EAAE,EAAE,GAAG;QAAEpH,EAAE,EAAE;MAAI,CAAC;MAC1Cc,QAAQ,EAAE;QAAEf,EAAE,EAAE,OAAO;QAAEqH,EAAE,EAAE,GAAG;QAAEpH,EAAE,EAAE;MAAI,CAAC;MAC3CwI,EAAE,EAAE,MAAM;MACVjI,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZ4B,CAAC,EAAE,CAAC;MACJjC,OAAO,EAAE,MAAM;MACfU,UAAU,EAAE,yDAAyD;MACrE,SAAS,EAAE;QACTD,SAAS,EAAE,kBAAkB;QAC7BJ,SAAS,EAAE;MACb;IACF,CAAE;IAAAQ,QAAA,gBAGFxC,OAAA,CAACjC,GAAG;MACFyG,OAAO,EAAEqF,eAAgB;MACzBzI,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpB2C,KAAK,EAAE,MAAM;QACbiG,UAAU,EAAEL,WAAW;QACvB7H,YAAY,EAAE,CAAC;QACf2D,QAAQ,EAAE,QAAQ;QAClBjB,MAAM,EAAE;MACV,CAAE;MAAAjC,QAAA,eAEFxC,OAAA,CAAC3B,SAAS;QACR8H,SAAS,EAAC,KAAK;QACf/E,EAAE,EAAE;UAAEC,QAAQ,EAAE,UAAU;UAAEC,GAAG,EAAE,CAAC;UAAE8G,IAAI,EAAE,CAAC;UAAEpE,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE,MAAM;UAAEsC,SAAS,EAAE;QAAQ,CAAE;QACjGF,KAAK,EAAElG,WAAW,CAACmI,IAAI,CAACjC,KAAK,CAAE;QAC/BC,GAAG,EAAEgC,IAAI,CAACxC,KAAM;QAChBU,OAAO,EAAGC,CAAC,IAAK;UAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAGtD,OAAO,KAAK,SAAS,GAAG,6CAA6C,GAAG,2CAA2C;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN7C,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEI,KAAK,EAAE,EAAE;QAAEoH,MAAM,EAAE,EAAE;QAAEnH,MAAM,EAAE,CAAC;QAAEwI,EAAE,EAAE,CAAC;QAAEjI,OAAO,EAAE,MAAM;QAAEE,GAAG,EAAE;MAAE,CAAE;MAAAK,QAAA,gBAClGxC,OAAA,CAACZ,OAAO;QAAC0G,KAAK,EAAE6D,OAAO,GAAG,YAAY,GAAG,iBAAkB;QAAAnH,QAAA,eACzDxC,OAAA,CAACb,UAAU;UACTyC,KAAK,EAAE+H,OAAO,GAAG,OAAO,GAAG,SAAU;UACrCQ,IAAI,EAAC,QAAQ;UACb3F,OAAO,EAAGiC,CAAC,IAAK;YACdA,CAAC,CAAC2D,eAAe,CAAC,CAAC,CAAC,CAAC;YACrBV,MAAM,CAACpB,IAAI,CAAC;UACd,CAAE;UACFlH,EAAE,EAAE;YACFiB,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE;cACTD,SAAS,EAAE,YAAY;cACvBR,KAAK,EAAE;YACT,CAAC;YACD,UAAU,EAAE;cACVQ,SAAS,EAAE;YACb,CAAC;YACD,IAAIuH,OAAO,IAAI;cACbtB,SAAS,EAAE,4BAA4B;cACvC,sBAAsB,EAAE;gBACtB,IAAI,EAAE;kBAAEjG,SAAS,EAAE;gBAAW,CAAC;gBAC/B,KAAK,EAAE;kBAAEA,SAAS,EAAE;gBAAa,CAAC;gBAClC,KAAK,EAAE;kBAAEA,SAAS,EAAE;gBAAW,CAAC;gBAChC,KAAK,EAAE;kBAAEA,SAAS,EAAE;gBAAa,CAAC;gBAClC,KAAK,EAAE;kBAAEA,SAAS,EAAE;gBAAW;cACjC;YACF,CAAC;UACH,CAAE;UAAAI,QAAA,EAEDmH,OAAO,gBACN3J,OAAA,CAACf,YAAY;YAACwD,QAAQ,EAAC,QAAQ;YAACrB,EAAE,EAAE;cAAEQ,KAAK,EAAE;YAAU;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5D7C,OAAA,CAACd,kBAAkB;YAACuD,QAAQ,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACxC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACV7C,OAAA,CAACZ,OAAO;QAAC0G,KAAK,EACZ,CAAC2D,eAAe,GAAG,8BAA8B,GACjDF,YAAY,GAAG,qBAAqB,GAAG,oBACxC;QAAA/G,QAAA,eACCxC,OAAA,CAACb,UAAU;UACTyC,KAAK,EAAE2H,YAAY,GAAG,SAAS,GAAG,SAAU;UAC5CY,IAAI,EAAC,QAAQ;UACb3F,OAAO,EAAGiC,CAAC,IAAK;YACdA,CAAC,CAAC2D,eAAe,CAAC,CAAC,CAAC,CAAC;YACrBd,UAAU,CAAChB,IAAI,CAAC;UAClB,CAAE;UACFlH,EAAE,EAAE;YACFiB,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE;cAAED,SAAS,EAAE;YAAa;UACvC,CAAE;UAAAI,QAAA,EAED+G,YAAY,gBACXvJ,OAAA,CAAChB,iBAAiB;YAACyD,QAAQ,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEvC7C,OAAA,CAACjB,eAAe;YAAC0D,QAAQ,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACrC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACV7C,OAAA,CAACZ,OAAO;QAAC0G,KAAK,EAAC,SAAS;QAAAtD,QAAA,eACtBxC,OAAA,CAACb,UAAU;UACTyC,KAAK,EAAC,SAAS;UACfuI,IAAI,EAAC,QAAQ;UACb3F,OAAO,EAAGiC,CAAC,IAAK;YACdA,CAAC,CAAC2D,eAAe,CAAC,CAAC,CAAC,CAAC;YACrBf,OAAO,CAACf,IAAI,CAAC;UACf,CAAE;UAAA9F,QAAA,eAEFxC,OAAA,CAAClB,SAAS;YAAC2D,QAAQ,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAEN7C,OAAA,CAAC5B,WAAW;MACVoG,OAAO,EAAEqF,eAAgB;MACzBzI,EAAE,EAAE;QACFoE,IAAI,EAAE,CAAC;QACPvD,OAAO,EAAE,MAAM;QACf8H,aAAa,EAAE,QAAQ;QACvBM,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACL7F,MAAM,EAAE;MACV,CAAE;MAAAjC,QAAA,gBAEFxC,OAAA,CAAClC,UAAU;QAACgF,OAAO,EAAC,IAAI;QAAC1B,EAAE,EAAE;UAAE2B,UAAU,EAAE,GAAG;UAAEnB,KAAK,EAAE,cAAc;UAAEiC,EAAE,EAAE,GAAG;UAAEpB,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAAE,CAAE;QAAAgB,QAAA,EAAE8F,IAAI,CAACxC;MAAK;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzI7C,OAAA,CAAClC,UAAU;QAACgF,OAAO,EAAC,SAAS;QAAC1B,EAAE,EAAE;UAAEQ,KAAK,EAAE,UAAU;UAAEiC,EAAE,EAAE,CAAC;UAAEpB,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAAE,CAAE;QAAAgB,QAAA,GAAC,YAClF,eAAAxC,OAAA,CAAClC,UAAU;UAACqI,SAAS,EAAC,MAAM;UAAC/E,EAAE,EAAE;YAAE2B,UAAU,EAAE,GAAG;YAAEnB,KAAK,EAAE;UAAiB,CAAE;UAAAY,QAAA,EAAE8F,IAAI,CAACvC,aAAa,IAAIuC,IAAI,CAACtC,QAAQ,IAAI;QAAM;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,YAAQ,EAACsG,UAAU,CAACb,IAAI,CAACrC,UAAU,IAAIqC,IAAI,CAACvD,IAAI,CAAC;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5L,CAAC,eACb7C,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UAAEyC,EAAE,EAAE,CAAC;UAAEK,EAAE,EAAE;QAAG,CAAE;QAAA1B,QAAA,GAAC,GAAC,eAC3BxC,OAAA,CAAClC,UAAU;UACTgF,OAAO,EAAC,OAAO;UACf1B,EAAE,EAAE;YACFQ,KAAK,EAAE,UAAU;YACjBa,QAAQ,EAAE;cAAElB,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG,CAAC;YAC5BiE,UAAU,EAAE,GAAG;YACfxD,OAAO,EAAE,aAAa;YACtB2D,eAAe,EAAEqD,QAAQ,GAAG,MAAM,GAAG,CAAC;YACtCpD,eAAe,EAAE,UAAU;YAC3BH,QAAQ,EAAE,QAAQ;YAClBC,YAAY,EAAE,UAAU;YACxB9B,EAAE,EAAE;UACN,CAAE;UAAArB,QAAA,EAED8F,IAAI,CAACiC;QAAW;UAAA7H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EACZyF,IAAI,CAACiC,WAAW,CAACjH,MAAM,GAAG,GAAG,iBAC5BtD,OAAA,CAAChC,MAAM;UACLmM,IAAI,EAAC,OAAO;UACZvI,KAAK,EAAC,SAAS;UACf4C,OAAO,EAAGiC,CAAC,IAAK;YACdA,CAAC,CAAC2D,eAAe,CAAC,CAAC,CAAC,CAAC;YACrBlB,QAAQ,CAAC,CAAC;UACZ,CAAE;UACF9H,EAAE,EAAE;YACFoJ,SAAS,EAAE,YAAY;YACvBC,aAAa,EAAE,MAAM;YACrB1H,UAAU,EAAE,GAAG;YACfN,QAAQ,EAAE,QAAQ;YAClBmB,CAAC,EAAE,GAAG;YACNrB,QAAQ,EAAE;UACZ,CAAE;UAAAC,QAAA,EAEDyG,QAAQ,GAAG,oBAAoB,GAAG;QAAmB;UAAAvG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAAC6H,GAAA,GAvLQ1B,QAAQ;AAyLjB,SAAS2B,gBAAgBA,CAAC;EAAE7D,MAAM;EAAElC,QAAQ;EAAEgG,WAAW;EAAExH,cAAc;EAAEF,gBAAgB;EAAEC,mBAAmB;EAAE0H,aAAa;EAAEC,gBAAgB;EAAEC,OAAO;EAAEC,SAAS;EAAEC,YAAY;EAAEC,aAAa;EAAEC,WAAW;EAAEC,kBAAkB;EAAEC,WAAW;EAAE/B,UAAU;EAAEgC,cAAc;EAAEC,aAAa;EAAEC,qBAAqB;EAAEhC,WAAW;EAAEiC,UAAU;EAAEC,QAAQ;EAAEC,IAAI;EAAElC,eAAe;EAAEmC,YAAY;EAAElC,MAAM;EAAEmC;AAAU,CAAC,EAAE;EAC/Y,MAAMzC,YAAY,GAAGA,CAAC0C,IAAI,EAAEC,SAAS,GAAG,GAAG,KAAK;IAC9C,IAAID,IAAI,CAACxI,MAAM,IAAIyI,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EACD,MAAM5C,UAAU,GAAIrE,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACkH,kBAAkB,CAAC,OAAO,EAAE;MAAEC,GAAG,EAAE,SAAS;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC;EAC7F,CAAC;EACD,MAAMC,YAAY,GAAGzH,QAAQ,CAAC0H,MAAM,CAAChE,IAAI,IACvCpF,gBAAgB,KAAK,OAAO,IAC5BoF,IAAI,CAACtC,QAAQ,KAAK9C,gBAAgB,IAClCoF,IAAI,CAACvC,aAAa,KAAK7C,gBACzB,CAAC,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACb,oBACErF,OAAA,CAACjC,GAAG;IAACqD,EAAE,EAAE;MAAEmL,SAAS,EAAE,OAAO;MAAE5K,OAAO,EAAE,oBAAoB;MAAEqC,KAAK,EAAE,OAAO;MAAE0B,QAAQ,EAAE;IAAS,CAAE;IAAAlD,QAAA,gBAEjGxC,OAAA,CAACpC,MAAM;MAACyD,QAAQ,EAAC,OAAO;MAACO,KAAK,EAAC,SAAS;MAAC4K,SAAS,EAAE,CAAE;MAACpL,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAE8K,YAAY,EAAE,CAAC;QAAE1I,WAAW,EAAE,UAAU;QAAErC,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eACrIxC,OAAA,CAACnC,OAAO;QAACuD,EAAE,EAAE;UAAEmL,SAAS,EAAE,EAAE;UAAE1K,EAAE,EAAE;QAAE,CAAE;QAAAW,QAAA,gBACpCxC,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEwK,QAAQ,EAAE;UAAE,CAAE;UAAAlK,QAAA,gBAC9DxC,OAAA,CAAC1B,MAAM;YACL8H,GAAG,EAAEU,MAAM,CAAC6F,IAAI,IAAI,cAAe;YACnCrG,GAAG,EAAC,MAAM;YACVlF,EAAE,EAAE;cACF4C,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,EAAE,EAAE,CAAC;cACLO,MAAM,EAAE;YACV,CAAE;YACF+B,OAAO,EAAGC,CAAC,IAAK;cACdpG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;cACzCmG,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAC/B,CAAE;YACF5B,OAAO,EAAEA,CAAA,KAAMkH,QAAQ,CAAC,GAAG;UAAE;YAAAhJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACF7C,OAAA,CAAClC,UAAU;YACTgF,OAAO,EAAC,IAAI;YACZ1B,EAAE,EAAE;cACF2B,UAAU,EAAE,GAAG;cACfnB,KAAK,EAAE,cAAc;cACrBa,QAAQ,EAAE,EAAE;cACZgC,MAAM,EAAE;YACV,CAAE;YACFD,OAAO,EAAEA,CAAA,KAAMkH,QAAQ,CAAC,GAAG,CAAE;YAAAlJ,QAAA,EAE5BsE,MAAM,CAAChB,KAAK,IAAI;UAAmB;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN7C,OAAA,CAACb,UAAU;UAACyN,IAAI,EAAC,KAAK;UAAChL,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAEA,CAAA,KAAMqI,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,sEAAuE;UAAC3L,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAC4B,KAAK,EAAC,OAAO;UAAAtD,QAAA,eAC/KxC,OAAA;YAAGgN,SAAS,EAAC,oBAAoB;YAAC3F,KAAK,EAAE;cAAE5E,QAAQ,EAAE;YAAG;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACb7C,OAAA,CAACb,UAAU;UAACyN,IAAI,EAAC,KAAK;UAAChL,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAE+G,aAAc;UAACnK,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC3ExC,OAAA,CAACT,UAAU;YAACkD,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACb7C,OAAA,CAACb,UAAU;UAACyN,IAAI,EAAC,KAAK;UAAChL,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAE0G,aAAc;UAAA1I,QAAA,eAC5DxC,OAAA,CAACxB,QAAQ;YAACiE,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAET7C,OAAA,CAACrC,MAAM;MACLsP,MAAM,EAAC,OAAO;MACdC,IAAI,EAAE/B,WAAY;MAClBhK,OAAO,EAAEiK,kBAAmB;MAC5B+B,UAAU,EAAE;QAAEC,WAAW,EAAE;MAAK,CAAE;MAClChM,EAAE,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eAErBxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UAAE4C,KAAK,EAAE,GAAG;UAAEJ,CAAC,EAAE,CAAC;UAAEvC,QAAQ,EAAE;QAAW,CAAE;QAAAmB,QAAA,gBAClDxC,OAAA,CAACb,UAAU;UACTqF,OAAO,EAAE4G,kBAAmB;UAC5BhK,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEC,GAAG,EAAE,CAAC;YAAEG,KAAK,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAG,CAAE;UAC3D,cAAW,OAAO;UAAAc,QAAA,eAElBxC,OAAA,CAACX,SAAS;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACb7C,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE2B,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,gBACxDxC,OAAA,CAAC1B,MAAM;YAAC8H,GAAG,EAAEU,MAAM,CAAC6F,IAAK;YAACrG,GAAG,EAAC,MAAM;YAAClF,EAAE,EAAE;cAAE4C,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAACsC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjI7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEa,QAAQ,EAAE;YAAG,CAAE;YAAAD,QAAA,EAAEsE,MAAM,CAAChB;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eACN7C,OAAA,CAACzB,OAAO;UAAC6C,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1B7C,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,gBACjBxC,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAClC2B,UAAU,EAAE,GAAG;cACfnB,KAAK,EAAE,gBAAgB;cACvBiC,EAAE,EAAE,GAAG;cACPpB,QAAQ,EAAE,EAAE;cACZgI,aAAa,EAAE,WAAW;cAC1B4C,aAAa,EAAE;YACjB,CAAE;YAAA7K,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAAC9B,KAAK;YAACoH,OAAO,EAAE,GAAI;YAAA9C,QAAA,gBAClBxC,OAAA,CAAChC,MAAM;cACL8E,OAAO,EAAC,MAAM;cACdwK,SAAS;cACTlM,EAAE,EAAE;gBACFgD,cAAc,EAAE,YAAY;gBAC5BqG,aAAa,EAAE,MAAM;gBACrB7I,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACT2L,eAAe,EAAE;gBACnB;cACF,CAAE;cACF/I,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIiF,eAAe,EAAE;kBACnBoD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;gBACjC,CAAC,MAAM;kBACLrB,QAAQ,CAAC,aAAa,CAAC;gBACzB;cACF,CAAE;cAAAlJ,QAAA,gBAEFxC,OAAA;gBAAGgN,SAAS,EAAC,iBAAiB;gBAAC3F,KAAK,EAAE;kBAAEmG,WAAW,EAAE,CAAC;kBAAE/K,QAAQ,EAAE;gBAAG;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oBAE9E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7C,OAAA,CAAChC,MAAM;cACL8E,OAAO,EAAC,MAAM;cACdwK,SAAS;cACTlM,EAAE,EAAE;gBACFgD,cAAc,EAAE,YAAY;gBAC5BqG,aAAa,EAAE,MAAM;gBACrB7I,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACT2L,eAAe,EAAE;gBACnB;cACF,CAAE;cACF/I,OAAO,EAAEA,CAAA,KAAM;gBACb4G,kBAAkB,CAAC,CAAC;gBACpBK,UAAU,CAAC,CAAC;cACd,CAAE;cAAAjJ,QAAA,gBAEFxC,OAAA;gBAAGgN,SAAS,EAAC,eAAe;gBAAC3F,KAAK,EAAE;kBAAEmG,WAAW,EAAE,CAAC;kBAAE/K,QAAQ,EAAE;gBAAG;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE5E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7C,OAAA,CAAChC,MAAM;cACL8E,OAAO,EAAC,MAAM;cACdwK,SAAS;cACTlM,EAAE,EAAE;gBACFgD,cAAc,EAAE,YAAY;gBAC5BqG,aAAa,EAAE,MAAM;gBACrB7I,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACT2L,eAAe,EAAE;gBACnB;cACF,CAAE;cACF/I,OAAO,EAAEA,CAAA,KAAM;gBACb4G,kBAAkB,CAAC,CAAC;gBACpByB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,sEAAsE;cAC/F,CAAE;cAAAvK,QAAA,gBAEFxC,OAAA;gBAAGgN,SAAS,EAAC,oBAAoB;gBAAC3F,KAAK,EAAE;kBAAEmG,WAAW,EAAE,CAAC;kBAAE/K,QAAQ,EAAE;gBAAG;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAEjF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN7C,OAAA,CAACzB,OAAO;UAAC6C,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1B7C,OAAA,CAACjC,GAAG;UAAAyE,QAAA,gBACFxC,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAClC2B,UAAU,EAAE,GAAG;cACfnB,KAAK,EAAE,gBAAgB;cACvBiC,EAAE,EAAE,GAAG;cACPpB,QAAQ,EAAE,EAAE;cACZgI,aAAa,EAAE,WAAW;cAC1B4C,aAAa,EAAE;YACjB,CAAE;YAAA7K,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAAC9B,KAAK;YAACoH,OAAO,EAAE,CAAE;YAAA9C,QAAA,EACfiH,eAAe;YAAA;YACd;YACAzJ,OAAA,CAAAE,SAAA;cAAAsC,QAAA,gBACExC,OAAA,CAACjC,GAAG;gBAACqD,EAAE,EAAE;kBACPa,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,GAAG,EAAE,CAAC;kBACNyB,CAAC,EAAE,CAAC;kBACJjC,OAAO,EAAE,SAAS;kBAClBI,YAAY,EAAE,CAAC;kBACf+B,MAAM,EAAE,WAAW;kBACnBC,WAAW,EAAE;gBACf,CAAE;gBAAAvB,QAAA,gBACAxC,OAAA,CAACF,iBAAiB;kBAACsB,EAAE,EAAE;oBAAEQ,KAAK,EAAE,cAAc;oBAAEa,QAAQ,EAAE;kBAAG;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE7C,OAAA,CAACjC,GAAG;kBAAAyE,QAAA,gBACFxC,OAAA,CAAClC,UAAU;oBAACgF,OAAO,EAAC,WAAW;oBAACC,UAAU,EAAE,GAAI;oBAAAP,QAAA,EAC7C,CAAAmJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAElI,IAAI,KAAI;kBAAM;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACb7C,OAAA,CAAClC,UAAU;oBAACgF,OAAO,EAAC,SAAS;oBAAClB,KAAK,EAAC,gBAAgB;oBAAAY,QAAA,EACjD,CAAAmJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,KAAI;kBAAkB;oBAAA/K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN7C,OAAA,CAAChC,MAAM;gBACL8E,OAAO,EAAC,UAAU;gBAClBlB,KAAK,EAAC,OAAO;gBACb0L,SAAS;gBACTI,SAAS,eAAE1N,OAAA,CAACH,UAAU;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BzB,EAAE,EAAE;kBACFqJ,aAAa,EAAE,MAAM;kBACrB1G,WAAW,EAAE,YAAY;kBACzBnC,KAAK,EAAE,YAAY;kBACnB,SAAS,EAAE;oBACTmC,WAAW,EAAE,YAAY;oBACzBwJ,eAAe,EAAE,YAAY;oBAC7B3L,KAAK,EAAE;kBACT;gBACF,CAAE;gBACF4C,OAAO,EAAEoH,YAAa;gBAAApJ,QAAA,EACvB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CAAC;YAAA;YAEH;YACA7C,OAAA,CAAAE,SAAA;cAAAsC,QAAA,gBACExC,OAAA,CAAChC,MAAM;gBACL8E,OAAO,EAAC,UAAU;gBAClBlB,KAAK,EAAC,SAAS;gBACf0L,SAAS;gBACTI,SAAS,eAAE1N,OAAA,CAACL,SAAS;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBzB,EAAE,EAAE;kBACFqJ,aAAa,EAAE,MAAM;kBACrB1G,WAAW,EAAE,cAAc;kBAC3BnC,KAAK,EAAE,cAAc;kBACrB,SAAS,EAAE;oBACTmC,WAAW,EAAE,cAAc;oBAC3BwJ,eAAe,EAAE,cAAc;oBAC/B3L,KAAK,EAAE;kBACT;gBACF,CAAE;gBACF4C,OAAO,EAAEA,CAAA,KAAMkH,QAAQ,CAAC,aAAa,CAAE;gBAAAlJ,QAAA,EACxC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7C,OAAA,CAAChC,MAAM;gBACL8E,OAAO,EAAC,WAAW;gBACnBlB,KAAK,EAAC,SAAS;gBACf0L,SAAS;gBACTI,SAAS,eAAE1N,OAAA,CAACJ,aAAa;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7BzB,EAAE,EAAE;kBACFqJ,aAAa,EAAE,MAAM;kBACrB8C,eAAe,EAAE,cAAc;kBAC/B,SAAS,EAAE;oBACTA,eAAe,EAAE;kBACnB;gBACF,CAAE;gBACF/I,OAAO,EAAEA,CAAA,KAAMkH,QAAQ,CAAC,gBAAgB,CAAE;gBAAAlJ,QAAA,EAC3C;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET7C,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAE8K,YAAY,EAAE,CAAC;QAAE1I,WAAW,EAAE,UAAU;QAAEjC,EAAE,EAAE,CAAC;QAAET,QAAQ,EAAE,OAAO;QAAEC,GAAG,EAAE,EAAE;QAAE8G,IAAI,EAAE,CAAC;QAAE3G,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eACzIxC,OAAA,CAAC9B,KAAK;QAACyP,SAAS,EAAC,KAAK;QAACrI,OAAO,EAAE,CAAE;QAAClE,EAAE,EAAE;UAAEwM,SAAS,EAAE,MAAM;UAAE/L,EAAE,EAAE,CAAC;UAAE,sBAAsB,EAAE;YAAEI,OAAO,EAAE;UAAO,CAAC;UAAE4L,cAAc,EAAE,MAAM;UAAEC,eAAe,EAAE;QAAO,CAAE;QAAAtL,QAAA,EAC/JzB,UAAU,CAACwC,GAAG,CAAEC,GAAG,iBAClBxD,OAAA,CAAC/B,IAAI;UAEHsG,KAAK,EAAEf,GAAI;UACX5B,KAAK,EAAEsB,gBAAgB,KAAKM,GAAG,GAAG,SAAS,GAAG,SAAU;UACxDgB,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAACK,GAAG,CAAE;UACxCpC,EAAE,EAAE;YAAE2B,UAAU,EAAE,GAAG;YAAE0B,MAAM,EAAE,SAAS;YAAEyB,UAAU,EAAE,CAAC;YAAE6H,UAAU,EAAE;UAAS;QAAE,GAJ3EvK,GAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKT,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN7C,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,GAAG,EAAE,GAAG;QAAE8G,IAAI,EAAE,CAAC;QAAE3G,KAAK,EAAE,CAAC;QAAEoH,MAAM,EAAE,EAAE;QAAEhH,EAAE,EAAE,CAAC;QAAES,QAAQ,EAAE,MAAM;QAAE0H,EAAE,EAAE,MAAM;QAAEhG,KAAK,EAAE,MAAM;QAAE/B,OAAO,EAAE,MAAM;QAAE8H,aAAa,EAAE,QAAQ;QAAEwC,SAAS,EAAE,CAAC;QAAE7K,MAAM,EAAE,CAAC;QAAE4I,EAAE,EAAE,CAAC;QAAED,EAAE,EAAE;MAAE,CAAE;MAAA7H,QAAA,eAC9MxC,OAAA,CAACjC,GAAG;QACFoI,SAAS,EAAC,MAAM;QAChB/E,EAAE,EAAE;UACFoE,IAAI,EAAE,CAAC;UACPvB,MAAM,EAAE,4BAA4B;UAAE;UACtC+J,SAAS,EAAE,MAAM;UACjBzB,SAAS,EAAE,CAAC;UACZjC,EAAE,EAAE,CAAC;UACLD,EAAE,EAAE,CAAC;UAAE;UACP,sBAAsB,EAAE;YAAEpI,OAAO,EAAE;UAAO,CAAC;UAC3C4L,cAAc,EAAE,MAAM;UACtBC,eAAe,EAAE;QACnB,CAAE;QAAAtL,QAAA,gBAGFxC,OAAA,CAAC6G,kBAAkB;UAACjC,QAAQ,EAAEA,QAAS;UAACkC,MAAM,EAAEA;QAAO;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1D7C,OAAA,CAACiD,WAAW;UAACC,gBAAgB,EAAEA,gBAAiB;UAACC,mBAAmB,EAAEA,mBAAoB;UAACC,cAAc,EAAEA;QAAe;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG7H7C,OAAA,CAAC2E,WAAW;UAACC,QAAQ,EAAEA;QAAS;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC7C,OAAA,CAACjC,GAAG;UACFqD,EAAE,EAAE;YACFa,OAAO,EAAE,MAAM;YACf8H,aAAa,EAAE,QAAQ;YACvB5H,GAAG,EAAE,CAAC;YACN6L,SAAS,EAAE,MAAM;YACjB3D,EAAE,EAAE;cAAE9I,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAC;YAAE;YACvB,sBAAsB,EAAE;cAAES,OAAO,EAAE;YAAO,CAAC;YAC3C4L,cAAc,EAAE,MAAM;YACtBC,eAAe,EAAE,MAAM;YACvBjM,EAAE,EAAE,CAAC;YACLoM,EAAE,EAAE,CAAC,CAAE;UACT,CAAE;UAAAzL,QAAA,GAEDuI,OAAO,GACNmD,KAAK,CAACC,IAAI,CAAC;YAAE7K,MAAM,EAAE;UAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACuF,CAAC,EAAEsF,GAAG,kBACnCpO,OAAA,CAAC7B,IAAI;YAAWiD,EAAE,EAAE;cAAEa,OAAO,EAAE,MAAM;cAAE8H,aAAa,EAAE,QAAQ;cAAE9F,MAAM,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,gBAC9FxC,OAAA,CAACjC,GAAG;cAACqD,EAAE,EAAE;gBAAEC,QAAQ,EAAE,UAAU;gBAAE2C,KAAK,EAAE,MAAM;gBAAEiG,UAAU,EAAE,MAAM;gBAAEtI,OAAO,EAAE;cAAW;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7F7C,OAAA,CAAC5B,WAAW;cAACgD,EAAE,EAAE;gBAAEoE,IAAI,EAAE,CAAC;gBAAEvD,OAAO,EAAE,MAAM;gBAAE8H,aAAa,EAAE,QAAQ;gBAAEM,EAAE,EAAE;cAAE,CAAE;cAAA7H,QAAA,gBAC5ExC,OAAA,CAACjC,GAAG;gBAACqD,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEkC,EAAE,EAAE,CAAC;kBAAE9B,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxE7C,OAAA,CAACjC,GAAG;gBAACqD,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEkC,EAAE,EAAE,CAAC;kBAAE9B,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxE7C,OAAA,CAACjC,GAAG;gBAACqD,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEI,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA,GANLuL,GAAG;YAAA1L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOR,CACP,CAAC,GACAwJ,YAAY,CAAC/I,MAAM,KAAK,CAAC,gBAC3BtD,OAAA,CAACjC,GAAG;YAACqD,EAAE,EAAE;cAAE+G,SAAS,EAAE,QAAQ;cAAErG,EAAE,EAAE,CAAC;cAAEF,KAAK,EAAE,UAAU;cAAEoC,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,gBACxExC,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,IAAI;cAAAN,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtD7C,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,GAENwJ,YAAY,CAAC9I,GAAG,CAAC,CAAC+E,IAAI,EAAE8F,GAAG,kBACzBpO,OAAA,CAACjC,GAAG;YAAsBqD,EAAE,EAAE;cAAE4C,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,eAC9CxC,OAAA,CAACgJ,QAAQ;cACPV,IAAI,EAAEA,IAAK;cACXW,QAAQ,EAAE,CAAC,CAAC4B,aAAa,CAACvC,IAAI,CAAC3B,EAAE,CAAE;cACnCuC,QAAQ,EAAEA,CAAA,KAAM4B,gBAAgB,CAACxC,IAAI,CAAC3B,EAAE,CAAE;cAC1CwC,UAAU,EAAEA,UAAW;cACvBC,YAAY,EAAEA,YAAa;cAC3BtG,OAAO,EAAC,QAAQ;cAChBuG,OAAO,EAAEgC,WAAY;cACrB/B,UAAU,EAAEA,UAAW;cACvBC,YAAY,EAAE+B,cAAc,CAAC+C,GAAG,CAAC/F,IAAI,CAAC3B,EAAE,CAAE;cAC1C6C,WAAW,EAAEA,WAAY;cACzBC,eAAe,EAAEA,eAAgB;cACjCC,MAAM,EAAEA,MAAO;cACfC,OAAO,EAAEkC,SAAS,CAACwC,GAAG,CAAC/F,IAAI,CAAC3B,EAAE;YAAE;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC,GAfMyF,IAAI,CAAC3B,EAAE,IAAIyH,GAAG;YAAA1L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBnB,CACN,CACF,eAGD7C,OAAA,CAACjC,GAAG;YAACqD,EAAE,EAAE;cAAE8I,EAAE,EAAE,CAAC;cAAEpI,EAAE,EAAE,CAAC;cAAEwM,SAAS,EAAE,mBAAmB;cAAEnG,SAAS,EAAE;YAAS,CAAE;YAAA3F,QAAA,eAC7ExC,OAAA,CAACvB,MAAM;cAACqH,KAAK,EAAEgB,MAAM,CAAChB;YAAM;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QACPC,QAAQ,EAAE,OAAO;QACjB+G,IAAI,EAAE,CAAC;QACP3G,KAAK,EAAE,CAAC;QACRoH,MAAM,EAAE,CAAC;QACTnH,MAAM,EAAE,IAAI;QACZO,OAAO,EAAE,OAAO;QAChBsL,eAAe,EAAE,OAAO;QACxBe,SAAS,EAAE,mBAAmB;QAC9BtM,SAAS,EAAE;MACb,CAAE;MAAAQ,QAAA,eACAxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UACPa,OAAO,EAAE,MAAM;UACfmC,cAAc,EAAE,cAAc;UAC9BlC,UAAU,EAAE,QAAQ;UACpB+B,MAAM,EAAE,EAAE;UACVpC,EAAE,EAAE;QACN,CAAE;QAAAW,QAAA,gBACAxC,OAAA,CAACjC,GAAG;UACFyG,OAAO,EAAEA,CAAA,KAAMgH,qBAAqB,CAAC,CAAC,CAAE;UACxCwB,SAAS,EAAE,mBAAmBhC,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAxI,QAAA,gBAEhExC,OAAA;YAAGgN,SAAS,EAAC,6BAA6B;YAAC3F,KAAK,EAAE;cAAEzF,KAAK,EAAEoJ,SAAS,KAAK,CAAC,GAAGlE,MAAM,CAACyH,aAAa,GAAG;YAAU;UAAE;YAAA7L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrH7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,SAAS;YAACkK,SAAS,EAAC,kBAAkB;YAAC5L,EAAE,EAAE;cAAEQ,KAAK,EAAEoJ,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG;YAAiB,CAAE;YAAAxI,QAAA,EAAC;UAE/H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN7C,OAAA,CAACjC,GAAG;UACFyG,OAAO,EAAEA,CAAA,KAAMgH,qBAAqB,CAAC,CAAC,CAAE;UACxCwB,SAAS,EAAE,mBAAmBhC,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAxI,QAAA,gBAEhExC,OAAA;YAAGgN,SAAS,EAAC,oCAAoC;YAAC3F,KAAK,EAAE;cAAEzF,KAAK,EAAEoJ,SAAS,KAAK,CAAC,GAAGlE,MAAM,CAACyH,aAAa,GAAG;YAAU;UAAE;YAAA7L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5H7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,SAAS;YAACkK,SAAS,EAAC,kBAAkB;YAAC5L,EAAE,EAAE;cAAEQ,KAAK,EAAEoJ,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG;YAAiB,CAAE;YAAAxI,QAAA,EAAC;UAE/H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN7C,OAAA,CAACjC,GAAG;UACFyG,OAAO,EAAEA,CAAA,KAAMgH,qBAAqB,CAAC,CAAC,CAAE;UACxCwB,SAAS,EAAE,mBAAmBhC,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAxI,QAAA,gBAEhExC,OAAA;YAAGgN,SAAS,EAAC,+BAA+B;YAAC3F,KAAK,EAAE;cAAEzF,KAAK,EAAEoJ,SAAS,KAAK,CAAC,GAAGlE,MAAM,CAACyH,aAAa,GAAG;YAAU;UAAE;YAAA7L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvH7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,SAAS;YAACkK,SAAS,EAAC,kBAAkB;YAAC5L,EAAE,EAAE;cAAEQ,KAAK,EAAEoJ,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG;YAAiB,CAAE;YAAAxI,QAAA,EAAC;UAE/H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN7C,OAAA,CAACjC,GAAG;UACFyG,OAAO,EAAEA,CAAA,KAAMgH,qBAAqB,CAAC,CAAC,CAAE;UACxCwB,SAAS,EAAE,mBAAmBhC,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAxI,QAAA,gBAEhExC,OAAA;YAAGgN,SAAS,EAAC,iCAAiC;YAAC3F,KAAK,EAAE;cAAEzF,KAAK,EAAEoJ,SAAS,KAAK,CAAC,GAAGlE,MAAM,CAACyH,aAAa,GAAG;YAAU;UAAE;YAAA7L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzH7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,SAAS;YAACkK,SAAS,EAAC,kBAAkB;YAAC5L,EAAE,EAAE;cAAEQ,KAAK,EAAEoJ,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG;YAAiB,CAAE;YAAAxI,QAAA,EAAC;UAE/H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC2L,GAAA,GApaQ7D,gBAAgB;AAsazB,SAAS8D,iBAAiBA,CAAC;EAAE3H,MAAM;EAAElC,QAAQ;EAAEgG,WAAW;EAAExH,cAAc;EAAEF,gBAAgB;EAAEC,mBAAmB;EAAE0H,aAAa;EAAEC,gBAAgB;EAAEC,OAAO;EAAEM,WAAW;EAAE/B,UAAU;EAAEgC,cAAc;EAAEC,aAAa;EAAEJ,WAAW;EAAED,aAAa;EAAEE,kBAAkB;EAAE5B,WAAW;EAAEiC,UAAU;EAAEC,QAAQ;EAAEC,IAAI;EAAElC,eAAe;EAAEmC,YAAY;EAAElC,MAAM;EAAEmC;AAAU,CAAC,EAAE;EAChW,MAAMzC,YAAY,GAAGA,CAAC0C,IAAI,EAAEC,SAAS,GAAG,GAAG,KAAK;IAC9C,IAAID,IAAI,CAACxI,MAAM,IAAIyI,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EACD,MAAM5C,UAAU,GAAIrE,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACkH,kBAAkB,CAAC,OAAO,EAAE;MAAEC,GAAG,EAAE,SAAS;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC;EAC7F,CAAC;EACD,MAAMC,YAAY,GAAGzH,QAAQ,CAAC0H,MAAM,CAAChE,IAAI,IACvCpF,gBAAgB,KAAK,OAAO,IAC5BoF,IAAI,CAACtC,QAAQ,KAAK9C,gBAAgB,IAClCoF,IAAI,CAACvC,aAAa,KAAK7C,gBACzB,CAAC,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACb,oBACErF,OAAA,CAACjC,GAAG;IAACqD,EAAE,EAAE;MAAEmL,SAAS,EAAE,OAAO;MAAE5K,OAAO,EAAE,oBAAoB;MAAEqC,KAAK,EAAE,OAAO;MAAE0B,QAAQ,EAAE;IAAS,CAAE;IAAAlD,QAAA,gBACjGxC,OAAA,CAACpC,MAAM;MAACyD,QAAQ,EAAC,OAAO;MAACO,KAAK,EAAC,SAAS;MAAC4K,SAAS,EAAE,CAAE;MAACpL,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAE8K,YAAY,EAAE,CAAC;QAAE1I,WAAW,EAAE,UAAU;QAAErC,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eACrIxC,OAAA,CAACnC,OAAO;QAACuD,EAAE,EAAE;UAAEmL,SAAS,EAAE,EAAE;UAAE1K,EAAE,EAAE;QAAE,CAAE;QAAAW,QAAA,gBACpCxC,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEwK,QAAQ,EAAE;UAAE,CAAE;UAAAlK,QAAA,gBAC9DxC,OAAA,CAAC1B,MAAM;YAAC8H,GAAG,EAAEU,MAAM,CAAC6F,IAAK;YAACrG,GAAG,EAAC,MAAM;YAAClF,EAAE,EAAE;cAAE4C,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAACsC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjI7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEa,QAAQ,EAAE;YAAG,CAAE;YAAAD,QAAA,EAAEsE,MAAM,CAAChB;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eACN7C,OAAA,CAACb,UAAU;UAACyN,IAAI,EAAC,KAAK;UAAChL,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAEA,CAAA,KAAMkH,QAAQ,CAAC,QAAQ,CAAE;UAACtK,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAC4B,KAAK,EAAC,OAAO;UAAAtD,QAAA,eACpGxC,OAAA;YAAGgN,SAAS,EAAC,oBAAoB;YAAC3F,KAAK,EAAE;cAAE5E,QAAQ,EAAE;YAAG;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACb7C,OAAA,CAACb,UAAU;UAACyN,IAAI,EAAC,KAAK;UAAChL,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAE+G,aAAc;UAACnK,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC3ExC,OAAA,CAACT,UAAU;YAACkD,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACb7C,OAAA,CAACb,UAAU;UAACyN,IAAI,EAAC,KAAK;UAAChL,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAE0G,aAAc;UAAC9J,EAAE,EAAE;YAAEsN,EAAE,EAAE;UAAE,CAAE;UAAAlM,QAAA,eAC3ExC,OAAA,CAACxB,QAAQ;YAACiE,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACT7C,OAAA,CAACrC,MAAM;MACLsP,MAAM,EAAC,OAAO;MACdC,IAAI,EAAE/B,WAAY;MAClBhK,OAAO,EAAEiK,kBAAmB;MAC5B+B,UAAU,EAAE;QAAEC,WAAW,EAAE;MAAK,CAAE;MAClChM,EAAE,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eAErBxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UAAE4C,KAAK,EAAE,GAAG;UAAEJ,CAAC,EAAE,CAAC;UAAEvC,QAAQ,EAAE;QAAW,CAAE;QAAAmB,QAAA,gBAClDxC,OAAA,CAACb,UAAU;UACTqF,OAAO,EAAE4G,kBAAmB;UAC5BhK,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEC,GAAG,EAAE,CAAC;YAAEG,KAAK,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAG,CAAE;UAC3D,cAAW,OAAO;UAAAc,QAAA,eAElBxC,OAAA,CAACX,SAAS;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACb7C,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE2B,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,gBACxDxC,OAAA,CAAC1B,MAAM;YAAC8H,GAAG,EAAEU,MAAM,CAAC6F,IAAK;YAACrG,GAAG,EAAC,MAAM;YAAClF,EAAE,EAAE;cAAE4C,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAACsC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjI7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEa,QAAQ,EAAE;YAAG,CAAE;YAAAD,QAAA,EAAEsE,MAAM,CAAChB;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eACN7C,OAAA,CAACzB,OAAO;UAAC6C,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1B7C,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,gBACjBxC,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAClC2B,UAAU,EAAE,GAAG;cACfnB,KAAK,EAAE,gBAAgB;cACvBiC,EAAE,EAAE,GAAG;cACPpB,QAAQ,EAAE,EAAE;cACZgI,aAAa,EAAE,WAAW;cAC1B4C,aAAa,EAAE;YACjB,CAAE;YAAA7K,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAAC9B,KAAK;YAACoH,OAAO,EAAE,GAAI;YAAA9C,QAAA,gBAClBxC,OAAA,CAAChC,MAAM;cACL8E,OAAO,EAAC,MAAM;cACdwK,SAAS;cACTlM,EAAE,EAAE;gBACFgD,cAAc,EAAE,YAAY;gBAC5BqG,aAAa,EAAE,MAAM;gBACrB7I,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACT2L,eAAe,EAAE;gBACnB;cACF,CAAE;cACF/I,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIiF,eAAe,EAAE;kBACnBoD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;gBACjC,CAAC,MAAM;kBACLrB,QAAQ,CAAC,aAAa,CAAC;gBACzB;cACF,CAAE;cAAAlJ,QAAA,gBAEFxC,OAAA;gBAAGgN,SAAS,EAAC,iBAAiB;gBAAC3F,KAAK,EAAE;kBAAEmG,WAAW,EAAE,CAAC;kBAAE/K,QAAQ,EAAE;gBAAG;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oBAE9E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7C,OAAA,CAAChC,MAAM;cACL8E,OAAO,EAAC,MAAM;cACdwK,SAAS;cACTlM,EAAE,EAAE;gBACFgD,cAAc,EAAE,YAAY;gBAC5BqG,aAAa,EAAE,MAAM;gBACrB7I,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACT2L,eAAe,EAAE;gBACnB;cACF,CAAE;cACF/I,OAAO,EAAEA,CAAA,KAAM;gBACb4G,kBAAkB,CAAC,CAAC;gBACpBK,UAAU,CAAC,CAAC;cACd,CAAE;cAAAjJ,QAAA,gBAEFxC,OAAA;gBAAGgN,SAAS,EAAC,eAAe;gBAAC3F,KAAK,EAAE;kBAAEmG,WAAW,EAAE,CAAC;kBAAE/K,QAAQ,EAAE;gBAAG;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE5E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7C,OAAA,CAAChC,MAAM;cACL8E,OAAO,EAAC,MAAM;cACdwK,SAAS;cACTlM,EAAE,EAAE;gBACFgD,cAAc,EAAE,YAAY;gBAC5BqG,aAAa,EAAE,MAAM;gBACrB7I,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE;kBACT2L,eAAe,EAAE;gBACnB;cACF,CAAE;cACF/I,OAAO,EAAEA,CAAA,KAAM;gBACb4G,kBAAkB,CAAC,CAAC;gBACpBM,QAAQ,CAAC,QAAQ,CAAC;cACpB,CAAE;cAAAlJ,QAAA,gBAEFxC,OAAA;gBAAGgN,SAAS,EAAC,oBAAoB;gBAAC3F,KAAK,EAAE;kBAAEmG,WAAW,EAAE,CAAC;kBAAE/K,QAAQ,EAAE;gBAAG;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAEjF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN7C,OAAA,CAACzB,OAAO;UAAC6C,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1B7C,OAAA,CAACjC,GAAG;UAAAyE,QAAA,gBACFxC,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAClC2B,UAAU,EAAE,GAAG;cACfnB,KAAK,EAAE,gBAAgB;cACvBiC,EAAE,EAAE,GAAG;cACPpB,QAAQ,EAAE,EAAE;cACZgI,aAAa,EAAE,WAAW;cAC1B4C,aAAa,EAAE;YACjB,CAAE;YAAA7K,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAAC9B,KAAK;YAACoH,OAAO,EAAE,CAAE;YAAA9C,QAAA,EACfiH,eAAe;YAAA;YACd;YACAzJ,OAAA,CAAAE,SAAA;cAAAsC,QAAA,gBACExC,OAAA,CAACjC,GAAG;gBAACqD,EAAE,EAAE;kBACPa,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,GAAG,EAAE,CAAC;kBACNyB,CAAC,EAAE,CAAC;kBACJjC,OAAO,EAAE,SAAS;kBAClBI,YAAY,EAAE,CAAC;kBACf+B,MAAM,EAAE,WAAW;kBACnBC,WAAW,EAAE;gBACf,CAAE;gBAAAvB,QAAA,gBACAxC,OAAA,CAACF,iBAAiB;kBAACsB,EAAE,EAAE;oBAAEQ,KAAK,EAAE,cAAc;oBAAEa,QAAQ,EAAE;kBAAG;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE7C,OAAA,CAACjC,GAAG;kBAAAyE,QAAA,gBACFxC,OAAA,CAAClC,UAAU;oBAACgF,OAAO,EAAC,WAAW;oBAACC,UAAU,EAAE,GAAI;oBAAAP,QAAA,EAC7C,CAAAmJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAElI,IAAI,KAAI;kBAAM;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACb7C,OAAA,CAAClC,UAAU;oBAACgF,OAAO,EAAC,SAAS;oBAAClB,KAAK,EAAC,gBAAgB;oBAAAY,QAAA,EACjD,CAAAmJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,KAAI;kBAAkB;oBAAA/K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7C,OAAA,CAAChC,MAAM;gBACL8E,OAAO,EAAC,UAAU;gBAClBlB,KAAK,EAAC,OAAO;gBACb0L,SAAS;gBACTI,SAAS,eAAE1N,OAAA,CAACH,UAAU;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BzB,EAAE,EAAE;kBACFqJ,aAAa,EAAE,MAAM;kBACrB1G,WAAW,EAAE,YAAY;kBACzBnC,KAAK,EAAE,YAAY;kBACnB,SAAS,EAAE;oBACTmC,WAAW,EAAE,YAAY;oBACzBwJ,eAAe,EAAE,YAAY;oBAC7B3L,KAAK,EAAE;kBACT;gBACF,CAAE;gBACF4C,OAAO,EAAEoH,YAAa;gBAAApJ,QAAA,EACvB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CAAC;YAAA;YAEH;YACA7C,OAAA,CAAAE,SAAA;cAAAsC,QAAA,gBACExC,OAAA,CAAChC,MAAM;gBACL8E,OAAO,EAAC,UAAU;gBAClBlB,KAAK,EAAC,SAAS;gBACf0L,SAAS;gBACTI,SAAS,eAAE1N,OAAA,CAACL,SAAS;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBzB,EAAE,EAAE;kBACFqJ,aAAa,EAAE,MAAM;kBACrB1G,WAAW,EAAE,cAAc;kBAC3BnC,KAAK,EAAE,cAAc;kBACrB,SAAS,EAAE;oBACTmC,WAAW,EAAE,cAAc;oBAC3BwJ,eAAe,EAAE,cAAc;oBAC/B3L,KAAK,EAAE;kBACT;gBACF,CAAE;gBACF4C,OAAO,EAAEA,CAAA,KAAMkH,QAAQ,CAAC,aAAa,CAAE;gBAAAlJ,QAAA,EACxC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7C,OAAA,CAAChC,MAAM;gBACL8E,OAAO,EAAC,WAAW;gBACnBlB,KAAK,EAAC,SAAS;gBACf0L,SAAS;gBACTI,SAAS,eAAE1N,OAAA,CAACJ,aAAa;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7BzB,EAAE,EAAE;kBACFqJ,aAAa,EAAE,MAAM;kBACrB8C,eAAe,EAAE,cAAc;kBAC/B,SAAS,EAAE;oBACTA,eAAe,EAAE;kBACnB;gBACF,CAAE;gBACF/I,OAAO,EAAEA,CAAA,KAAMkH,QAAQ,CAAC,gBAAgB,CAAE;gBAAAlJ,QAAA,EAC3C;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACT7C,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAE8K,YAAY,EAAE,CAAC;QAAE1I,WAAW,EAAE,UAAU;QAAEjC,EAAE,EAAE,CAAC;QAAET,QAAQ,EAAE,OAAO;QAAEC,GAAG,EAAE,EAAE;QAAE8G,IAAI,EAAE,CAAC;QAAE3G,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eACzIxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UAAEkB,QAAQ,EAAE,QAAQ;UAAE0H,EAAE,EAAE,MAAM;UAAEnI,EAAE,EAAE;QAAE,CAAE;QAAAW,QAAA,eACjDxC,OAAA,CAAC9B,KAAK;UAACyP,SAAS,EAAC,KAAK;UAACrI,OAAO,EAAE,CAAE;UAAClE,EAAE,EAAE;YACrCwM,SAAS,EAAE,MAAM;YACjBxJ,cAAc,EAAE,QAAQ;YACxB,sBAAsB,EAAE;cAAEnC,OAAO,EAAE;YAAO,CAAC;YAC3C4L,cAAc,EAAE,MAAM;YACtBC,eAAe,EAAE;UACnB,CAAE;UAAAtL,QAAA,EACCzB,UAAU,CAACwC,GAAG,CAAEC,GAAG,iBAClBxD,OAAA,CAAC/B,IAAI;YAEHsG,KAAK,EAAEf,GAAI;YACX5B,KAAK,EAAEsB,gBAAgB,KAAKM,GAAG,GAAG,SAAS,GAAG,SAAU;YACxDgB,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAACK,GAAG,CAAE;YACxCpC,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAE0B,MAAM,EAAE,SAAS;cAAEyB,UAAU,EAAE,CAAC;cAAE6H,UAAU,EAAE;YAAS;UAAE,GAJ3EvK,GAAG;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKT,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN7C,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,GAAG,EAAE,GAAG;QAAE8G,IAAI,EAAE,CAAC;QAAE3G,KAAK,EAAE,CAAC;QAAEoH,MAAM,EAAE,CAAC;QAAEhH,EAAE,EAAE,CAAC;QAAES,QAAQ,EAAE,QAAQ;QAAE0H,EAAE,EAAE,MAAM;QAAEhG,KAAK,EAAE,MAAM;QAAE/B,OAAO,EAAE,MAAM;QAAE8H,aAAa,EAAE,KAAK;QAAE5H,GAAG,EAAE,CAAC;QAAEoK,SAAS,EAAE,CAAC;QAAE7K,MAAM,EAAE,CAAC;QAAE4I,EAAE,EAAE,CAAC;QAAED,EAAE,EAAE;MAAE,CAAE;MAAA7H,QAAA,gBACpNxC,OAAA,CAACjC,GAAG;QACFoI,SAAS,EAAC,MAAM;QAChB/E,EAAE,EAAE;UACFoE,IAAI,EAAE,CAAC;UACPvB,MAAM,EAAE,qBAAqB;UAAE;UAC/B+J,SAAS,EAAE,MAAM;UACjBzB,SAAS,EAAE,CAAC;UACZjC,EAAE,EAAE,CAAC;UACLD,EAAE,EAAE,CAAC;UAAE;UACP,sBAAsB,EAAE;YAAEpI,OAAO,EAAE;UAAO,CAAC;UAC3C4L,cAAc,EAAE,MAAM;UACtBC,eAAe,EAAE;QACnB,CAAE;QAAAtL,QAAA,gBAGFxC,OAAA,CAAC6G,kBAAkB;UAACjC,QAAQ,EAAEA,QAAS;UAACkC,MAAM,EAAEA;QAAO;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1D7C,OAAA,CAACiD,WAAW;UAACC,gBAAgB,EAAEA,gBAAiB;UAACC,mBAAmB,EAAEA,mBAAoB;UAACC,cAAc,EAAEA;QAAe;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG7H7C,OAAA,CAAC2E,WAAW;UAACC,QAAQ,EAAEA;QAAS;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC7C,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YACPa,OAAO,EAAE,MAAM;YACf8H,aAAa,EAAE,KAAK;YACpB5H,GAAG,EAAE,CAAC;YACN6B,KAAK,EAAE,MAAM;YACb1B,QAAQ,EAAE,MAAM;YAChBsL,SAAS,EAAE,MAAM;YACjB,sBAAsB,EAAE;cAAE3L,OAAO,EAAE;YAAO,CAAC;YAC3C4L,cAAc,EAAE,MAAM;YACtBC,eAAe,EAAE,MAAM;YACvBjM,EAAE,EAAE,CAAC;YACLqI,EAAE,EAAE,CAAC;YACLG,EAAE,EAAE,CAAC,CAAE;UACT,CAAE;UAAA7H,QAAA,EACCuI,OAAO,GACNmD,KAAK,CAACC,IAAI,CAAC;YAAE7K,MAAM,EAAE;UAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACuF,CAAC,EAAEsF,GAAG,kBACnCpO,OAAA,CAAC7B,IAAI;YAAWiD,EAAE,EAAE;cAAEa,OAAO,EAAE,MAAM;cAAE8H,aAAa,EAAE,QAAQ;cAAE9F,MAAM,EAAE,MAAM;cAAE1B,QAAQ,EAAE,GAAG;cAAE2D,UAAU,EAAE;YAAE,CAAE;YAAA1D,QAAA,gBAC7GxC,OAAA,CAACjC,GAAG;cAACqD,EAAE,EAAE;gBAAEC,QAAQ,EAAE,UAAU;gBAAE2C,KAAK,EAAE,MAAM;gBAAEiG,UAAU,EAAE,MAAM;gBAAEtI,OAAO,EAAE;cAAW;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7F7C,OAAA,CAAC5B,WAAW;cAACgD,EAAE,EAAE;gBAAEoE,IAAI,EAAE,CAAC;gBAAEvD,OAAO,EAAE,MAAM;gBAAE8H,aAAa,EAAE,QAAQ;gBAAEM,EAAE,EAAE;cAAE,CAAE;cAAA7H,QAAA,gBAC5ExC,OAAA,CAACjC,GAAG;gBAACqD,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEkC,EAAE,EAAE,CAAC;kBAAE9B,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxE7C,OAAA,CAACjC,GAAG;gBAACqD,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEkC,EAAE,EAAE,CAAC;kBAAE9B,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxE7C,OAAA,CAACjC,GAAG;gBAACqD,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEI,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA,GANLuL,GAAG;YAAA1L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOR,CACP,CAAC,GACAwJ,YAAY,CAAC/I,MAAM,KAAK,CAAC,gBAC3BtD,OAAA,CAACjC,GAAG;YAACqD,EAAE,EAAE;cAAE+G,SAAS,EAAE,QAAQ;cAAErG,EAAE,EAAE,CAAC;cAAEF,KAAK,EAAE,UAAU;cAAEoC,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,gBACxExC,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,IAAI;cAAAN,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtD7C,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,GAENwJ,YAAY,CAAC9I,GAAG,CAAC,CAAC+E,IAAI,EAAE8F,GAAG,kBACzBpO,OAAA,CAACjC,GAAG;YAAsBqD,EAAE,EAAE;cAAEmB,QAAQ,EAAE,GAAG;cAAE2D,UAAU,EAAE;YAAE,CAAE;YAAA1D,QAAA,eAC7CxC,OAAA,CAACgJ,QAAQ;cACzBV,IAAI,EAAEA,IAAK;cACXW,QAAQ,EAAE,CAAC,CAAC4B,aAAa,CAACvC,IAAI,CAAC3B,EAAE,CAAE;cACnCuC,QAAQ,EAAEA,CAAA,KAAM4B,gBAAgB,CAACxC,IAAI,CAAC3B,EAAE,CAAE;cAC1CwC,UAAU,EAAEA,UAAW;cACvBC,YAAY,EAAEA,YAAa;cAC3BtG,OAAO,EAAC,SAAS;cACjBuG,OAAO,EAAEgC,WAAY;cACrB/B,UAAU,EAAEA,UAAW;cACvBC,YAAY,EAAE+B,cAAc,CAAC+C,GAAG,CAAC/F,IAAI,CAAC3B,EAAE,CAAE;cAC1C6C,WAAW,EAAEA,WAAY;cACzBC,eAAe,EAAEA,eAAgB;cACjCC,MAAM,EAAEA,MAAO;cACfC,OAAO,EAAEkC,SAAS,CAACwC,GAAG,CAAC/F,IAAI,CAAC3B,EAAE;YAAE;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC,GAfQyF,IAAI,CAAC3B,EAAE,IAAIyH,GAAG;YAAA1L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBnB,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA,CAACjC,GAAG;QAACoI,SAAS,EAAC,OAAO;QAAC/E,EAAE,EAAE;UAAE4C,KAAK,EAAE,GAAG;UAAEkG,EAAE,EAAE,CAAC;UAAEjI,OAAO,EAAE,OAAO;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAArB,QAAA,eACxExC,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEI,YAAY,EAAE,CAAC;YAAEC,SAAS,EAAE,CAAC;YAAE4B,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEqG,EAAE,EAAE;UAAE,CAAE;UAAA1H,QAAA,gBAC9ExC,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3G7C,OAAA,CAACzB,OAAO;YAAC6C,EAAE,EAAE;cAAEyC,EAAE,EAAE;YAAE;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1B7C,OAAA,CAAC9B,KAAK;YAACoH,OAAO,EAAE,CAAE;YAAA9C,QAAA,EACfoI,WAAW,CAACtH,MAAM,GAAG,CAAC,GACrBsH,WAAW,CAACrH,GAAG,CAAC,CAACoL,IAAI,EAAEP,GAAG,kBACxBpO,OAAA,CAACjC,GAAG;cAAsBqD,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAK,QAAA,gBAC9ExC,OAAA,CAAC1B,MAAM;gBAAC8H,GAAG,EAAEjG,WAAW,CAACwO,IAAI,CAACtI,KAAK,CAAE;gBAACC,GAAG,EAAEqI,IAAI,CAAC7I,KAAM;gBAAC1E,EAAE,EAAE;kBAAE4C,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAACsC,OAAO,EAAGC,CAAC,IAAK;kBAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,2CAA2C;gBAAE;cAAE;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChL7C,OAAA,CAACjC,GAAG;gBAAAyE,QAAA,gBACFxC,OAAA,CAAClC,UAAU;kBAACgF,OAAO,EAAC,WAAW;kBAAC1B,EAAE,EAAE;oBAAE2B,UAAU,EAAE,GAAG;oBAAEnB,KAAK,EAAE,UAAU;oBAAE6D,UAAU,EAAE;kBAAI,CAAE;kBAAAjD,QAAA,EACzFmM,IAAI,CAAC7I,KAAK,CAACxC,MAAM,GAAG,EAAE,GAAGqL,IAAI,CAAC7I,KAAK,CAACkG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG2C,IAAI,CAAC7I;gBAAK;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACb7C,OAAA,CAAClC,UAAU;kBAACgF,OAAO,EAAC,SAAS;kBAAC1B,EAAE,EAAE;oBAAEQ,KAAK,EAAE;kBAAW,CAAE;kBAAAY,QAAA,GACrD2G,UAAU,CAACwF,IAAI,CAAC1I,UAAU,IAAI0I,IAAI,CAAC5J,IAAI,CAAC,EAAC,UAAG,EAAC4J,IAAI,CAACC,KAAK,IAAI,CAAC,EAAC,QAChE;gBAAA;kBAAAlM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA,GATE8L,IAAI,CAAChI,EAAE,IAAIyH,GAAG;cAAA1L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUnB,CACN,CAAC,gBAEF7C,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,OAAO;cAAC1B,EAAE,EAAE;gBAAEQ,KAAK,EAAE,UAAU;gBAAEuG,SAAS,EAAE;cAAS,CAAE;cAAA3F,QAAA,EAAC;YAE5E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7C,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UAAE8I,EAAE,EAAE,CAAC;UAAEpI,EAAE,EAAE,CAAC;UAAEwM,SAAS,EAAE,mBAAmB;UAAEnG,SAAS,EAAE,QAAQ;UAAEtG,EAAE,EAAE;QAAE,CAAE;QAAAW,QAAA,eACpFxC,OAAA,CAACvB,MAAM;UAACqH,KAAK,EAAEgB,MAAM,CAAChB;QAAM;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAgM,GAAA,GAhXSJ,iBAAiB;AAiX1B,MAAMK,iBAAiB,GAAIhI,MAAM,IAAK;EACpC,OAAOlI,WAAW,CAAC;IACjBmQ,OAAO,EAAE;MACPC,OAAO,EAAE;QACPC,IAAI,EAAEnI,MAAM,CAACyH,aAAa,IAAI;MAChC,CAAC;MACDW,SAAS,EAAE;QACTD,IAAI,EAAEnI,MAAM,CAACqI,eAAe,IAAI;MAClC,CAAC;MACD3G,UAAU,EAAE;QACV4G,OAAO,EAAEtI,MAAM,CAACuI,gBAAgB,IAAI;MACtC,CAAC;MACDvD,IAAI,EAAE;QACJkD,OAAO,EAAElI,MAAM,CAACwI,UAAU,IAAI;MAChC;IACF;EACF,CAAC,CAAC;AACJ,CAAC;AAED,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,GAAA;EACpC,MAAM9D,QAAQ,GAAGjO,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkO,IAAI;IAAE8D,MAAM;IAAEhG;EAAgB,CAAC,GAAG/L,OAAO,CAAC,CAAC;EACnD,MAAM,CAACwF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3F,QAAQ,CAAC,OAAO,CAAC;EACjE,MAAM,CAAC2N,WAAW,EAAEuE,cAAc,CAAC,GAAGlS,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwN,SAAS,EAAEC,YAAY,CAAC,GAAGzN,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACsJ,MAAM,EAAE6I,SAAS,CAAC,GAAGnS,QAAQ,CAAC;IACnCmP,IAAI,EAAE,EAAE;IACR7G,KAAK,EAAE,EAAE;IACTyI,aAAa,EAAE,SAAS;IACxBY,eAAe,EAAE,SAAS;IAC1BS,YAAY,EAAE,SAAS;IACvBP,gBAAgB,EAAE,SAAS;IAC3BC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAAC1K,QAAQ,EAAEiL,WAAW,CAAC,GAAGrS,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoN,WAAW,EAAEkF,cAAc,CAAC,GAAGtS,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4F,cAAc,EAAE2M,iBAAiB,CAAC,GAAGvS,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuN,OAAO,EAAEiF,UAAU,CAAC,GAAGxS,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqN,aAAa,EAAEoF,gBAAgB,CAAC,GAAGzS,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD;EACA,MAAM,CAAC0S,YAAY,EAAEC,eAAe,CAAC,GAAG3S,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4S,WAAW,EAAEC,cAAc,CAAC,GAAG7S,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8N,cAAc,EAAEgF,iBAAiB,CAAC,GAAG9S,QAAQ,CAAC,IAAI+S,GAAG,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAC1E,SAAS,EAAE2E,YAAY,CAAC,GAAGhT,QAAQ,CAAC,IAAI+S,GAAG,CAAC,CAAC,CAAC;EACrD,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGlT,QAAQ,CAAC;IAAEmT,OAAO,EAAE,KAAK;IAAE1P,OAAO,EAAE;EAAG,CAAC,CAAC;;EAEnE;EACA1D,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,MAAMwJ,SAAS,GAAG,IAAIC,eAAe,CAAChE,MAAM,CAACC,QAAQ,CAACgE,MAAM,CAAC;IAC7D,MAAMC,QAAQ,GAAGH,SAAS,CAACI,GAAG,CAAC,UAAU,CAAC;IAC1C,MAAMC,QAAQ,GAAGL,SAAS,CAACI,GAAG,CAAC,QAAQ,CAAC;IAExC,IAAID,QAAQ,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACpC5N,mBAAmB,CAAC4N,QAAQ,CAAC;IAC/B;;IAEA;IACA,IAAIE,QAAQ,EAAE;MACZ;MACA5Q,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2Q,QAAQ,CAAC;IAC1C;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,KAAK,GAAGvS,QAAQ,CAAC,CAAC;EACxB,MAAMwS,SAAS,GAAGzS,aAAa,CAACwS,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAG3D,MAAMnG,aAAa,GAAGA,CAAA,KAAMwE,cAAc,CAAC,IAAI,CAAC;EAChD,MAAMtE,kBAAkB,GAAGA,CAAA,KAAMsE,cAAc,CAAC,KAAK,CAAC;EAEtD,MAAMrE,WAAW,GAAG,MAAO/C,IAAI,IAAK;IAClC,IAAI;MACF;MACAgJ,KAAK,CAAC,kCAAkC,GAAGhJ,IAAI,CAAC3B,EAAE,GAAG,QAAQ,EAAE;QAC7D4K,MAAM,EAAE;MACV,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAIpR,OAAO,CAACqR,KAAK,CAAC,6BAA6B,EAAED,GAAG,CAAC,CAAC;;MAElE;MACA,MAAME,QAAQ,GAAGrJ,IAAI,CAACxC,KAAK,CACxB8L,WAAW,CAAC,CAAC,CACbnR,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;MAAA,CAC7BA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;MAAA,CACrBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;MAAA,CACpBoR,IAAI,CAAC,CAAC;MAET,MAAMC,IAAI,GAAG,GAAGjF,MAAM,CAACC,QAAQ,CAACiF,MAAM,YAAYzJ,IAAI,CAAC3B,EAAE,UAAUgL,QAAQ,aAAarJ,IAAI,CAACtC,QAAQ,EAAE;;MAEvG;MACA,MAAMgM,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,IAAI,CAAC;;MAEzC;MACAjC,WAAW,CAACsC,QAAQ,IAClBA,QAAQ,CAAC5O,GAAG,CAACoL,IAAI,IACfA,IAAI,CAAChI,EAAE,KAAK2B,IAAI,CAAC3B,EAAE,GACf;QAAE,GAAGgI,IAAI;QAAEyD,KAAK,EAAE,CAACzD,IAAI,CAACyD,KAAK,IAAI,CAAC,IAAI;MAAE,CAAC,GACzCzD,IACN,CACF,CAAC;;MAED;MACA+B,QAAQ,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAE1P,OAAO,EAAE;MAA6C,CAAC,CAAC;;MAElF;MACAoR,UAAU,CAAC,MAAM;QACf3B,QAAQ,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAE1P,OAAO,EAAE;QAAG,CAAC,CAAC;MAC3C,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOyQ,KAAK,EAAE;MACdrR,OAAO,CAACqR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChB,QAAQ,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAE1P,OAAO,EAAE;MAA6B,CAAC,CAAC;MAClEoR,UAAU,CAAC,MAAM;QACf3B,QAAQ,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAE1P,OAAO,EAAE;QAAG,CAAC,CAAC;MAC3C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMqR,UAAU,GAAG,MAAOhK,IAAI,IAAK;IACjC,MAAMiK,gBAAgB,GAAG1G,SAAS,CAACwC,GAAG,CAAC/F,IAAI,CAAC3B,EAAE,CAAC;IAE/C,IAAI;MACF;MACA,MAAM6L,YAAY,GAAG,IAAIjC,GAAG,CAAC1E,SAAS,CAAC;MACvC,IAAI0G,gBAAgB,EAAE;QACpBC,YAAY,CAACC,MAAM,CAACnK,IAAI,CAAC3B,EAAE,CAAC;MAC9B,CAAC,MAAM;QACL6L,YAAY,CAACE,GAAG,CAACpK,IAAI,CAAC3B,EAAE,CAAC;MAC3B;MACA6J,YAAY,CAACgC,YAAY,CAAC;;MAE1B;MACA,MAAMG,QAAQ,GAAG,MAAMrB,KAAK,CAAC,sFAAsFhJ,IAAI,CAAC3B,EAAE,EAAE,EAAE;QAC5H4K,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,MAAMqB,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACA,MAAMC,cAAc,GAAG,IAAIxC,GAAG,CAAC1E,SAAS,CAAC;QACzC,IAAI+G,IAAI,CAACI,KAAK,EAAE;UACdD,cAAc,CAACL,GAAG,CAACpK,IAAI,CAAC3B,EAAE,CAAC;QAC7B,CAAC,MAAM;UACLoM,cAAc,CAACN,MAAM,CAACnK,IAAI,CAAC3B,EAAE,CAAC;QAChC;QACA6J,YAAY,CAACuC,cAAc,CAAC;;QAE5B;QACAlD,WAAW,CAACoD,QAAQ,IAClBA,QAAQ,CAAC1P,GAAG,CAACoL,IAAI,IACfA,IAAI,CAAChI,EAAE,KAAK2B,IAAI,CAAC3B,EAAE,GACf;UAAE,GAAGgI,IAAI;UAAEuE,KAAK,EAAEN,IAAI,CAACM;QAAM,CAAC,GAC9BvE,IACN,CACF,CAAC;QAEDmB,cAAc,CAACmD,QAAQ,IACrBA,QAAQ,CAAC1P,GAAG,CAACoL,IAAI,IACfA,IAAI,CAAChI,EAAE,KAAK2B,IAAI,CAAC3B,EAAE,GACf;UAAE,GAAGgI,IAAI;UAAEuE,KAAK,EAAEN,IAAI,CAACM;QAAM,CAAC,GAC9BvE,IACN,CACF,CAAC;QAED+B,QAAQ,CAAC;UACPC,OAAO,EAAE,IAAI;UACb1P,OAAO,EAAE2R,IAAI,CAACI,KAAK,GAAG,iBAAiB,GAAG;QAC5C,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAxC,YAAY,CAAC3E,SAAS,CAAC;QACvB6E,QAAQ,CAAC;UAAEC,OAAO,EAAE,IAAI;UAAE1P,OAAO,EAAE;QAAyB,CAAC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOyQ,KAAK,EAAE;MACd;MACAlB,YAAY,CAAC3E,SAAS,CAAC;MACvBxL,OAAO,CAACqR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9ChB,QAAQ,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAE1P,OAAO,EAAE;MAAyB,CAAC,CAAC;IAChE;;IAEA;IACAoR,UAAU,CAAC,MAAM;MACf3B,QAAQ,CAAC;QAAEC,OAAO,EAAE,KAAK;QAAE1P,OAAO,EAAE;MAAG,CAAC,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMkS,cAAc,GAAG,MAAO7K,IAAI,IAAK;IACrC;IACA,IAAI,CAACmB,eAAe,EAAE;MACpBiC,QAAQ,CAAC,aAAa,CAAC;MACvB;IACF;IAEA,MAAM0H,qBAAqB,GAAG9H,cAAc,CAAC+C,GAAG,CAAC/F,IAAI,CAAC3B,EAAE,CAAC;IAEzD,IAAI;MACF;MACA,MAAM0M,MAAM,GAAGD,qBAAqB,GAAG,mBAAmB,GAAG,gBAAgB;MAE7E,MAAMT,QAAQ,GAAG,MAAMrB,KAAK,CAAC,8DAA8D,EAAE;QAC3FC,MAAM,EAAE,MAAM;QACd+B,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBJ,MAAM,EAAEA,MAAM;UACdK,OAAO,EAAEpL,IAAI,CAAC3B;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACgM,QAAQ,CAACgB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBjB,QAAQ,CAACkB,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMC,MAAM,GAAG,MAAMnB,QAAQ,CAACE,IAAI,CAAC,CAAC;MAEpC,IAAIiB,MAAM,CAAChB,OAAO,EAAE;QAClB;QACAxC,iBAAiB,CAACvI,IAAI,IAAI;UACxB,MAAMgM,MAAM,GAAG,IAAIxD,GAAG,CAACxI,IAAI,CAAC;UAC5B,IAAIqL,qBAAqB,EAAE;YACzBW,MAAM,CAACtB,MAAM,CAACnK,IAAI,CAAC3B,EAAE,CAAC;UACxB,CAAC,MAAM;YACLoN,MAAM,CAACrB,GAAG,CAACpK,IAAI,CAAC3B,EAAE,CAAC;UACrB;UACA,OAAOoN,MAAM;QACf,CAAC,CAAC;;QAEF;QACA,MAAM9S,OAAO,GAAGmS,qBAAqB,GACjC,8BAA8B,GAC9B,gCAAgC;QACpC1C,QAAQ,CAAC;UAAEC,OAAO,EAAE,IAAI;UAAE1P;QAAQ,CAAC,CAAC;QAEpCoR,UAAU,CAAC,MAAM;UACf3B,QAAQ,CAAC;YAAEC,OAAO,EAAE,KAAK;YAAE1P,OAAO,EAAE;UAAG,CAAC,CAAC;QAC3C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLyP,QAAQ,CAAC;UAAEC,OAAO,EAAE,IAAI;UAAE1P,OAAO,EAAE,6BAA6B,IAAI6S,MAAM,CAAC7S,OAAO,IAAI,eAAe;QAAE,CAAC,CAAC;QACzGoR,UAAU,CAAC,MAAM;UACf3B,QAAQ,CAAC;YAAEC,OAAO,EAAE,KAAK;YAAE1P,OAAO,EAAE;UAAG,CAAC,CAAC;QAC3C,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOyQ,KAAK,EAAE;MACdrR,OAAO,CAACqR,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDhB,QAAQ,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAE1P,OAAO,EAAE,SAAS,GAAGyQ,KAAK,CAACzQ;MAAQ,CAAC,CAAC;MAC/DoR,UAAU,CAAC,MAAM;QACf3B,QAAQ,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAE1P,OAAO,EAAE;QAAG,CAAC,CAAC;MAC3C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;;EAED;EACA,MAAMwK,UAAU,GAAGA,CAAA,KAAM;IACvB0E,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EACD;EACA,MAAM2D,WAAW,GAAGA,CAAA,KAAM;IACxB7D,eAAe,CAAC,KAAK,CAAC;IACtBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EACD;EACA,MAAM4D,cAAc,GAAG7D,WAAW,CAACyB,IAAI,CAAC,CAAC,GAAGjN,QAAQ,CAAC0H,MAAM,CACzD4H,CAAC,IACCA,CAAC,CAACpO,KAAK,CAAC8L,WAAW,CAAC,CAAC,CAAClR,QAAQ,CAAC0P,WAAW,CAACwB,WAAW,CAAC,CAAC,CAAC,IACzDsC,CAAC,CAAC3J,WAAW,CAACqH,WAAW,CAAC,CAAC,CAAClR,QAAQ,CAAC0P,WAAW,CAACwB,WAAW,CAAC,CAAC,CAClE,CAAC,CAACvM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE;EAClB;EACA9H,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,IAAI,CAAC8I,YAAY,EAAE;IACnB,MAAMiE,SAAS,GAAI1N,CAAC,IAAK;MACvB,IAAIA,CAAC,CAAC2N,GAAG,KAAK,QAAQ,EAAEJ,WAAW,CAAC,CAAC;IACvC,CAAC;IACDnH,MAAM,CAACwH,gBAAgB,CAAC,SAAS,EAAEF,SAAS,CAAC;IAC7C,OAAO,MAAMtH,MAAM,CAACyH,mBAAmB,CAAC,SAAS,EAAEH,SAAS,CAAC;EAC/D,CAAC,EAAE,CAACjE,YAAY,CAAC,CAAC;;EAElB;EACA3S,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB;IACAkK,KAAK,CAAC,kFAAkF,CAAC,CACtFiD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3B,IAAI,CAAC,CAAC,CAAC,CACvB0B,IAAI,CAAC5B,QAAQ,IAAI;MAChB,IAAIA,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACC,IAAI,EAAE;QACrC,MAAMA,IAAI,GAAGD,QAAQ,CAACC,IAAI;;QAE1B;QACA,IAAI6B,QAAQ,GAAG,4BAA4B,GAAGC,IAAI,CAAC;AAC7D;AACA;AACA;AACA,WAAW,CAAC;QAEF,IAAI9B,IAAI,CAAC+B,YAAY,IAAI/B,IAAI,CAAC+B,YAAY,KAAK,cAAc,IAAI/B,IAAI,CAAC+B,YAAY,KAAK,iBAAiB,EAAE;UACxG;UACA,MAAMnU,QAAQ,GAAGoS,IAAI,CAAC+B,YAAY,CAAChU,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;UACnD6T,QAAQ,GAAG,uCAAuCjU,QAAQ,EAAE;UAC5D;QACF;QAEAmP,SAAS,CAAC;UACRhD,IAAI,EAAE8H,QAAQ;UACd3O,KAAK,EAAE8M,IAAI,CAACgC,YAAY,IAAI,mBAAmB;UAC/CrK,WAAW,EAAEqI,IAAI,CAACiC,mBAAmB,IAAI,sCAAsC;UAC/EtG,aAAa,EAAEqE,IAAI,CAACrE,aAAa,IAAI,SAAS;UAC9CY,eAAe,EAAEyD,IAAI,CAACzD,eAAe,IAAI,SAAS;UAClDS,YAAY,EAAEgD,IAAI,CAAChD,YAAY,IAAI,SAAS;UAC5CP,gBAAgB,EAAE,SAAS;UAC3BC,UAAU,EAAE,SAAS;UACrBwF,WAAW,EAAE,gDAAgD;UAC7DC,aAAa,EAAE,qBAAqB;UACpCC,eAAe,EAAE,EAAE;UACnBC,cAAc,EAAE,EAAE;UAClBC,gBAAgB,EAAE,EAAE;UACpBC,cAAc,EAAE,EAAE;UAClBC,aAAa,EAAE,sBAAsB;UACrCC,gBAAgB,EAAEzC,IAAI,CAACiC,mBAAmB,IAAI;QAChD,CAAC,CAAC;;QAEF;QACAvN,QAAQ,CAACxB,KAAK,GAAG8M,IAAI,CAACgC,YAAY,IAAI,mBAAmB;;QAEzD;QACA,MAAMU,eAAe,GAAGhO,QAAQ,CAACiO,aAAa,CAAC,0BAA0B,CAAC;QAC1E,IAAID,eAAe,EAAE;UACnBA,eAAe,CAACE,YAAY,CAAC,SAAS,EAAE5C,IAAI,CAACiC,mBAAmB,IAAI,uBAAuB,CAAC;QAC9F;;QAEA;QACA,MAAMY,YAAY,GAAGnO,QAAQ,CAACiO,aAAa,CAAC,uBAAuB,CAAC;QACpE,IAAIE,YAAY,EAAE;UAChBA,YAAY,CAACD,YAAY,CAAC,SAAS,EAAE,sBAAsB,CAAC;QAC9D;;QAEA;QACA,IAAI5C,IAAI,CAAC+B,YAAY,IAAI/B,IAAI,CAAC+B,YAAY,KAAK,cAAc,IAAI/B,IAAI,CAAC+B,YAAY,KAAK,iBAAiB,EAAE;UACxG,MAAMnU,QAAQ,GAAGoS,IAAI,CAAC+B,YAAY,CAAChU,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;UACnD,MAAM8U,UAAU,GAAG,uCAAuClV,QAAQ,EAAE;;UAEpE;UACA,IAAImV,OAAO,GAAGrO,QAAQ,CAACiO,aAAa,CAAC,kBAAkB,CAAC,IAAIjO,QAAQ,CAACiO,aAAa,CAAC,2BAA2B,CAAC;UAC/G,IAAI,CAACI,OAAO,EAAE;YACZA,OAAO,GAAGrO,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;YACxCoO,OAAO,CAACC,GAAG,GAAG,MAAM;YACpBtO,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACiO,OAAO,CAAC;UACpC;UACAA,OAAO,CAAC5I,IAAI,GAAG2I,UAAU;;UAEzB;UACA,MAAMG,cAAc,GAAGvO,QAAQ,CAACiO,aAAa,CAAC,8BAA8B,CAAC;UAC7E,IAAIM,cAAc,EAAE;YAClBA,cAAc,CAAC9I,IAAI,GAAG2I,UAAU;UAClC;UAEArV,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoV,UAAU,CAAC;QAC3D;MACF,CAAC,MAAM;QACL,MAAM,IAAI9B,KAAK,CAAC,sBAAsB,CAAC;MACzC;IACF,CAAC,CAAC,CACDpC,KAAK,CAACC,GAAG,IAAI;MACZpR,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEmR,GAAG,CAAC;MACpE;MACA9B,SAAS,CAAC;QACRhD,IAAI,EAAE,4BAA4B,GAAG+H,IAAI,CAAC;AACpD;AACA;AACA;AACA,WAAW,CAAC;QACF5O,KAAK,EAAE,mBAAmB;QAC1ByE,WAAW,EAAE,sCAAsC;QACnDgE,aAAa,EAAE,SAAS;QACxBY,eAAe,EAAE,SAAS;QAC1BS,YAAY,EAAE,SAAS;QACvBP,gBAAgB,EAAE,SAAS;QAC3BC,UAAU,EAAE,SAAS;QACrBwF,WAAW,EAAE,gDAAgD;QAC7DC,aAAa,EAAE,qBAAqB;QACpCC,eAAe,EAAE,EAAE;QACnBC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,6BAA6B;QAC5CC,gBAAgB,EAAE;MACpB,CAAC,CAAC;;MAEF;MACA/N,QAAQ,CAACxB,KAAK,GAAG,mBAAmB;IACtC,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgQ,eAAe,GAAGvY,KAAK,CAACwY,WAAW,CAAC,MAAM;IAC9CzE,KAAK,CAAC,qFAAqF,GAAGtM,IAAI,CAACgR,GAAG,CAAC,CAAC,CAAC,CACtGzB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3B,IAAI,CAAC,CAAC,CAAC,CACvB0B,IAAI,CAAC5B,QAAQ,IAAI;MAChB,IAAIA,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACC,IAAI,EAAE;QACrC,MAAMA,IAAI,GAAGD,QAAQ,CAACC,IAAI;;QAE1B;QACA,IAAI6B,QAAQ,GAAG,4BAA4B,GAAGC,IAAI,CAAC;AAC7D;AACA;AACA;AACA,WAAW,CAAC;QAEF,IAAI9B,IAAI,CAAC+B,YAAY,IAAI/B,IAAI,CAAC+B,YAAY,KAAK,cAAc,IAAI/B,IAAI,CAAC+B,YAAY,KAAK,iBAAiB,EAAE;UACxG;UACA,MAAMnU,QAAQ,GAAGoS,IAAI,CAAC+B,YAAY,CAAChU,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;UACnD6T,QAAQ,GAAG,uCAAuCjU,QAAQ,EAAE;UAC5D;;UAEA;UACA,IAAImV,OAAO,GAAGrO,QAAQ,CAACiO,aAAa,CAAC,kBAAkB,CAAC,IAAIjO,QAAQ,CAACiO,aAAa,CAAC,2BAA2B,CAAC;UAC/G,IAAI,CAACI,OAAO,EAAE;YACZA,OAAO,GAAGrO,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;YACxCoO,OAAO,CAACC,GAAG,GAAG,MAAM;YACpBtO,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACiO,OAAO,CAAC;UACpC;UACAA,OAAO,CAAC5I,IAAI,GAAG0H,QAAQ;;UAEvB;UACA,MAAMoB,cAAc,GAAGvO,QAAQ,CAACiO,aAAa,CAAC,8BAA8B,CAAC;UAC7E,IAAIM,cAAc,EAAE;YAClBA,cAAc,CAAC9I,IAAI,GAAG0H,QAAQ;UAChC;;UAEA;QACF;QAEA9E,SAAS,CAAC5H,IAAI,KAAK;UACjB,GAAGA,IAAI;UACP4E,IAAI,EAAE8H,QAAQ;UACd3O,KAAK,EAAE8M,IAAI,CAACgC,YAAY,IAAI,mBAAmB;UAC/CrK,WAAW,EAAEqI,IAAI,CAACiC,mBAAmB,IAAI;QAC3C,CAAC,CAAC,CAAC;QAEHvN,QAAQ,CAACxB,KAAK,GAAG8M,IAAI,CAACgC,YAAY,IAAI,mBAAmB;MAC3D;IACF,CAAC,CAAC,CACDpD,KAAK,CAACC,GAAG,IAAIpR,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmR,GAAG,CAAC,CAAC;EAClE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlU,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,MAAM6O,mBAAmB,GAAIxP,CAAC,IAAK;MACjC,IAAIA,CAAC,CAAC2N,GAAG,KAAK,kBAAkB,EAAE;QAChC/T,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CwV,eAAe,CAAC,CAAC;MACnB;IACF,CAAC;IAEDjJ,MAAM,CAACwH,gBAAgB,CAAC,SAAS,EAAE4B,mBAAmB,CAAC;IACvD,OAAO,MAAMpJ,MAAM,CAACyH,mBAAmB,CAAC,SAAS,EAAE2B,mBAAmB,CAAC;EACzE,CAAC,EAAE,CAACH,eAAe,CAAC,CAAC;;EAErB;EACAvY,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpBkK,KAAK,CAAC,oFAAoF,CAAC,CACxFiD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3B,IAAI,CAAC,CAAC,CAAC,CACvB0B,IAAI,CAAC3B,IAAI,IAAI;MACZ,IAAIA,IAAI,CAACE,OAAO,IAAI5E,KAAK,CAACgI,OAAO,CAACtD,IAAI,CAACA,IAAI,CAAC,EAAE;QAC5C;QACA,MAAMuD,gBAAgB,GAAGvD,IAAI,CAACA,IAAI,CAACrP,GAAG,CAACC,GAAG,KAAK;UAC7CmD,EAAE,EAAEnD,GAAG,CAACmD,EAAE;UACVlD,IAAI,EAAED,GAAG,CAACC,IAAI;UACd2S,IAAI,EAAE5S,GAAG,CAAC4S,IAAI;UACd7L,WAAW,EAAE/G,GAAG,CAAC+G,WAAW;UAC5B3I,KAAK,EAAE4B,GAAG,CAAC5B,KAAK,IAAI,SAAS;UAC7ByU,SAAS,EAAE7S,GAAG,CAAC6S,SAAS;UACxB1S,UAAU,EAAEH,GAAG,CAACG,UAAU,IAAI,CAAC;UAC/BsC,UAAU,EAAEzC,GAAG,CAACyC,UAAU;UAC1BqQ,UAAU,EAAE9S,GAAG,CAAC8S;QAClB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,gBAAgB,GAAGJ,gBAAgB,CAAC7J,MAAM,CAAC9I,GAAG,IAAIA,GAAG,CAAC6S,SAAS,KAAK,KAAK,CAAC;QAChFtG,iBAAiB,CAACwG,gBAAgB,CAAC;MACrC;IACF,CAAC,CAAC,CACD/E,KAAK,CAACC,GAAG,IAAI;MACZ;MACA1B,iBAAiB,CAAC,CAChB;QACEpJ,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,MAAM;QACZ2S,IAAI,EAAE,MAAM;QACZ7L,WAAW,EAAE,mCAAmC;QAChD3I,KAAK,EAAE,SAAS;QAChByU,SAAS,EAAE,IAAI;QACf1S,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,WAAW;QACjB2S,IAAI,EAAE,WAAW;QACjB7L,WAAW,EAAE,sCAAsC;QACnD3I,KAAK,EAAE,SAAS;QAChByU,SAAS,EAAE,IAAI;QACf1S,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,QAAQ;QACd2S,IAAI,EAAE,QAAQ;QACd7L,WAAW,EAAE,2BAA2B;QACxC3I,KAAK,EAAE,SAAS;QAChByU,SAAS,EAAE,IAAI;QACf1S,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,UAAU;QAChB2S,IAAI,EAAE,UAAU;QAChB7L,WAAW,EAAE,+BAA+B;QAC5C3I,KAAK,EAAE,SAAS;QAChByU,SAAS,EAAE,IAAI;QACf1S,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,SAAS;QACf2S,IAAI,EAAE,SAAS;QACf7L,WAAW,EAAE,8BAA8B;QAC3C3I,KAAK,EAAE,SAAS;QAChByU,SAAS,EAAE,IAAI;QACf1S,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,SAAS;QACf2S,IAAI,EAAE,SAAS;QACf7L,WAAW,EAAE,iCAAiC;QAC9C3I,KAAK,EAAE,SAAS;QAChByU,SAAS,EAAE,IAAI;QACf1S,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,WAAW;QACjB2S,IAAI,EAAE,WAAW;QACjB7L,WAAW,EAAE,4BAA4B;QACzC3I,KAAK,EAAE,SAAS;QAChByU,SAAS,EAAE,IAAI;QACf1S,UAAU,EAAE;MACd,CAAC,CACF,CAAC;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApG,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpBkK,KAAK,CAAC,8EAA8E,CAAC,CAClFiD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC3B,IAAI,CAAC,CAAC,CAAC,CACvB0B,IAAI,CAAC3B,IAAI,IAAI;MACZ,IAAIA,IAAI,CAACE,OAAO,IAAI5E,KAAK,CAACgI,OAAO,CAACtD,IAAI,CAACA,IAAI,CAAC,EAAE;QAC5C;QACA,MAAM4D,UAAU,GAAG5D,IAAI,CAACA,IAAI,CAACrP,GAAG,CAACoL,IAAI;UAAA,IAAA8H,aAAA;UAAA,OAAK;YACxC9P,EAAE,EAAEgI,IAAI,CAAChI,EAAE;YACXb,KAAK,EAAE6I,IAAI,CAAC7I,KAAK;YACjBsQ,IAAI,EAAEzH,IAAI,CAACyH,IAAI;YACf7L,WAAW,EAAEoE,IAAI,CAACpE,WAAW,IAAIoE,IAAI,CAAC+H,OAAO,IAAI,EAAAD,aAAA,GAAA9H,IAAI,CAACgI,OAAO,cAAAF,aAAA,uBAAZA,aAAA,CAAczK,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK;YACxF2K,OAAO,EAAEhI,IAAI,CAACgI,OAAO;YACrBtQ,KAAK,EAAEsI,IAAI,CAACtI,KAAK,IAAI,2BAA2B;YAChDuQ,SAAS,EAAEjI,IAAI,CAACiI,SAAS,IAAIjI,IAAI,CAAC7I,KAAK;YACvCE,QAAQ,EAAE2I,IAAI,CAAC5I,aAAa,IAAI,MAAM;YACtCA,aAAa,EAAE4I,IAAI,CAAC5I,aAAa,IAAI,MAAM;YAC3C8Q,WAAW,EAAElI,IAAI,CAACkI,WAAW,IAAI,CAAC;YAClCC,cAAc,EAAEnI,IAAI,CAACmI,cAAc,IAAI,SAAS;YAChDjD,MAAM,EAAElF,IAAI,CAACkF,MAAM,IAAI,WAAW;YAClCkD,QAAQ,EAAEpI,IAAI,CAACoI,QAAQ,IAAI,KAAK;YAChCC,IAAI,EAAErI,IAAI,CAACqI,IAAI,GAAGrI,IAAI,CAACqI,IAAI,CAACrW,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3CiO,KAAK,EAAED,IAAI,CAACC,KAAK,IAAI,CAAC;YACtBwD,KAAK,EAAEzD,IAAI,CAACyD,KAAK,IAAI,CAAC;YACtBc,KAAK,EAAEvE,IAAI,CAACuE,KAAK,IAAI,CAAC;YACtB+D,cAAc,EAAEtI,IAAI,CAACsI,cAAc,IAAI,CAAC;YACxCC,YAAY,EAAEvI,IAAI,CAACuI,YAAY,IAAI,CAAC;YACpCnS,IAAI,EAAE4J,IAAI,CAAC5J,IAAI,IAAI4J,IAAI,CAAC1I,UAAU;YAClCkR,YAAY,EAAExI,IAAI,CAACwI,YAAY;YAC/BlR,UAAU,EAAE0I,IAAI,CAAC1I,UAAU;YAC3BqQ,UAAU,EAAE3H,IAAI,CAAC2H,UAAU;YAC3Bc,MAAM,EAAEzI,IAAI,CAAC0I,WAAW,IAAI1I,IAAI,CAAC2I,SAAS,IAAI,OAAO;YACrDC,OAAO,EAAE5I,IAAI,CAAC4I;UAChB,CAAC;QAAA,CAAC,CAAC;;QAEH;QACA,MAAMC,cAAc,GAAGhB,UAAU,CAAClK,MAAM,CAACqC,IAAI,IAAIA,IAAI,CAACkF,MAAM,KAAK,WAAW,CAAC;QAC7EhE,WAAW,CAAC2H,cAAc,CAAC;;QAE3B;QACA,MAAMC,OAAO,GAAGD,cAAc,CAC3BE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACd,MAAMC,MAAM,GAAIF,CAAC,CAAC/I,KAAK,GAAG,CAAC,GAAK+I,CAAC,CAACvF,KAAK,GAAG,CAAE,GAAIuF,CAAC,CAACzE,KAAK,GAAG,CAAE,GAAIyE,CAAC,CAACV,cAAc,GAAG,CAAE;UACrF,MAAMa,MAAM,GAAIF,CAAC,CAAChJ,KAAK,GAAG,CAAC,GAAKgJ,CAAC,CAACxF,KAAK,GAAG,CAAE,GAAIwF,CAAC,CAAC1E,KAAK,GAAG,CAAE,GAAI0E,CAAC,CAACX,cAAc,GAAG,CAAE;UACrF,OAAOa,MAAM,GAAGD,MAAM;QACxB,CAAC,CAAC,CACDxS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACdyK,cAAc,CAAC2H,OAAO,CAAC;MACzB,CAAC,MAAM;QACL5H,WAAW,CAAC,EAAE,CAAC;QACfC,cAAc,CAAC,EAAE,CAAC;MACpB;MACAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDwB,KAAK,CAAC,MAAM;MACX;MACA,MAAMuG,SAAS,GAAG,CAChB;QACEpR,EAAE,EAAE,CAAC;QACLb,KAAK,EAAE,qCAAqC;QAC5CsQ,IAAI,EAAE,qCAAqC;QAC3C7L,WAAW,EAAE,8GAA8G;QAC3HoM,OAAO,EAAE,+JAA+J;QACxKtQ,KAAK,EAAE,2BAA2B;QAClCuQ,SAAS,EAAE,mBAAmB;QAC9B5Q,QAAQ,EAAE,WAAW;QACrBD,aAAa,EAAE,WAAW;QAC1B8Q,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,SAAS;QACzBjD,MAAM,EAAE,WAAW;QACnBkD,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;QAChDpI,KAAK,EAAE,GAAG;QACVwD,KAAK,EAAE,EAAE;QACTc,KAAK,EAAE,EAAE;QACT+D,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfnS,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACgT,WAAW,CAAC,CAAC;QAC9Bb,YAAY,EAAE,IAAInS,IAAI,CAAC,CAAC,CAACgT,WAAW,CAAC,CAAC;QACtC/R,UAAU,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAACgT,WAAW,CAAC,CAAC;QACpC1B,UAAU,EAAE,IAAItR,IAAI,CAAC,CAAC,CAACgT,WAAW,CAAC,CAAC;QACpCZ,MAAM,EAAE,OAAO;QACfG,OAAO,EAAE;MACX,CAAC,EACD;QACE5Q,EAAE,EAAE,CAAC;QACLb,KAAK,EAAE,6CAA6C;QACpDsQ,IAAI,EAAE,6CAA6C;QACnD7L,WAAW,EAAE,qGAAqG;QAClHoM,OAAO,EAAE,gKAAgK;QACzKtQ,KAAK,EAAE,yBAAyB;QAChCuQ,SAAS,EAAE,cAAc;QACzB5Q,QAAQ,EAAE,WAAW;QACrBD,aAAa,EAAE,WAAW;QAC1B8Q,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,SAAS;QACzBjD,MAAM,EAAE,WAAW;QACnBkD,QAAQ,EAAE,KAAK;QACfC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC;QAC/CpI,KAAK,EAAE,EAAE;QACTwD,KAAK,EAAE,EAAE;QACTc,KAAK,EAAE,EAAE;QACT+D,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfnS,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACgR,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACgC,WAAW,CAAC,CAAC;QACnDb,YAAY,EAAE,IAAInS,IAAI,CAACA,IAAI,CAACgR,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACgC,WAAW,CAAC,CAAC;QAC3D/R,UAAU,EAAE,IAAIjB,IAAI,CAACA,IAAI,CAACgR,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACgC,WAAW,CAAC,CAAC;QACzD1B,UAAU,EAAE,IAAItR,IAAI,CAACA,IAAI,CAACgR,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACgC,WAAW,CAAC,CAAC;QACzDZ,MAAM,EAAE,OAAO;QACfG,OAAO,EAAE;MACX,CAAC,EACD;QACE5Q,EAAE,EAAE,CAAC;QACLb,KAAK,EAAE,4CAA4C;QACnDsQ,IAAI,EAAE,4CAA4C;QAClD7L,WAAW,EAAE,qFAAqF;QAClGoM,OAAO,EAAE,0IAA0I;QACnJtQ,KAAK,EAAE,+BAA+B;QACtCuQ,SAAS,EAAE,mBAAmB;QAC9B5Q,QAAQ,EAAE,QAAQ;QAClBD,aAAa,EAAE,QAAQ;QACvB8Q,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,SAAS;QACzBjD,MAAM,EAAE,WAAW;QACnBkD,QAAQ,EAAE,KAAK;QACfC,IAAI,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;QACpDpI,KAAK,EAAE,EAAE;QACTwD,KAAK,EAAE,CAAC;QACRc,KAAK,EAAE,EAAE;QACT+D,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfnS,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACgR,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACgC,WAAW,CAAC,CAAC;QACpDb,YAAY,EAAE,IAAInS,IAAI,CAACA,IAAI,CAACgR,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACgC,WAAW,CAAC,CAAC;QAC5D/R,UAAU,EAAE,IAAIjB,IAAI,CAACA,IAAI,CAACgR,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACgC,WAAW,CAAC,CAAC;QAC1D1B,UAAU,EAAE,IAAItR,IAAI,CAACA,IAAI,CAACgR,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACgC,WAAW,CAAC,CAAC;QAC1DZ,MAAM,EAAE,OAAO;QACfG,OAAO,EAAE;MACX,CAAC,CACF;MACD1H,WAAW,CAACkI,SAAS,CAAC;MACtBjI,cAAc,CAACiI,SAAS,CAAC;MACzB/H,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzS,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,IAAI,CAACqC,eAAe,EAAE;MACpB6G,iBAAiB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;IAEA,MAAM0H,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF,MAAMtF,QAAQ,GAAG,MAAMrB,KAAK,CAAC,oFAAoF,CAAC;QAClH,MAAMsB,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAI5E,KAAK,CAACgI,OAAO,CAACtD,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5C;UACA,MAAMsF,aAAa,GAAG,IAAI3H,GAAG,CAACqC,IAAI,CAACA,IAAI,CAACrP,GAAG,CAAC+E,IAAI,IAAIA,IAAI,CAAC3B,EAAE,CAAC,CAAC;UAC7D2J,iBAAiB,CAAC4H,aAAa,CAAC;QAClC;MACF,CAAC,CAAC,OAAOxG,KAAK,EAAE;QACdrR,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEoR,KAAK,CAAC;MACvD;IACF,CAAC;IAEDuG,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACxO,eAAe,CAAC,CAAC;;EAErB;EACAlM,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,MAAM+Q,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF,MAAMxF,QAAQ,GAAG,MAAMrB,KAAK,CAAC,qFAAqF,CAAC;QACnH,MAAMsB,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAI5E,KAAK,CAACgI,OAAO,CAACtD,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5C;UACA,MAAMwF,QAAQ,GAAG,IAAI7H,GAAG,CAACqC,IAAI,CAACA,IAAI,CAACrP,GAAG,CAACoD,EAAE,IAAI0R,QAAQ,CAAC1R,EAAE,CAAC,CAAC,CAAC;UAC3D6J,YAAY,CAAC4H,QAAQ,CAAC;UACtB/X,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEsS,IAAI,CAAC0F,UAAU,EAAE,QAAQ,EAAE1F,IAAI,CAACA,IAAI,CAAC;QACjF;MACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;QACdrR,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoR,KAAK,CAAC;MACnD;IACF,CAAC;IAEDyG,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA5a,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,IAAIxC,QAAQ,CAACtB,MAAM,GAAG,CAAC,IAAIF,cAAc,CAACE,MAAM,GAAG,CAAC,EAAE;MACpD;MACA,MAAMiV,YAAY,GAAGnV,cAAc,CAACoV,IAAI,CAAChV,GAAG,IAAIA,GAAG,CAACG,UAAU,GAAG,CAAC,CAAC;MAEnE,IAAI,CAAC4U,YAAY,EAAE;QACjB;QACA,MAAME,UAAU,GAAG,CAAC,CAAC;QACrB7T,QAAQ,CAAC8T,OAAO,CAACpQ,IAAI,IAAI;UACvB,MAAMqQ,UAAU,GAAGrQ,IAAI,CAACuO,WAAW;UACnC4B,UAAU,CAACE,UAAU,CAAC,GAAG,CAACF,UAAU,CAACE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;QAC5D,CAAC,CAAC;;QAEF;QACA,MAAMC,iBAAiB,GAAGxV,cAAc,CAACG,GAAG,CAACC,GAAG,KAAK;UACnD,GAAGA,GAAG;UACNG,UAAU,EAAE8U,UAAU,CAACjV,GAAG,CAACmD,EAAE,CAAC,IAAI;QACpC,CAAC,CAAC,CAAC;QAEHoJ,iBAAiB,CAAC6I,iBAAiB,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CAAChU,QAAQ,EAAExB,cAAc,CAAC,CAAC;EAE9B,MAAM0H,gBAAgB,GAAI+N,MAAM,IAAK;IACnC5I,gBAAgB,CAAClI,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC8Q,MAAM,GAAG,CAAC9Q,IAAI,CAAC8Q,MAAM;IAAE,CAAC,CAAC,CAAC;EAClE,CAAC;EAED,MAAMhP,eAAe,GAAG,MAAOiP,MAAM,IAAK;IACxC;IACA,IAAI;MACF,MAAMxH,KAAK,CAAC,0FAA0FwH,MAAM,EAAE,EAAE;QAC9GvH,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdrR,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoR,KAAK,CAAC;IAClD;;IAEA;IACAhG,QAAQ,CAAC,cAAcoN,MAAM,EAAE,CAAC;EAClC,CAAC;EAED,MAAMlN,YAAY,GAAGA,CAAA,KAAM;IACzB6D,MAAM,CAAC,CAAC;IACR/D,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMqN,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAtN,UAAU,CAAC,CAAC;IACZiE,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;;EAED;EACA,MAAMlE,qBAAqB,GAAIwN,QAAQ,IAAK;IAC1C,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClB/N,YAAY,CAAC,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAI+N,QAAQ,KAAK,CAAC,EAAE;MACzB/N,YAAY,CAAC,CAAC,CAAC;MACf;MACAA,YAAY,CAAC,CAAC,CAAC;MACf5K,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD;MACAuM,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,sEAAsE;IAC/F,CAAC,MAAM,IAAIiM,QAAQ,KAAK,CAAC,EAAE;MACzB/N,YAAY,CAAC,CAAC,CAAC;MACfQ,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIuN,QAAQ,KAAK,CAAC,EAAE;MACzB;MACA,IAAIvP,eAAe,EAAE;QACnBoD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ,CAAC,CAAC;MACnC,CAAC,MAAM;QACL;QACArB,QAAQ,CAAC,aAAa,CAAC;MACzB;IACF,CAAC,MAAM;MACLT,YAAY,CAAC+N,QAAQ,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,WAAW,GAAGnK,iBAAiB,CAAChI,MAAM,CAAC;EAE7C,IAAIqK,SAAS,EAAE;IACb,oBACEnR,OAAA,CAACnB,aAAa;MAACqS,KAAK,EAAE+H,WAAY;MAAAzW,QAAA,gBAChCxC,OAAA,CAACyO,iBAAiB;QAChB3H,MAAM,EAAEA,MAAO;QACflC,QAAQ,EAAEA,QAAS;QACnBgG,WAAW,EAAEA,WAAY;QACzBxH,cAAc,EAAEA,cAAe;QAC/BF,gBAAgB,EAAEA,gBAAiB;QACnCC,mBAAmB,EAAEA,mBAAoB;QACzC0H,aAAa,EAAEA,aAAc;QAC7BC,gBAAgB,EAAEA,gBAAiB;QACnCC,OAAO,EAAEA,OAAQ;QACjBM,WAAW,EAAEA,WAAY;QACzB/B,UAAU,EAAE6J,cAAe;QAC3B7H,cAAc,EAAEA,cAAe;QAC/BC,aAAa,EAAEE,UAAW;QAC1BN,WAAW,EAAEA,WAAY;QACzBD,aAAa,EAAEA,aAAc;QAC7BE,kBAAkB,EAAEA,kBAAmB;QACvC5B,WAAW,EAAEK,eAAgB;QAC7B4B,UAAU,EAAEA,UAAW;QACvBC,QAAQ,EAAEA,QAAS;QACnBC,IAAI,EAAEA,IAAK;QACXlC,eAAe,EAAEA,eAAgB;QACjCmC,YAAY,EAAEA,YAAa;QAC3BlC,MAAM,EAAE4I,UAAW;QACnBzG,SAAS,EAAEA;MAAU;QAAAnJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAGF7C,OAAA,CAACgB,KAAK;QACJC,OAAO,EAAEwP,KAAK,CAACxP,OAAQ;QACvBC,SAAS,EAAEuP,KAAK,CAACE,OAAQ;QACzBxP,OAAO,EAAEA,CAAA,KAAMuP,QAAQ,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAE1P,OAAO,EAAE;QAAG,CAAC;MAAE;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,EAEDqN,YAAY,iBACXlQ,OAAA,CAAAE,SAAA;QAAAsC,QAAA,gBAEExC,OAAA,CAACjC,GAAG;UACFyG,OAAO,EAAEwP,WAAY;UACrB5S,EAAE,EAAE;YACFC,QAAQ,EAAE,OAAO;YACjBC,GAAG,EAAE,CAAC;YACN8G,IAAI,EAAE,CAAC;YACP3G,KAAK,EAAE,CAAC;YACRoH,MAAM,EAAE,CAAC;YACTnH,MAAM,EAAE,IAAI;YACZC,OAAO,EAAE,iBAAiB;YAC1B0G,SAAS,EAAE,sBAAsB;YACjC,mBAAmB,EAAE;cACnB,IAAI,EAAE;gBAAEE,OAAO,EAAE;cAAE,CAAC;cACpB,MAAM,EAAE;gBAAEA,OAAO,EAAE;cAAE;YACvB;UACF;QAAE;UAAA7F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEF7C,OAAA,CAACjC,GAAG;UAACqD,EAAE,EAAE;YACPC,QAAQ,EAAE,OAAO;YACjBC,GAAG,EAAE,CAAC;YACN8G,IAAI,EAAE,CAAC;YACP3G,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,IAAI;YACZC,OAAO,EAAE,wBAAwB;YACjCuX,cAAc,EAAE,YAAY;YAC5BlX,SAAS,EAAE,4BAA4B;YACvCH,EAAE,EAAE,CAAC;YACLyI,EAAE,EAAE,EAAE;YAAE;YACRD,EAAE,EAAE,CAAC;YACLhC,SAAS,EAAE,yBAAyB;YACpC,sBAAsB,EAAE;cACtB,IAAI,EAAE;gBACJjG,SAAS,EAAE,mBAAmB;gBAC9BmG,OAAO,EAAE;cACX,CAAC;cACD,MAAM,EAAE;gBACNnG,SAAS,EAAE,eAAe;gBAC1BmG,OAAO,EAAE;cACX;YACF;UACF,CAAE;UAAA/F,QAAA,gBACFxC,OAAA,CAACjC,GAAG;YAACqD,EAAE,EAAE;cAAEkB,QAAQ,EAAE,GAAG;cAAE0H,EAAE,EAAE,MAAM;cAAE3I,QAAQ,EAAE;YAAW,CAAE;YAAAmB,QAAA,gBAC3DxC,OAAA,CAACV,SAAS;cACR6Z,SAAS;cACT7L,SAAS;cACT8L,KAAK,EAAEhJ,WAAY;cACnBiJ,QAAQ,EAAE5S,CAAC,IAAI4J,cAAc,CAAC5J,CAAC,CAACC,MAAM,CAAC0S,KAAK,CAAE;cAC9CE,WAAW,EAAC,wBAAwB;cACpCxW,OAAO,EAAC,UAAU;cAClB1B,EAAE,EAAE;gBACFqB,QAAQ,EAAE,EAAE;gBACZd,OAAO,EAAE,MAAM;gBACf,0BAA0B,EAAE;kBAC1BI,YAAY,EAAE,CAAC;kBACfU,QAAQ,EAAE,EAAE;kBACZ,kBAAkB,EAAE;oBAClBsB,WAAW,EAAE;kBACf,CAAC;kBACD,wBAAwB,EAAE;oBACxBA,WAAW,EAAE,cAAc;oBAC3BwV,WAAW,EAAE;kBACf;gBACF;cACF,CAAE;cACFC,UAAU,EAAE;gBACVC,cAAc,eACZzZ,OAAA,CAACT,UAAU;kBAAC6B,EAAE,EAAE;oBAAEQ,KAAK,EAAE,UAAU;oBAAEsC,EAAE,EAAE;kBAAE;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAEnD;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7C,OAAA,CAACb,UAAU;cACTqF,OAAO,EAAEwP,WAAY;cACrB5S,EAAE,EAAE;gBACFC,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,CAAC;gBACNG,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,EAAE;gBACVC,OAAO,EAAE,kBAAkB;gBAC3B,SAAS,EAAE;kBACTA,OAAO,EAAE;gBACX;cACF,CAAE;cACF,cAAW,OAAO;cAAAa,QAAA,eAElBxC,OAAA,CAACX,SAAS;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAELuN,WAAW,CAACyB,IAAI,CAAC,CAAC,iBACjB7R,OAAA,CAACjC,GAAG;YAACqD,EAAE,EAAE;cACPkB,QAAQ,EAAE,GAAG;cACb0H,EAAE,EAAE,MAAM;cACVE,EAAE,EAAE,CAAC;cACLvI,OAAO,EAAE,MAAM;cACfI,YAAY,EAAE,CAAC;cACfC,SAAS,EAAE,4BAA4B;cACvC4B,CAAC,EAAE,CAAC;cACJ8V,SAAS,EAAE,MAAM;cACjB1L,SAAS,EAAE;YACb,CAAE;YAAAxL,QAAA,EACCyR,cAAc,CAAC3Q,MAAM,KAAK,CAAC,gBAC1BtD,OAAA,CAAClC,UAAU;cAACgF,OAAO,EAAC,OAAO;cAAC1B,EAAE,EAAE;gBAAEQ,KAAK,EAAE,UAAU;gBAAEuG,SAAS,EAAE,QAAQ;gBAAErG,EAAE,EAAE;cAAE,CAAE;cAAAU,QAAA,GAAC,qCAC/C,EAAC4N,WAAW,EAAC,IACjD;YAAA;cAAA1N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,gBAEb7C,OAAA,CAAAE,SAAA;cAAAsC,QAAA,gBACExC,OAAA,CAAClC,UAAU;gBAACgF,OAAO,EAAC,OAAO;gBAAC1B,EAAE,EAAE;kBAAEQ,KAAK,EAAE,UAAU;kBAAEiC,EAAE,EAAE,CAAC;kBAAEd,UAAU,EAAE;gBAAI,CAAE;gBAAAP,QAAA,GAAC,YACnE,EAACyR,cAAc,CAAC3Q,MAAM,EAAC,SACnC;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZoR,cAAc,CAAC1Q,GAAG,CAAEoL,IAAI,iBACvB3O,OAAA,CAACjC,GAAG;gBAEFyG,OAAO,EAAEA,CAAA,KAAM;kBACbwP,WAAW,CAAC,CAAC;kBACbnK,eAAe,CAAC8E,IAAI,CAAChI,EAAE,CAAC;gBAC1B,CAAE;gBACFvF,EAAE,EAAE;kBACFU,EAAE,EAAE,CAAC;kBACLD,EAAE,EAAE,CAAC;kBACL4K,YAAY,EAAE,mBAAmB;kBACjChI,MAAM,EAAE,SAAS;kBACjB1C,YAAY,EAAE,CAAC;kBACf,cAAc,EAAE;oBAAE0K,YAAY,EAAE;kBAAE,CAAC;kBACnC,SAAS,EAAE;oBACT9K,OAAO,EAAE,0BAA0B;oBACnCS,SAAS,EAAE,kBAAkB;oBAC7BC,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAG,QAAA,gBAEFxC,OAAA,CAAClC,UAAU;kBAACgF,OAAO,EAAC,WAAW;kBAAC1B,EAAE,EAAE;oBAAE2B,UAAU,EAAE,GAAG;oBAAEnB,KAAK,EAAE,cAAc;oBAAEiC,EAAE,EAAE;kBAAI,CAAE;kBAAArB,QAAA,EACrFmM,IAAI,CAAC7I;gBAAK;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACb7C,OAAA,CAAClC,UAAU;kBAACgF,OAAO,EAAC,SAAS;kBAAC1B,EAAE,EAAE;oBAAEQ,KAAK,EAAE,UAAU;oBAAEK,OAAO,EAAE,OAAO;oBAAE4B,EAAE,EAAE;kBAAE,CAAE;kBAAArB,QAAA,GAC9EmM,IAAI,CAAC3I,QAAQ,EAAC,UAAG,EAAC,IAAIhB,IAAI,CAAC2J,IAAI,CAAC5J,IAAI,CAAC,CAACkH,kBAAkB,CAAC,OAAO,EAAE;oBAAEC,GAAG,EAAE,SAAS;oBAAEC,KAAK,EAAE,MAAM;oBAAEC,IAAI,EAAE;kBAAU,CAAC,CAAC;gBAAA;kBAAA1J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5G,CAAC,eACb7C,OAAA,CAAClC,UAAU;kBAACgF,OAAO,EAAC,OAAO;kBAAC1B,EAAE,EAAE;oBAAEQ,KAAK,EAAE,UAAU;oBAAE6D,UAAU,EAAE;kBAAI,CAAE;kBAAAjD,QAAA,EACpEmM,IAAI,CAACpE,WAAW,CAACjH,MAAM,GAAG,GAAG,GAAGqL,IAAI,CAACpE,WAAW,CAACyB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG2C,IAAI,CAACpE;gBAAW;kBAAA7H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC;cAAA,GA3BR8L,IAAI,CAAChI,EAAE;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4BT,CACN,CAAC;YAAA,eACF;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,eACJ,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAEpB;EACA,oBACE7C,OAAA,CAACnB,aAAa;IAACqS,KAAK,EAAE+H,WAAY;IAAAzW,QAAA,gBAChCxC,OAAA,CAAC2K,gBAAgB;MACf7D,MAAM,EAAEA,MAAO;MACflC,QAAQ,EAAEA,QAAS;MACnBgG,WAAW,EAAEA,WAAY;MACzBxH,cAAc,EAAEA,cAAe;MAC/BF,gBAAgB,EAAEA,gBAAiB;MACnCC,mBAAmB,EAAEA,mBAAoB;MACzC0H,aAAa,EAAEA,aAAc;MAC7BC,gBAAgB,EAAEA,gBAAiB;MACnCC,OAAO,EAAEA,OAAQ;MACjBC,SAAS,EAAEA,SAAU;MACrBC,YAAY,EAAEA,YAAa;MAC3BC,aAAa,EAAEA,aAAc;MAC7BC,WAAW,EAAEA,WAAY;MACzBC,kBAAkB,EAAEA,kBAAmB;MACvCC,WAAW,EAAEA,WAAY;MACzB/B,UAAU,EAAE6J,cAAe;MAC3B7H,cAAc,EAAEA,cAAe;MAC/BC,aAAa,EAAEwN,iBAAkB;MACjCvN,qBAAqB,EAAEA,qBAAsB;MAC7ChC,WAAW,EAAEK,eAAgB;MAC7B4B,UAAU,EAAEA,UAAW;MACvBC,QAAQ,EAAEA,QAAS;MACnBC,IAAI,EAAEA,IAAK;MACXlC,eAAe,EAAEA,eAAgB;MACjCmC,YAAY,EAAEA,YAAa;MAC3BlC,MAAM,EAAE4I,UAAW;MACnBzG,SAAS,EAAEA;IAAU;MAAAnJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAGF7C,OAAA,CAACgB,KAAK;MACJC,OAAO,EAAEwP,KAAK,CAACxP,OAAQ;MACvBC,SAAS,EAAEuP,KAAK,CAACE,OAAQ;MACzBxP,OAAO,EAAEA,CAAA,KAAMuP,QAAQ,CAAC;QAAEC,OAAO,EAAE,KAAK;QAAE1P,OAAO,EAAE;MAAG,CAAC;IAAE;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,EAEDqN,YAAY,iBACXlQ,OAAA,CAACjC,GAAG;MAACqD,EAAE,EAAE;QACPC,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACN8G,IAAI,EAAE,CAAC;QACP3G,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,wBAAwB;QACjCK,SAAS,EAAE,CAAC;QACZH,EAAE,EAAE;UAAEN,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QACpB8I,EAAE,EAAE;UAAE/I,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAAE;QACtB6I,EAAE,EAAE;MACN,CAAE;MAAA7H,QAAA,gBACAxC,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UAAEkB,QAAQ,EAAE,GAAG;UAAE0H,EAAE,EAAE,MAAM;UAAE3I,QAAQ,EAAE;QAAW,CAAE;QAAAmB,QAAA,gBAC3DxC,OAAA,CAACV,SAAS;UACR6Z,SAAS;UACT7L,SAAS;UACT8L,KAAK,EAAEhJ,WAAY;UACnBiJ,QAAQ,EAAE5S,CAAC,IAAI4J,cAAc,CAAC5J,CAAC,CAACC,MAAM,CAAC0S,KAAK,CAAE;UAC9CE,WAAW,EAAC,wBAAwB;UACpCxW,OAAO,EAAC,UAAU;UAClB1B,EAAE,EAAE;YAAEqB,QAAQ,EAAE,EAAE;YAAEd,OAAO,EAAE;UAAO;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACF7C,OAAA,CAACb,UAAU;UACTqF,OAAO,EAAEwP,WAAY;UACrB5S,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEC,GAAG,EAAE,CAAC;YAAEG,KAAK,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAG,CAAE;UAC3D,cAAW,OAAO;UAAAc,QAAA,eAElBxC,OAAA,CAACX,SAAS;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAELuN,WAAW,CAACyB,IAAI,CAAC,CAAC,iBACjB7R,OAAA,CAACjC,GAAG;QAACqD,EAAE,EAAE;UAAEkB,QAAQ,EAAE,GAAG;UAAE0H,EAAE,EAAE,MAAM;UAAEE,EAAE,EAAE,CAAC;UAAEvI,OAAO,EAAE,MAAM;UAAEI,YAAY,EAAE,CAAC;UAAEC,SAAS,EAAE,CAAC;UAAE4B,CAAC,EAAE;QAAE,CAAE;QAAApB,QAAA,EACjGyR,cAAc,CAAC3Q,MAAM,KAAK,CAAC,gBAC1BtD,OAAA,CAAClC,UAAU;UAACgF,OAAO,EAAC,OAAO;UAAC1B,EAAE,EAAE;YAAEQ,KAAK,EAAE,UAAU;YAAEuG,SAAS,EAAE,QAAQ;YAAErG,EAAE,EAAE;UAAE,CAAE;UAAAU,QAAA,EAAC;QAEnF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,GAEboR,cAAc,CAAC1Q,GAAG,CAAEoL,IAAI,iBACtB3O,OAAA,CAACjC,GAAG;UAAeqD,EAAE,EAAE;YAAEU,EAAE,EAAE,CAAC;YAAED,EAAE,EAAE,CAAC;YAAE4K,YAAY,EAAE,gBAAgB;YAAEhI,MAAM,EAAE,SAAS;YAAE,cAAc,EAAE;cAAEgI,YAAY,EAAE;YAAE;UAAE,CAAE;UAAAjK,QAAA,gBAC9HxC,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE;YAAe,CAAE;YAAAY,QAAA,EAAEmM,IAAI,CAAC7I;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzG7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,SAAS;YAAC1B,EAAE,EAAE;cAAEQ,KAAK,EAAE;YAAW,CAAE;YAAAY,QAAA,GAAEmM,IAAI,CAAC3I,QAAQ,EAAC,UAAQ,EAAC,IAAIhB,IAAI,CAAC2J,IAAI,CAAC5J,IAAI,CAAC,CAACkH,kBAAkB,CAAC,OAAO,EAAE;cAAEC,GAAG,EAAE,SAAS;cAAEC,KAAK,EAAE,MAAM;cAAEC,IAAI,EAAE;YAAU,CAAC,CAAC;UAAA;YAAA1J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClM7C,OAAA,CAAClC,UAAU;YAACgF,OAAO,EAAC,OAAO;YAAC1B,EAAE,EAAE;cAAEQ,KAAK,EAAE;YAAW,CAAE;YAAAY,QAAA,EACnDmM,IAAI,CAACpE,WAAW,CAACjH,MAAM,GAAG,EAAE,GAAGqL,IAAI,CAACpE,WAAW,CAACyB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG2C,IAAI,CAACpE;UAAW;YAAA7H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC;QAAA,GALL8L,IAAI,CAAChI,EAAE;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMZ,CACN;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEY,CAAC;AAEpB;AAAC2M,GAAA,CApkCuBD,WAAW;EAAA,QAChB9R,WAAW,EACcC,OAAO,EAyCnCiB,QAAQ,EACJD,aAAa;AAAA;AAAAib,GAAA,GA5CTpK,WAAW;AAAA,IAAAvM,EAAA,EAAA0B,GAAA,EAAAkC,GAAA,EAAAmC,GAAA,EAAA2B,GAAA,EAAA8D,GAAA,EAAAK,GAAA,EAAA8K,GAAA;AAAAC,YAAA,CAAA5W,EAAA;AAAA4W,YAAA,CAAAlV,GAAA;AAAAkV,YAAA,CAAAhT,GAAA;AAAAgT,YAAA,CAAA7Q,GAAA;AAAA6Q,YAAA,CAAAlP,GAAA;AAAAkP,YAAA,CAAApL,GAAA;AAAAoL,YAAA,CAAA/K,GAAA;AAAA+K,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}