<?php
// Script untuk membuat default logo PNG
$width = 200;
$height = 200;

// Create image
$image = imagecreate($width, $height);

// Colors
$bg_color = imagecolorallocate($image, 59, 130, 246); // Blue background
$text_color = imagecolorallocate($image, 255, 255, 255); // White text

// Fill background
imagefill($image, 0, 0, $bg_color);

// Add text
$text = "LOGO";
$font_size = 5;
$text_width = imagefontwidth($font_size) * strlen($text);
$text_height = imagefontheight($font_size);
$x = ($width - $text_width) / 2;
$y = ($height - $text_height) / 2;

imagestring($image, $font_size, $x, $y, $text, $text_color);

// Save to uploads folder
$upload_dir = __DIR__ . '/uploads';
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

$filename = $upload_dir . '/default-logo.png';
imagepng($image, $filename);
imagedestroy($image);

echo "Default logo created: " . $filename;
?>
