<?php
require_once __DIR__ . '/config.php';

// Start session and check admin authentication
session_start();

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // Not logged in, redirect to login page
    header('Location: auth/login.php');
    exit();
}

// Security check: Verify IP and User Agent (optional but recommended)
$currentIp = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
$currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

if (isset($_SESSION['user_ip']) && $_SESSION['user_ip'] !== $currentIp) {
    // IP changed, possible session hijacking
    session_destroy();
    header('Location: auth/login.php?security=ip_changed');
    exit();
}

// Check session timeout (2 hours)
if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 7200) {
    // Session expired
    session_destroy();
    header('Location: auth/login.php?expired=1');
    exit();
}

// Check inactivity timeout (30 minutes)
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > 1800) {
    // Inactive too long
    session_destroy();
    header('Location: auth/login.php?timeout=inactive');
    exit();
}

// Update last activity time
$_SESSION['last_activity'] = time();

// Get current page
$page = $_GET['page'] ?? 'dashboard';

// Get current settings and fix logo path - Use data URL or base64 if needed
$website_logo = getSetting('website_logo');

// Debug: Log the logo value from database
error_log("Dashboard.php - Logo from database: " . substr($website_logo, 0, 100) . (strlen($website_logo) > 100 ? '...' : ''));

// Always use a working fallback first
$defaultLogo = 'data:image/svg+xml;base64,' . base64_encode('
<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="#3B82F6">
  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
</svg>');

if (!empty($website_logo) && strpos($website_logo, 'assets/logo.png') === false) {
    // Check if logo is base64 data from database
    if (strpos($website_logo, 'data:image/') === 0) {
        // Logo is base64 data - use directly
        // No processing needed, $website_logo already contains the base64 data
    } else {
        // Logo is file path - try to construct the correct path
        $filename = basename($website_logo);
    $possiblePaths = [
        __DIR__ . '/../../../uploads/' . $filename,
        __DIR__ . '/../../uploads/' . $filename,
        __DIR__ . '/uploads/' . $filename,
        __DIR__ . '/assets/' . $filename,
        $_SERVER['DOCUMENT_ROOT'] . '/react-news/uploads/' . $filename
    ];

    $foundPath = null;
    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            $foundPath = $path;
            break;
        }
    }

    if ($foundPath) {
        // Convert local path to URL
        if (strpos($foundPath, __DIR__ . '/../../../uploads/') === 0) {
            $website_logo = 'http://localhost/react-news/uploads/' . $filename;
        } else if (strpos($foundPath, __DIR__ . '/../../uploads/') === 0) {
            $website_logo = 'http://localhost/react-news/frontend/uploads/' . $filename;
        } else if (strpos($foundPath, __DIR__ . '/uploads/') === 0) {
            $website_logo = 'http://localhost/react-news/frontend/src/pages/admin/uploads/' . $filename;
        } else if (strpos($foundPath, __DIR__ . '/assets/') === 0) {
            $website_logo = 'http://localhost/react-news/frontend/src/pages/admin/assets/' . $filename;
        } else {
            // Use relative path from document root
            $relativePath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $foundPath);
            $website_logo = 'http://localhost/react-news' . str_replace('\\', '/', $relativePath);
        }
    } else {
        $website_logo = $defaultLogo;
    }
}
} else {
    $website_logo = $defaultLogo;
}

// Debug: Log final logo value
error_log("Dashboard.php - Final logo value: " . substr($website_logo, 0, 100) . (strlen($website_logo) > 100 ? '...' : ''));

$settings = [
    'website_name' => getSetting('website_name') ?: 'React News Portal',
    'website_logo' => $website_logo,
    'website_description' => getSetting('website_description') ?: 'Portal berita terkini dan terpercaya',
    'primary_color' => getSetting('primary_color') ?: '#3B82F6',
    'secondary_color' => getSetting('secondary_color') ?: '#10B981',
    'accent_color' => getSetting('accent_color') ?: '#F59E0B'
];

// Get statistics for dashboard
$stats = getStats();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($settings['website_name']); ?> - Admin Dashboard</title>

    <?php
    // Add favicon from database logo (base64 or file path)
    $faviconUrl = $defaultLogo; // Use SVG fallback as default
    if (!empty($website_logo)) {
        if (strpos($website_logo, 'data:image/') === 0) {
            // If logo is base64 data, use it directly
            $faviconUrl = $website_logo;
        } elseif (strpos($website_logo, 'data:image/svg') === false) {
            // If we have a file path (not SVG fallback), use it as favicon
            $faviconUrl = $website_logo;
        }
    }
    ?>
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($faviconUrl); ?>">
    <link rel="shortcut icon" type="image/x-icon" href="<?php echo htmlspecialchars($faviconUrl); ?>">
    <link rel="apple-touch-icon" href="<?php echo htmlspecialchars($faviconUrl); ?>">

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Image Helper -->
    <script src="assets/js/image-helper.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '<?php echo $settings['primary_color']; ?>',
                        secondary: '<?php echo $settings['secondary_color']; ?>',
                        accent: '<?php echo $settings['accent_color']; ?>',
                        dark: '#1F2937',
                        light: '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        .mobile-hidden {
            transform: translateX(-100%);
        }
        
        @media (min-width: 1024px) {
            .mobile-hidden {
                transform: translateX(0);
            }
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .nav-item {
            transition: all 0.2s ease;
        }
        
        .nav-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }
        
        .nav-item.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-right: 4px solid white;
        }

        /* Notification Dropdown Styles */
        .notification-item {
            transition: background-color 0.2s ease;
        }

        .notification-item:hover {
            background-color: #f8fafc !important;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Badge animation */
        #notificationBadge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        /* Dropdown animation */
        #notificationDropdown {
            animation: slideDown 0.2s ease-out;
            transform-origin: top right;
            transition: all 0.2s ease;
        }

        #notificationDropdown.hidden {
            opacity: 0;
            transform: translateY(-10px) scale(0.95);
            pointer-events: none;
        }

        #notificationDropdown:not(.hidden) {
            opacity: 1;
            transform: translateY(0) scale(1);
            pointer-events: auto;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* SweetAlert2 Custom Styles */
        .swal2-popup {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            border-radius: 12px;
        }

        /* Login Success SweetAlert Styling */
        .swal2-popup.rounded-xl {
            border-radius: 1rem !important;
        }

        .swal2-title.text-green-600 {
            color: #059669 !important;
        }

        .swal2-title.text-blue-600 {
            color: #2563eb !important;
        }

        .swal2-html-container .text-blue-600 {
            color: #2563eb !important;
        }

        .swal2-html-container .text-gray-600 {
            color: #4b5563 !important;
        }

        /* Mobile Sidebar Scrollable */
        @media (max-width: 768px) {
            #sidebar {
                overflow-y: auto;
                max-height: 100vh;
            }

            #sidebar nav {
                overflow-y: auto;
                max-height: calc(100vh - 120px);
                padding-bottom: 20px;
            }

            /* Custom scrollbar for sidebar */
            #sidebar nav::-webkit-scrollbar {
                width: 6px;
            }

            #sidebar nav::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }

            #sidebar nav::-webkit-scrollbar-thumb {
                background: rgba(156, 163, 175, 0.6);
                border-radius: 3px;
            }

            #sidebar nav::-webkit-scrollbar-thumb:hover {
                background: rgba(156, 163, 175, 0.8);
            }

            /* Firefox scrollbar */
            #sidebar nav {
                scrollbar-width: thin;
                scrollbar-color: rgba(156, 163, 175, 0.6) rgba(255, 255, 255, 0.1);
            }
        }

        /* Notification Dropdown Responsive */
        @media (max-width: 768px) {
            #notificationDropdown {
                right: 0 !important;
                width: 320px !important;
                max-width: calc(100vw - 2rem) !important;
                max-height: 60vh !important;
            }

            /* Adjust for very small screens */
            @media (max-width: 360px) {
                #notificationDropdown {
                    width: 280px !important;
                    max-width: calc(100vw - 1rem) !important;
                }
            }

            /* Make notifications list more compact on mobile */
            #notificationDropdown .notification-item {
                padding: 8px 12px !important;
            }

            #notificationDropdown .notification-item .text-sm {
                font-size: 13px !important;
            }

            #notificationDropdown .notification-item .text-xs {
                font-size: 11px !important;
            }

            /* Compact header on mobile */
            #notificationDropdown .flex.items-center.justify-between {
                padding: 12px 16px !important;
            }

            #notificationDropdown h3 {
                font-size: 14px !important;
            }

            #notificationDropdown button {
                font-size: 11px !important;
            }
        }

        /* Custom scrollbar for notifications list */
        #notificationsList::-webkit-scrollbar {
            width: 4px;
        }

        #notificationsList::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        #notificationsList::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        #notificationsList::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Firefox scrollbar */
        #notificationsList {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        /* Sidebar close button styling */
        #closeSidebarBtn {
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        #closeSidebarBtn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: scale(1.05);
        }

        #closeSidebarBtn:active {
            transform: scale(0.95);
        }

        /* Hide close button on desktop */
        @media (min-width: 1024px) {
            #closeSidebarBtn {
                display: none;
            }
        }

        .swal2-title {
            font-weight: 600;
            color: #1f2937;
            font-size: 1.5rem;
        }

        .swal2-content {
            color: #6b7280;
            font-size: 1rem;
        }

        .swal2-confirm {
            font-weight: 500;
            border-radius: 8px;
            padding: 12px 24px;
        }

        .swal2-cancel {
            font-weight: 500;
            border-radius: 8px;
            padding: 12px 24px;
        }

        .swal2-icon {
            border-width: 3px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 text-white sidebar-transition lg:relative lg:translate-x-0 mobile-hidden"
             style="background-color: <?= htmlspecialchars($settings['sidebar_color'] ?? '#2563EB') ?>">
            <!-- Logo -->
            <div class="flex items-center justify-between h-16 px-4"
                 style="background-color: <?= htmlspecialchars($settings['sidebar_header_color'] ?? '#1D4ED8') ?>">
                <div class="flex items-center">
                    <img src="<?php echo htmlspecialchars($settings['website_logo']); ?>" alt="Logo" class="w-8 h-8 mr-3 rounded">
                    <h1 class="text-xl font-bold truncate"><?php echo htmlspecialchars($settings['website_name']); ?></h1>
                </div>
                <!-- Close button for mobile -->
                <button id="closeSidebarBtn" class="lg:hidden text-white hover:text-gray-300 transition-colors duration-200 p-2">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Navigation -->
            <nav class="mt-8 px-4">
                <div class="space-y-2">
                    <a href="?page=dashboard" class="nav-item <?php echo $page === 'dashboard' ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="?page=news" class="nav-item <?php echo $page === 'news' ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-newspaper mr-3"></i>
                        <span>Kelola Berita</span>
                    </a>
                    <a href="?page=add-news" class="nav-item <?php echo $page === 'add-news' ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-plus mr-3"></i>
                        <span>Tambah Berita</span>
                    </a>
                    <a href="?page=videos" class="nav-item <?php echo $page === 'videos' ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-video mr-3"></i>
                        <span>Kelola Video</span>
                    </a>
                    <a href="?page=add-video" class="nav-item <?php echo $page === 'add-video' ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-plus mr-3"></i>
                        <span>Tambah Video</span>
                    </a>
                    <a href="?page=categories" class="nav-item <?php echo $page === 'categories' ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-tags mr-3"></i>
                        <span>Kategori</span>
                    </a>
                    <a href="?page=users" class="nav-item <?php echo $page === 'users' ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-users mr-3"></i>
                        <span>Pengguna</span>
                    </a>
                    <a href="?page=settings" class="nav-item <?php echo $page === 'settings' ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-cog mr-3"></i>
                        <span>Pengaturan</span>
                    </a>
                    <a href="http://localhost:3000/" target="_blank" class="nav-item flex items-center px-4 py-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors duration-200">
                        <i class="fas fa-globe mr-3"></i>
                        <span>Lihat Website</span>
                    </a>
                </div>
                
                <!-- User Section -->
                <div class="mt-8 pt-8 border-t border-white border-opacity-20">
                    <a href="#" onclick="logout()" class="nav-item flex items-center px-4 py-3 rounded-lg hover:bg-red-50 hover:text-red-600 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt mr-3"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </div>
        
        <!-- Mobile overlay -->
        <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center">
                        <button id="mobile-menu-btn" class="lg:hidden mr-4 p-2 rounded-md text-gray-600 hover:text-primary hover:bg-gray-100">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h2 class="text-2xl font-bold text-gray-800" id="page-title">
                            <?php
                            $titles = [
                                'dashboard' => 'Dashboard',
                                'news' => 'Kelola Berita',
                                'add-news' => 'Tambah Berita',
                                'edit-news' => 'Edit Berita',
                                'videos' => 'Kelola Video',
                                'add-video' => 'Tambah Video',
                                'edit-video' => 'Edit Video',
                                'categories' => 'Kategori',
                                'users' => 'Pengguna',
                                'add-user' => 'Tambah Pengguna',
                                'edit-user' => 'Edit Pengguna',
                                'settings' => 'Pengaturan'
                            ];
                            echo $titles[$page] ?? 'Dashboard';
                            ?>
                        </h2>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Notifications Dropdown -->
                        <div class="relative">
                            <button id="notificationBtn" class="relative p-2 text-gray-600 hover:text-primary transition-colors duration-200 focus:outline-none">
                                <i class="fas fa-bell text-lg"></i>
                                <span id="notificationBadge" class="absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium hidden">0</span>
                            </button>

                            <!-- Dropdown Menu -->
                            <div id="notificationDropdown" class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 hidden">
                                <!-- Header -->
                                <div class="flex items-center justify-between px-4 py-3 border-b border-gray-200">
                                    <h3 class="text-sm font-semibold text-gray-800">Notifikasi</h3>
                                    <div class="flex items-center space-x-2">
                                        <button id="clearAllNotificationsBtn" class="text-xs text-red-600 hover:text-red-800 font-medium">Hapus Semua</button>
                                        <span class="text-gray-300">|</span>
                                        <button id="markAllReadBtn" class="text-xs text-blue-600 hover:text-blue-800 font-medium">Tandai Dibaca</button>
                                    </div>
                                </div>

                                <!-- Notifications List -->
                                <div id="notificationsList" class="max-h-96 md:max-h-96 max-h-[50vh] overflow-y-auto">
                                    <!-- Loading state -->
                                    <div id="notificationsLoading" class="flex items-center justify-center py-8">
                                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                                        <span class="ml-2 text-sm text-gray-500">Memuat notifikasi...</span>
                                    </div>

                                    <!-- Empty state -->
                                    <div id="notificationsEmpty" class="flex flex-col items-center justify-center py-8 hidden">
                                        <i class="fas fa-bell-slash text-3xl text-gray-300 mb-2"></i>
                                        <p class="text-sm text-gray-500">Tidak ada notifikasi</p>
                                    </div>
                                </div>

                                <!-- Footer -->
                                <div class="px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                                    <button class="text-xs text-gray-600 hover:text-gray-800 w-full text-center">Lihat Semua Notifikasi</button>
                                </div>
                            </div>
                        </div>

                        <!-- User Menu -->
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-user-circle text-2xl mr-2 text-primary"></i>
                            <div class="hidden md:block">
                                <div class="text-sm font-medium">Admin</div>
                                <div class="text-xs text-gray-500">Administrator</div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
                <?php
                // Include the appropriate page
                $allowed_pages = ['dashboard', 'news', 'add-news', 'edit-news', 'videos', 'add-video', 'edit-video', 'categories', 'users', 'add-user', 'edit-user', 'settings'];
                
                if (in_array($page, $allowed_pages)) {
                    $page_file = __DIR__ . "/pages/{$page}.php";
                    if (file_exists($page_file)) {
                        include $page_file;
                    } else {
                        include __DIR__ . '/pages/dashboard.php';
                    }
                } else {
                    include __DIR__ . '/pages/dashboard.php';
                }
                ?>
            </main>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 space-y-2"></div>
    
    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const closeSidebarBtn = document.getElementById('closeSidebarBtn');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');

        // Open sidebar
        mobileMenuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('mobile-hidden');
            sidebarOverlay.classList.toggle('hidden');
        });

        // Close sidebar with X button
        closeSidebarBtn.addEventListener('click', () => {
            sidebar.classList.add('mobile-hidden');
            sidebarOverlay.classList.add('hidden');
        });

        // Close sidebar with overlay click
        sidebarOverlay.addEventListener('click', () => {
            sidebar.classList.add('mobile-hidden');
            sidebarOverlay.classList.add('hidden');
        });

        // Close sidebar with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !sidebar.classList.contains('mobile-hidden')) {
                sidebar.classList.add('mobile-hidden');
                sidebarOverlay.classList.add('hidden');
            }
        });
        
        // Notification system
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };
            
            notification.className = `${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full', 'opacity-0');
            }, 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }
        
        // Logout function with SweetAlert
        function logout() {
            Swal.fire({
                title: 'Konfirmasi Logout',
                text: 'Apakah Anda yakin ingin keluar dari dashboard admin?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: '<i class="fas fa-sign-out-alt mr-2"></i>Ya, Logout',
                cancelButtonText: '<i class="fas fa-times mr-2"></i>Batal',
                reverseButtons: true,
                customClass: {
                    popup: 'rounded-xl',
                    confirmButton: 'rounded-lg px-6 py-2',
                    cancelButton: 'rounded-lg px-6 py-2'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Logging out...',
                        text: 'Membersihkan data sesi...',
                        icon: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Call logout API to destroy server session
                    fetch('api.php?action=admin_logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('🔄 Server logout response:', data);

                        // Clear all client-side data
                        localStorage.clear();
                        sessionStorage.clear();

                        // Clear specific admin auth data
                        localStorage.removeItem('admin_auth');
                        localStorage.removeItem('adminToken');
                        localStorage.removeItem('adminUser');
                        localStorage.removeItem('userInfo');

                        // Show success and redirect
                        setTimeout(() => {
                            Swal.fire({
                                title: 'Logout Berhasil!',
                                text: 'Sesi admin telah dihapus. Anda akan diarahkan ke halaman login...',
                                icon: 'success',
                                timer: 2000,
                                timerProgressBar: true,
                                showConfirmButton: false,
                                customClass: {
                                    popup: 'rounded-xl'
                                }
                            }).then(() => {
                                // Force redirect to login page
                                console.log('🔄 Redirecting to admin login...');
                                console.log('📍 Target URL: auth/login.php');

                                // Use replace to prevent back button issues
                                window.location.replace('auth/login.php');
                            });
                        }, 1000);
                    })
                    .catch(error => {
                        console.error('❌ Logout API error:', error);

                        // Even if API fails, clear client data and redirect
                        localStorage.clear();
                        sessionStorage.clear();

                        Swal.fire({
                            title: 'Logout',
                            text: 'Anda akan diarahkan ke halaman login...',
                            icon: 'info',
                            timer: 1500,
                            timerProgressBar: true,
                            showConfirmButton: false
                        }).then(() => {
                            window.location.replace('auth/login.php');
                        });
                    });
                }
            });
        }
        
        // Global variables for API calls
        const API_BASE = 'api.php';
        
        // Safe fetch function
        async function safeFetch(url, options = {}) {
            try {
                const response = await fetch(url, options);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('Fetch error:', error);
                throw error;
            }
        }

        // Notification System
        class NotificationSystem {
            constructor() {
                this.notificationBtn = document.getElementById('notificationBtn');
                this.notificationDropdown = document.getElementById('notificationDropdown');
                this.notificationBadge = document.getElementById('notificationBadge');
                this.notificationsList = document.getElementById('notificationsList');
                this.notificationsLoading = document.getElementById('notificationsLoading');
                this.notificationsEmpty = document.getElementById('notificationsEmpty');
                this.markAllReadBtn = document.getElementById('markAllReadBtn');
                this.clearAllNotificationsBtn = document.getElementById('clearAllNotificationsBtn');

                this.isOpen = false;
                this.refreshInterval = null;

                this.init();
            }

            init() {
                // Toggle dropdown
                this.notificationBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDropdown();
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (!this.notificationDropdown.contains(e.target) && !this.notificationBtn.contains(e.target)) {
                        this.closeDropdown();
                    }
                });

                // Mark all as read
                this.markAllReadBtn.addEventListener('click', () => {
                    this.markAllAsRead();
                });

                // Clear all notifications
                this.clearAllNotificationsBtn.addEventListener('click', () => {
                    this.clearAllNotifications();
                });

                // Handle window resize for responsive positioning
                window.addEventListener('resize', () => {
                    if (this.isOpen) {
                        this.adjustDropdownPosition();
                    }
                });

                // Initial load
                this.loadNotifications();

                // Auto refresh every 30 seconds
                this.startAutoRefresh();
            }

            toggleDropdown() {
                if (this.isOpen) {
                    this.closeDropdown();
                } else {
                    this.openDropdown();
                }
            }

            openDropdown() {
                this.notificationDropdown.classList.remove('hidden');
                this.isOpen = true;
                this.loadNotifications();

                // Adjust position on mobile to prevent overflow
                this.adjustDropdownPosition();
            }

            adjustDropdownPosition() {
                if (window.innerWidth <= 768) {
                    const dropdown = this.notificationDropdown;
                    const rect = dropdown.getBoundingClientRect();
                    const viewportWidth = window.innerWidth;

                    // If dropdown goes beyond viewport, adjust position
                    if (rect.right > viewportWidth) {
                        const overflow = rect.right - viewportWidth + 16; // 16px margin
                        dropdown.style.transform = `translateX(-${overflow}px)`;
                    } else {
                        dropdown.style.transform = 'translateX(0)';
                    }
                }
            }

            closeDropdown() {
                this.notificationDropdown.classList.add('hidden');
                this.isOpen = false;
            }

            async loadNotifications() {
                try {
                    this.showLoading();

                    const response = await fetch('api.php?action=get_notifications&limit=10');
                    const result = await response.json();

                    if (result.success) {
                        this.renderNotifications(result.data);
                        this.updateBadge(result.unread_count);
                    } else {
                        this.showEmpty();
                    }
                } catch (error) {
                    console.error('Error loading notifications:', error);
                    this.showEmpty();
                }
            }

            renderNotifications(notifications) {
                this.hideLoading();

                if (notifications.length === 0) {
                    this.showEmpty();
                    return;
                }

                this.hideEmpty();

                const html = notifications.map(notification => {
                    const timeAgo = this.getTimeAgo(notification.created_at);
                    const isUnread = !notification.is_read;

                    return `
                        <div class="notification-item px-4 py-3 border-b border-gray-100 hover:bg-gray-50 ${isUnread ? 'bg-blue-50' : ''}"
                             data-id="${notification.id}" data-read="${notification.is_read}">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    ${this.getNotificationIcon(notification.type, notification.action_type)}
                                </div>
                                <div class="flex-1 min-w-0 cursor-pointer" onclick="window.notificationSystem.markAsRead(${notification.id})">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            ${notification.title}
                                        </p>
                                        ${isUnread ? '<div class="w-2 h-2 bg-blue-600 rounded-full"></div>' : ''}
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1 line-clamp-2">
                                        ${notification.message}
                                    </p>
                                    <p class="text-xs text-gray-400 mt-1">
                                        ${timeAgo}
                                    </p>
                                </div>
                                <div class="flex-shrink-0">
                                    <button onclick="event.stopPropagation(); window.notificationSystem.deleteNotification(${notification.id})"
                                            class="text-gray-400 hover:text-red-600 transition-colors duration-200 p-1"
                                            title="Hapus notifikasi">
                                        <i class="fas fa-times text-xs"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                this.notificationsList.innerHTML = html;

                // Add click handlers
                this.notificationsList.querySelectorAll('.notification-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const id = item.dataset.id;
                        const isRead = item.dataset.read === '1';

                        if (!isRead) {
                            this.markAsRead(id);
                        }
                    });
                });
            }

            getNotificationIcon(type, actionType) {
                let iconClass = '';
                let bgColor = '';

                switch (actionType) {
                    case 'create':
                        iconClass = 'fas fa-plus-circle';
                        bgColor = 'bg-green-100 text-green-600';
                        break;
                    case 'update':
                        iconClass = 'fas fa-edit';
                        bgColor = 'bg-blue-100 text-blue-600';
                        break;
                    case 'delete':
                        iconClass = 'fas fa-trash-alt';
                        bgColor = 'bg-red-100 text-red-600';
                        break;
                    case 'upload':
                        iconClass = 'fas fa-cloud-upload-alt';
                        bgColor = 'bg-purple-100 text-purple-600';
                        break;
                    default:
                        iconClass = 'fas fa-info-circle';
                        bgColor = 'bg-gray-100 text-gray-600';
                }

                // Add entity-specific icons
                if (actionType === 'create' || actionType === 'update' || actionType === 'delete') {
                    switch (type) {
                        case 'news':
                            if (actionType === 'create') iconClass = 'fas fa-newspaper';
                            if (actionType === 'update') iconClass = 'fas fa-pen-to-square';
                            if (actionType === 'delete') iconClass = 'fas fa-trash-alt';
                            break;
                        case 'video':
                            if (actionType === 'create') iconClass = 'fas fa-video';
                            if (actionType === 'update') iconClass = 'fas fa-film';
                            if (actionType === 'delete') iconClass = 'fas fa-trash-alt';
                            break;
                        case 'user':
                            if (actionType === 'create') iconClass = 'fas fa-user-plus';
                            if (actionType === 'update') iconClass = 'fas fa-user-edit';
                            if (actionType === 'delete') iconClass = 'fas fa-user-times';
                            break;
                        case 'category':
                            if (actionType === 'create') iconClass = 'fas fa-tags';
                            if (actionType === 'update') iconClass = 'fas fa-tag';
                            if (actionType === 'delete') iconClass = 'fas fa-trash-alt';
                            break;
                        case 'settings':
                            if (actionType === 'create') iconClass = 'fas fa-cog';
                            if (actionType === 'update') iconClass = 'fas fa-tools';
                            if (actionType === 'upload') iconClass = 'fas fa-image';
                            break;
                    }
                }

                return `<div class="w-8 h-8 rounded-full ${bgColor} flex items-center justify-center">
                    <i class="${iconClass} text-xs"></i>
                </div>`;
            }

            updateBadge(count) {
                if (count > 0) {
                    this.notificationBadge.textContent = count > 9 ? '9+' : count.toString();
                    this.notificationBadge.classList.remove('hidden');
                } else {
                    this.notificationBadge.classList.add('hidden');
                }
            }

            async markAsRead(id) {
                try {
                    const formData = new FormData();
                    formData.append('id', id);

                    const response = await fetch('api.php?action=mark_notification_read', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    if (result.success) {
                        this.loadNotifications(); // Refresh notifications
                    }
                } catch (error) {
                    console.error('Error marking notification as read:', error);
                }
            }

            async markAllAsRead() {
                try {
                    const response = await fetch('api.php?action=mark_all_notifications_read', {
                        method: 'POST'
                    });

                    const result = await response.json();
                    if (result.success) {
                        this.loadNotifications(); // Refresh notifications
                        showNotification('Semua notifikasi ditandai sebagai dibaca', 'success');
                    }
                } catch (error) {
                    console.error('Error marking all notifications as read:', error);
                }
            }

            async deleteNotification(id) {
                try {
                    const formData = new FormData();
                    formData.append('id', id);

                    const response = await fetch('api.php?action=delete_notification', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    if (result.success) {
                        this.loadNotifications(); // Refresh notifications
                        showNotification('Notifikasi berhasil dihapus', 'success');
                    }
                } catch (error) {
                    console.error('Error deleting notification:', error);
                    showNotification('Gagal menghapus notifikasi', 'error');
                }
            }

            async clearAllNotifications() {
                // Show confirmation dialog
                const result = await Swal.fire({
                    title: 'Hapus Semua Notifikasi?',
                    text: 'Tindakan ini tidak dapat dibatalkan!',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#ef4444',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Ya, Hapus Semua',
                    cancelButtonText: 'Batal',
                    customClass: {
                        popup: 'rounded-xl'
                    }
                });

                if (result.isConfirmed) {
                    try {
                        const response = await fetch('api.php?action=clear_all_notifications', {
                            method: 'POST'
                        });

                        const result = await response.json();
                        if (result.success) {
                            this.loadNotifications(); // Refresh notifications
                            showNotification('Semua notifikasi berhasil dihapus', 'success');
                        }
                    } catch (error) {
                        console.error('Error clearing all notifications:', error);
                        showNotification('Gagal menghapus semua notifikasi', 'error');
                    }
                }
            }

            showLoading() {
                this.notificationsLoading.classList.remove('hidden');
                this.hideEmpty();
            }

            hideLoading() {
                this.notificationsLoading.classList.add('hidden');
            }

            showEmpty() {
                this.notificationsEmpty.classList.remove('hidden');
                this.hideLoading();
            }

            hideEmpty() {
                this.notificationsEmpty.classList.add('hidden');
            }

            getTimeAgo(dateString) {
                const now = new Date();
                const date = new Date(dateString);
                const diffInSeconds = Math.floor((now - date) / 1000);

                if (diffInSeconds < 60) {
                    return 'Baru saja';
                } else if (diffInSeconds < 3600) {
                    const minutes = Math.floor(diffInSeconds / 60);
                    return `${minutes} menit yang lalu`;
                } else if (diffInSeconds < 86400) {
                    const hours = Math.floor(diffInSeconds / 3600);
                    return `${hours} jam yang lalu`;
                } else {
                    const days = Math.floor(diffInSeconds / 86400);
                    return `${days} hari yang lalu`;
                }
            }

            startAutoRefresh() {
                // Refresh every 30 seconds
                this.refreshInterval = setInterval(() => {
                    this.loadNotifications();
                }, 30000);
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }
        }

        // Initialize notification system when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.notificationSystem = new NotificationSystem();

            // Check for login success parameter
            checkLoginSuccess();
        });

        // Check login success and show SweetAlert
        function checkLoginSuccess() {
            const urlParams = new URLSearchParams(window.location.search);
            const loginStatus = urlParams.get('login');
            const username = urlParams.get('user') || 'admin';

            console.log('🔍 Checking login status:', {
                loginStatus: loginStatus,
                username: username,
                fullUrl: window.location.href
            });

            if (loginStatus === 'success') {
                // Show login success alert
                Swal.fire({
                    title: 'Login Berhasil!',
                    html: `
                        <div class="text-center">
                            <i class="fas fa-user-shield text-blue-600 text-4xl mb-3"></i>
                            <p class="text-lg mb-2">Selamat datang kembali!</p>
                            <p class="text-gray-600">Berhasil login sebagai <strong class="text-blue-600">${username}</strong></p>
                        </div>
                    `,
                    icon: 'success',
                    timer: 3000,
                    timerProgressBar: true,
                    showConfirmButton: false,
                    customClass: {
                        popup: 'rounded-xl',
                        title: 'text-green-600'
                    },
                    backdrop: `
                        rgba(0,123,255,0.1)
                        left top
                        no-repeat
                    `
                }).then(() => {
                    // Clean URL after showing alert
                    const cleanUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, cleanUrl);
                });

                console.log('✅ Login success alert shown for user:', username);

            } else if (loginStatus === 'already') {
                // Show already logged in alert
                Swal.fire({
                    title: 'Sudah Login',
                    html: `
                        <div class="text-center">
                            <i class="fas fa-info-circle text-blue-600 text-4xl mb-3"></i>
                            <p class="text-lg mb-2">Anda sudah login</p>
                            <p class="text-gray-600">Sesi aktif sebagai <strong class="text-blue-600">${username}</strong></p>
                        </div>
                    `,
                    icon: 'info',
                    timer: 2000,
                    timerProgressBar: true,
                    showConfirmButton: false,
                    customClass: {
                        popup: 'rounded-xl',
                        title: 'text-blue-600'
                    }
                }).then(() => {
                    // Clean URL after showing alert
                    const cleanUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, cleanUrl);
                });

                console.log('ℹ️ Already logged in alert shown for user:', username);
            }
        }

        // Helper function to add notification (can be called from other scripts)
        window.addNotification = function(title, message, actionType, entityType, type = 'info', entityId = null) {
            fetch('api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'add_notification',
                    title: title,
                    message: message,
                    action_type: actionType,
                    entity_type: entityType,
                    type: type,
                    entity_id: entityId || ''
                })
            }).then(() => {
                // Refresh notifications after adding
                if (window.notificationSystem) {
                    window.notificationSystem.loadNotifications();
                }
            }).catch(error => {
                console.error('Error adding notification:', error);
            });
        };
    </script>
</body>
</html>
