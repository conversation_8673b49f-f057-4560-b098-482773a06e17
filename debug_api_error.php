<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug API Error</h2>";

try {
    echo "<h3>1. Testing Database Connection</h3>";
    require_once 'frontend/src/pages/admin/connect.php';
    
    $pdo = getConnection();
    echo "✅ Database connection successful<br>";
    
    echo "<h3>2. Testing Table Pengaturan</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'pengaturan'");
    $stmt->execute();
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "✅ Table 'pengaturan' exists<br>";
        
        // Test select
        $stmt = $pdo->prepare("SELECT * FROM pengaturan WHERE id = 1");
        $stmt->execute();
        $pengaturan = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($pengaturan) {
            echo "✅ Data found in pengaturan table<br>";
            echo "<pre>" . print_r($pengaturan, true) . "</pre>";
        } else {
            echo "❌ No data in pengaturan table<br>";
            
            // Insert default data
            echo "Inserting default data...<br>";
            $stmt = $pdo->prepare("INSERT INTO pengaturan (nama_website, deskripsi_website, logo_file_path) VALUES (?, ?, ?)");
            $result = $stmt->execute(['React News Portal', 'Portal berita terkini dan terpercaya', 'uploads/default-logo.png']);
            
            if ($result) {
                echo "✅ Default data inserted<br>";
            } else {
                echo "❌ Failed to insert default data<br>";
            }
        }
    } else {
        echo "❌ Table 'pengaturan' does not exist<br>";
    }
    
    echo "<h3>3. Testing Notifications Table</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'notifications'");
    $stmt->execute();
    $notifTableExists = $stmt->fetch();
    
    if ($notifTableExists) {
        echo "✅ Table 'notifications' exists<br>";
    } else {
        echo "❌ Table 'notifications' does not exist<br>";
        echo "Creating notifications table...<br>";
        
        $createNotifTable = "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type VARCHAR(50) DEFAULT 'info',
            category VARCHAR(50) DEFAULT 'general',
            status VARCHAR(20) DEFAULT 'unread',
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($createNotifTable);
        echo "✅ Notifications table created<br>";
    }
    
    echo "<h3>4. Testing API Functions</h3>";
    
    // Test getNotifications function
    if (function_exists('getNotifications')) {
        echo "✅ getNotifications function exists<br>";
        $notifications = getNotifications(5);
        echo "Notifications count: " . count($notifications) . "<br>";
    } else {
        echo "❌ getNotifications function does not exist<br>";
    }
    
    // Test getUnreadNotificationCount function
    if (function_exists('getUnreadNotificationCount')) {
        echo "✅ getUnreadNotificationCount function exists<br>";
        $count = getUnreadNotificationCount();
        echo "Unread count: " . $count . "<br>";
    } else {
        echo "❌ getUnreadNotificationCount function does not exist<br>";
    }
    
    echo "<h3>5. Simulating API Call</h3>";
    $_GET['action'] = 'get_pengaturan';
    
    ob_start();
    
    // Capture any errors
    set_error_handler(function($severity, $message, $file, $line) {
        throw new ErrorException($message, 0, $severity, $file, $line);
    });
    
    try {
        include 'frontend/src/pages/admin/api.php';
    } catch (Exception $e) {
        echo "❌ API Error: " . $e->getMessage() . "<br>";
        echo "File: " . $e->getFile() . "<br>";
        echo "Line: " . $e->getLine() . "<br>";
    }
    
    $output = ob_get_clean();
    
    echo "API Output:<br>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}
?>
