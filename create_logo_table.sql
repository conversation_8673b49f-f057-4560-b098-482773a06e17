-- Add logo columns to settings table if they don't exist
ALTER TABLE settings
ADD COLUMN IF NOT EXISTS logo_id INT NULL,
ADD COLUMN IF NOT EXISTS logo_filename VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS logo_original_name VA<PERSON>HAR(255) NULL,
ADD COLUMN IF NOT EXISTS logo_file_path VARCHAR(500) NULL,
ADD COLUMN IF NOT EXISTS logo_file_size INT NULL,
ADD COLUMN IF NOT EXISTS logo_mime_type VARCHAR(100) NULL,
ADD COLUMN IF NOT EXISTS logo_uploaded_at TIMESTAMP NULL;

-- Create table for logo management (optional - for history tracking)
CREATE TABLE IF NOT EXISTS logos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT 'Website Logo',
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default logo entry if not exists
INSERT INTO logos (name, filename, original_filename, file_path, file_size, mime_type, is_active)
SELECT 'Website Logo', 'default-logo.png', 'default-logo.png', 'uploads/default-logo.png', 0, 'image/png', 1
WHERE NOT EXISTS (SELECT 1 FROM logos WHERE is_active = 1);

-- Update settings table with default logo info
UPDATE settings SET
    logo_filename = 'default-logo.png',
    logo_original_name = 'default-logo.png',
    logo_file_path = 'uploads/default-logo.png',
    logo_file_size = 0,
    logo_mime_type = 'image/png',
    logo_uploaded_at = NOW()
WHERE id = 1;

-- Also update the website_logo field for backward compatibility
UPDATE settings SET website_logo = 'uploads/default-logo.png' WHERE id = 1;
