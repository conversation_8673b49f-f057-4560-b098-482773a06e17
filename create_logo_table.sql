-- Create table for logo management
CREATE TABLE IF NOT EXISTS logos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT 'Website Logo',
    filename VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    original_filename VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default logo entry if not exists
INSERT INTO logos (name, filename, original_filename, file_path, file_size, mime_type, is_active) 
SELECT 'Website Logo', 'default-logo.png', 'default-logo.png', 'uploads/default-logo.png', 0, 'image/png', 1
WHERE NOT EXISTS (SELECT 1 FROM logos WHERE is_active = 1);

-- Update settings table to reference logo table
UPDATE settings SET value = (SELECT file_path FROM logos WHERE is_active = 1 LIMIT 1) WHERE `key` = 'website_logo';
