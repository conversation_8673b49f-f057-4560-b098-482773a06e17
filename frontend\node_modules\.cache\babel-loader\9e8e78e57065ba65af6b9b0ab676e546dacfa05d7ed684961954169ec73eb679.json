{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\components\\\\VideoFeed.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, IconButton, Avatar, Chip, useTheme, useMediaQuery } from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction VideoItem({\n  video,\n  isActive,\n  onVideoClick\n}) {\n  _s();\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // Load liked status from database\n  useEffect(() => {\n    const loadLikedStatus = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');\n        const data = await response.json();\n        if (data.success && Array.isArray(data.data)) {\n          setIsLiked(data.data.includes(video.id));\n        }\n      } catch (error) {\n        console.error('Failed to load liked status:', error);\n      }\n    };\n    loadLikedStatus();\n  }, [video.id]);\n\n  // Increment view count when video becomes active\n  useEffect(() => {\n    if (isActive) {\n      const incrementView = async () => {\n        try {\n          const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/x-www-form-urlencoded'\n            },\n            body: `id=${video.id}`\n          });\n          const data = await response.json();\n          if (data.success) {\n            console.log('✅ View count incremented for video:', video.id);\n            video.stats.views = data.views || video.stats.views + 1;\n          }\n        } catch (error) {\n          console.error('❌ Failed to increment view count:', error);\n        }\n      };\n\n      // Delay to avoid rapid increments\n      const timer = setTimeout(incrementView, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isActive, video.id, video.stats]);\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    console.log('Generating YouTube embed URL for:', videoId, 'autoplay:', autoplay);\n    if (!videoId) {\n      console.warn('⚠️ No YouTube ID provided, cannot generate embed URL');\n      return 'about:blank'; // Return blank page instead of null\n    }\n    if (videoId.length !== 11) {\n      console.warn('⚠️ Invalid YouTube ID format (should be 11 characters):', videoId);\n    }\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '1',\n      // Enable controls for debugging\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '1',\n      // Allow fullscreen\n      disablekb: '0' // Allow keyboard for debugging\n    });\n    const url = `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n    console.log('Generated YouTube URL:', url);\n    return url;\n  };\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n    try {\n      // Direct to localhost PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: `id=${video.id}`\n      });\n      const data = await response.json();\n      console.log('View increment response:', data);\n      if (data.success) {\n        console.log('✅ View count updated successfully');\n        // Update local view count if needed\n        video.stats.views = data.views || video.stats.views + 1;\n      } else {\n        console.error('❌ Failed to update view count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update view count:', error);\n    }\n  };\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n    try {\n      console.log('Toggling like for video:', video.id);\n\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: `id=${video.id}`\n      });\n      const data = await response.json();\n      console.log('Like toggle response:', data);\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked || !wasLiked);\n        setLikes(data.likes || (wasLiked ? likes - 1 : likes + 1));\n        console.log('✅ Like toggled successfully');\n      } else {\n        console.error('❌ Failed to toggle like on server:', data.message);\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('❌ Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n  const handleShare = async () => {\n    console.log('Sharing video:', video.id);\n    try {\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: `id=${video.id}`\n      });\n      const data = await response.json();\n      console.log('Share response:', data);\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares || video.stats.shares + 1;\n        console.log('✅ Share count updated successfully');\n\n        // Show share options (you can customize this)\n        if (navigator.share) {\n          navigator.share({\n            title: video.title,\n            text: video.description,\n            url: video.videoUrl\n          });\n        } else {\n          // Fallback: copy to clipboard\n          navigator.clipboard.writeText(video.videoUrl);\n          alert('Link video telah disalin ke clipboard!');\n        }\n      } else {\n        console.error('❌ Failed to update share count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update share count:', error);\n    }\n  };\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n  console.log('🎬 Rendering VideoItem with video:', video.title, 'ID:', video.youtubeId, 'Active:', isActive);\n  if (!video || !video.youtubeId) {\n    console.error('❌ Invalid video data - missing video or YouTube ID:', video);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        height: '100%',\n        bgcolor: '#333',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: 'white',\n        textAlign: 'center',\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          fontSize: '3rem',\n          mb: 2\n        },\n        children: \"\\uD83D\\uDCF9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          fontSize: '1.2rem',\n          mb: 1\n        },\n        children: \"Video Tidak Tersedia\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          fontSize: '0.9rem',\n          opacity: 0.7\n        },\n        children: (video === null || video === void 0 ? void 0 : video.title) || 'Video tidak memiliki ID YouTube yang valid'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      height: '100vh',\n      width: '100%',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 10,\n        left: 10,\n        color: 'lime',\n        fontSize: 14,\n        zIndex: 50,\n        bgcolor: 'rgba(0,0,0,0.8)',\n        p: 1,\n        borderRadius: 1\n      },\n      children: [video.title, \" | Type: \", video.videoType, \" | ID: \", video.youtubeId || 'N/A', \" | Active: \", isActive ? 'YES' : 'NO']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: isMobile ? '100%' : '400px',\n        height: isMobile ? '100%' : '700px',\n        bgcolor: '#333',\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: [video.videoType === 'youtube' ?\n      /*#__PURE__*/\n      /* YouTube Video Iframe */\n      _jsxDEV(\"iframe\", {\n        ref: iframeRef,\n        src: getYouTubeEmbedUrl(video.youtubeId, isActive),\n        style: {\n          width: '100%',\n          height: '100%',\n          border: 'none',\n          cursor: 'pointer',\n          backgroundColor: '#000'\n        },\n        onClick: handleVideoClick,\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true,\n        title: video.title,\n        onLoad: () => console.log('✅ YouTube iframe loaded successfully for:', video.youtubeId),\n        onError: () => console.error('❌ YouTube iframe failed to load for:', video.youtubeId)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      /* Uploaded Video Player */\n      _jsxDEV(\"video\", {\n        ref: iframeRef,\n        src: video.videoUrl,\n        style: {\n          width: '100%',\n          height: '100%',\n          objectFit: 'contain',\n          backgroundColor: '#000'\n        },\n        controls: isActive,\n        autoPlay: isActive,\n        muted: !isActive,\n        loop: true,\n        onClick: handleVideoClick,\n        onLoadedData: () => console.log('✅ Upload video loaded successfully for:', video.title),\n        onError: () => console.error('❌ Upload video failed to load for:', video.title)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          bottom: 10,\n          left: 10,\n          right: 10,\n          color: 'white',\n          bgcolor: 'rgba(0,0,0,0.7)',\n          p: 2,\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1\n          },\n          children: video.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 1\n          },\n          children: video.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          children: [\"Views: \", video.stats.views, \" | Likes: \", video.stats.likes]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: video.author.avatar,\n          sx: {\n            width: 48,\n            height: 48,\n            border: '2px solid white',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), video.author.verified && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#1976d2',\n            borderRadius: '50%',\n            width: 20,\n            height: 20,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mt: -1,\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleLike,\n          sx: {\n            color: isLiked ? '#e91e63' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleComment,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSave,\n          sx: {\n            color: isSaved ? '#ffc107' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isSaved ? /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: \"Simpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleShare,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.shares\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          fontSize: 12,\n          mb: 1\n        },\n        children: [formatDate(video.uploadDate), \" \\u2022 \", video.duration]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: 16,\n          mb: 1,\n          lineHeight: 1.3\n        },\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: 14,\n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        },\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: video.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${tag}`,\n          size: \"small\",\n          sx: {\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            fontSize: 11,\n            height: 24,\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)'\n            }\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 5\n  }, this);\n}\n\n// Main VideoFeed Component\n_s(VideoItem, \"Xm5L4dCaQ3eydNKZfI9d1G4E50A=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = VideoItem;\nexport default function VideoFeed() {\n  _s2();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = url => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return match && match[2].length === 11 ? match[2] : null; // Return null if invalid\n  };\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('🎬 Loading videos from database...');\n        console.log('🔗 API URL: http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n        console.log('📡 Response status:', response.status);\n        console.log('📡 Response ok:', response.ok);\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log('📊 API Response:', data);\n        console.log('📊 Data type:', typeof data);\n        console.log('📊 Data.success:', data.success);\n        console.log('📊 Data.data:', data.data);\n        console.log('📊 Is data.data array:', Array.isArray(data.data));\n        console.log('📊 Data count:', data.count);\n        if (data.success && Array.isArray(data.data)) {\n          console.log('Raw data from API:', data.data);\n          console.log('Number of videos in raw data:', data.data.length);\n\n          // Convert database video data to component format\n          const publishedVideos = data.data.filter(video => video.status === 'published');\n          console.log('Published videos after filter:', publishedVideos);\n          console.log('Number of published videos:', publishedVideos.length);\n          const dbVideos = publishedVideos.map(video => {\n            var _video$content, _video$category;\n            console.log('Processing video:', video);\n\n            // Handle different video types\n            const videoType = video.video_type || 'youtube';\n            let youtubeId = null;\n            let videoUrl = '';\n            let thumbnail = '';\n            if (videoType === 'youtube') {\n              youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n              videoUrl = video.youtube_url;\n              thumbnail = youtubeId ? `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg` : '';\n\n              // Skip YouTube videos without valid ID\n              if (!youtubeId) {\n                console.warn('⚠️ Skipping YouTube video without valid ID:', video.title);\n                return null;\n              }\n            } else if (videoType === 'upload') {\n              // Handle uploaded videos\n              if (!video.video_path) {\n                console.warn('⚠️ Skipping upload video without file path:', video.title);\n                return null;\n              }\n\n              // Construct video URL for uploaded files\n              const filename = video.video_path;\n              videoUrl = `http://localhost/react-news/uploads/${filename}`;\n\n              // Use a default thumbnail for uploaded videos\n              thumbnail = 'data:image/svg+xml;base64,' + btoa(`\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"320\" height=\"180\" viewBox=\"0 0 320 180\" fill=\"#f3f4f6\">\n                    <rect width=\"320\" height=\"180\" fill=\"#e5e7eb\"/>\n                    <circle cx=\"160\" cy=\"90\" r=\"30\" fill=\"#9ca3af\"/>\n                    <polygon points=\"150,75 150,105 175,90\" fill=\"white\"/>\n                    <text x=\"160\" y=\"130\" text-anchor=\"middle\" fill=\"#6b7280\" font-family=\"Arial\" font-size=\"12\">Video Upload</text>\n                  </svg>\n                `);\n            } else {\n              console.warn('⚠️ Unknown video type:', videoType, video.title);\n              return null;\n            }\n            console.log('Video type:', videoType, 'YouTube ID:', youtubeId, 'Video URL:', videoUrl);\n            const processedVideo = {\n              id: video.id,\n              videoUrl: videoUrl,\n              youtubeId: youtubeId,\n              videoType: videoType,\n              title: video.title || 'Untitled Video',\n              description: video.description || ((_video$content = video.content) === null || _video$content === void 0 ? void 0 : _video$content.substring(0, 200)) + '...' || 'No description available',\n              tags: video.tags && video.tags.trim() ? video.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.toLowerCase()) || 'video'],\n              author: {\n                name: video.created_by || 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0,\n                views: parseInt(video.views) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video',\n              thumbnail: thumbnail\n            };\n            console.log('Processed video result:', processedVideo);\n            return processedVideo;\n          }).filter(video => video !== null); // Remove null entries\n\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            console.log('✅ Setting videos state with:', dbVideos.length, 'videos');\n            setVideos(dbVideos);\n            setCurrentVideoIndex(0); // Reset to first video\n            console.log('✅ Videos state updated successfully');\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadVideos();\n  }, []);\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n    const handleScroll = e => {\n      if (isScrolling) return;\n      isScrolling = true;\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n      setTimeout(() => {\n        isScrolling = false;\n      }, 300);\n    };\n    const handleTouchStart = e => {\n      startY = e.touches[0].clientY;\n    };\n    const handleTouchEnd = e => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n      if (Math.abs(diffY) > 50) {\n        // Minimum swipe distance\n        isScrolling = true;\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n        setTimeout(() => {\n          isScrolling = false;\n        }, 300);\n      }\n    };\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      color: 'white',\n      padding: {\n        xs: 3,\n        sm: 4\n      },\n      textAlign: 'center',\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)',\n        animation: 'pulse 4s ease-in-out infinite alternate'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 787,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: {\n            xs: 100,\n            sm: 120\n          },\n          height: {\n            xs: 100,\n            sm: 120\n          },\n          borderRadius: '50%',\n          background: 'linear-gradient(45deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',\n          backdropFilter: 'blur(10px)',\n          border: '2px solid rgba(255,255,255,0.3)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          mb: 4,\n          mx: 'auto',\n          boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n        },\n        children: /*#__PURE__*/_jsxDEV(PlayArrowIcon, {\n          sx: {\n            fontSize: {\n              xs: 50,\n              sm: 60\n            },\n            opacity: 0.9,\n            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 800,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          mb: 2,\n          fontWeight: 'bold',\n          fontSize: {\n            xs: '1.8rem',\n            sm: '2.2rem'\n          },\n          background: 'linear-gradient(45deg, #fff 30%, #f0f0f0 90%)',\n          backgroundClip: 'text',\n          WebkitBackgroundClip: 'text',\n          WebkitTextFillColor: 'transparent',\n          textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n        },\n        children: \"Maaf, Saat Ini Belum Ada Video\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          opacity: 0.9,\n          maxWidth: {\n            xs: 280,\n            sm: 400\n          },\n          mb: 5,\n          lineHeight: 1.6,\n          fontSize: {\n            xs: '0.95rem',\n            sm: '1.1rem'\n          },\n          textShadow: '0 1px 2px rgba(0,0,0,0.2)'\n        },\n        children: \"Video menarik akan segera hadir! Silakan kembali lagi nanti untuk menikmati konten video terbaru dari kami.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: reloadVideos,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            backdropFilter: 'blur(10px)',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)',\n              transform: 'scale(1.05)'\n            },\n            transition: 'all 0.3s ease',\n            mb: 2,\n            width: {\n              xs: 56,\n              sm: 64\n            },\n            height: {\n              xs: 56,\n              sm: 64\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-refresh\",\n            style: {\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            opacity: 0.8,\n            display: 'block',\n            fontSize: {\n              xs: '0.75rem',\n              sm: '0.85rem'\n            }\n          },\n          children: \"Ketuk untuk memuat ulang\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: {\n            xs: 3,\n            sm: 4\n          },\n          mt: 4,\n          opacity: 0.6\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            disabled: true,\n            sx: {\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': {\n                opacity: 0.5\n              },\n              width: {\n                xs: 48,\n                sm: 56\n              },\n              height: {\n                xs: 48,\n                sm: 56\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {\n              sx: {\n                fontSize: {\n                  xs: 20,\n                  sm: 24\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: {\n                xs: 10,\n                sm: 12\n              },\n              mt: 0.5,\n              opacity: 0.7\n            },\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            disabled: true,\n            sx: {\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': {\n                opacity: 0.5\n              },\n              width: {\n                xs: 48,\n                sm: 56\n              },\n              height: {\n                xs: 48,\n                sm: 56\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {\n              sx: {\n                fontSize: {\n                  xs: 20,\n                  sm: 24\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: {\n                xs: 10,\n                sm: 12\n              },\n              mt: 0.5,\n              opacity: 0.7\n            },\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 914,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            disabled: true,\n            sx: {\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': {\n                opacity: 0.5\n              },\n              width: {\n                xs: 48,\n                sm: 56\n              },\n              height: {\n                xs: 48,\n                sm: 56\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(ShareIcon, {\n              sx: {\n                fontSize: {\n                  xs: 20,\n                  sm: 24\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: {\n                xs: 10,\n                sm: 12\n              },\n              mt: 0.5,\n              opacity: 0.7\n            },\n            children: \"0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 798,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 773,\n    columnNumber: 5\n  }, this);\n\n  // Loading state\n  const LoadingVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': {\n            transform: 'rotate(0deg)'\n          },\n          '100%': {\n            transform: 'rotate(360deg)'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 966,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      children: \"Memuat Video...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 980,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 957,\n    columnNumber: 5\n  }, this);\n  console.log('VideoFeed render - Loading:', loading, 'Videos count:', videos.length);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        color: 'white',\n        fontSize: 12,\n        zIndex: 100,\n        bgcolor: 'rgba(255,0,0,0.7)',\n        p: 1,\n        textAlign: 'center'\n      },\n      children: [\"Loading: \", loading ? 'Yes' : 'No', \" | Videos: \", videos.length, \" | Current: \", currentVideoIndex]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 999,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(LoadingVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1015,\n      columnNumber: 9\n    }, this) : videos && videos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 50,\n          left: 10,\n          color: 'yellow',\n          fontSize: 14,\n          zIndex: 200,\n          bgcolor: 'rgba(0,0,0,0.8)',\n          p: 1\n        },\n        children: [\"Rendering \", videos.length, \" videos, current: \", currentVideoIndex]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1018,\n        columnNumber: 11\n      }, this), videos.map((video, index) => {\n        console.log(`Rendering video ${index}:`, video.title, 'isActive:', index === currentVideoIndex);\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n            transition: 'transform 0.3s ease-in-out',\n            zIndex: index === currentVideoIndex ? 10 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(VideoItem, {\n            video: video,\n            isActive: index === currentVideoIndex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 17\n          }, this)\n        }, video.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1034,\n          columnNumber: 15\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 20,\n          right: 20,\n          bgcolor: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 2,\n          px: 2,\n          py: 1,\n          zIndex: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12\n          },\n          children: [currentVideoIndex + 1, \" / \", videos.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1069,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(EmptyVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1075,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 989,\n    columnNumber: 5\n  }, this);\n}\n_s2(VideoFeed, \"C87+OgytXDlcze02hYwWuiUEmSU=\");\n_c2 = VideoFeed;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoItem\");\n$RefreshReg$(_c2, \"VideoFeed\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "IconButton", "Avatar", "Chip", "useTheme", "useMediaQuery", "FavoriteIcon", "FavoriteBorderIcon", "ChatBubbleOutlineIcon", "BookmarkBorderIcon", "BookmarkIcon", "ShareIcon", "PlayArrowIcon", "PauseIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoItem", "video", "isActive", "onVideoClick", "_s", "isLiked", "setIsLiked", "isSaved", "setIsSaved", "likes", "setLikes", "stats", "iframeRef", "theme", "isMobile", "breakpoints", "down", "loadLikedStatus", "response", "fetch", "data", "json", "success", "Array", "isArray", "includes", "id", "error", "console", "incrementView", "method", "headers", "body", "log", "views", "timer", "setTimeout", "clearTimeout", "videoHeight", "videoWidth", "getYouTubeEmbedUrl", "videoId", "autoplay", "warn", "length", "params", "URLSearchParams", "mute", "controls", "loop", "playlist", "rel", "showinfo", "modestbranding", "iv_load_policy", "fs", "disablekb", "url", "toString", "handleVideoClick", "title", "message", "handleLike", "wasLiked", "prev", "liked", "handleSave", "handleShare", "shares", "navigator", "share", "text", "description", "videoUrl", "clipboard", "writeText", "alert", "handleComment", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "youtubeId", "sx", "width", "height", "bgcolor", "display", "flexDirection", "alignItems", "justifyContent", "color", "textAlign", "p", "children", "fontSize", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "opacity", "position", "overflow", "top", "left", "zIndex", "borderRadius", "videoType", "ref", "src", "style", "border", "cursor", "backgroundColor", "onClick", "allow", "allowFullScreen", "onLoad", "onError", "objectFit", "autoPlay", "muted", "onLoadedData", "bottom", "right", "variant", "gap", "author", "avatar", "verified", "mt", "fontWeight", "toFixed", "comments", "uploadDate", "duration", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "flexWrap", "tags", "map", "tag", "index", "label", "size", "_c", "VideoFeed", "_s2", "currentVideoIndex", "setCurrentVideoIndex", "videos", "setVideos", "loading", "setLoading", "extractYouTubeId", "regExp", "match", "loadVideos", "status", "ok", "Error", "count", "publishedVideos", "filter", "dbVideos", "_video$content", "_video$category", "video_type", "thumbnail", "youtube_id", "youtube_url", "video_path", "filename", "btoa", "processedVideo", "content", "substring", "trim", "split", "category", "toLowerCase", "name", "created_by", "parseInt", "comments_count", "created_at", "toISOString", "startY", "isScrolling", "handleScroll", "e", "deltaY", "handleTouchStart", "touches", "clientY", "handleTouchEnd", "endY", "changedTouches", "diffY", "window", "addEventListener", "removeEventListener", "reloadVideos", "location", "reload", "EmptyVideoState", "background", "padding", "xs", "sm", "animation", "<PERSON><PERSON>ilter", "mx", "boxShadow", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "max<PERSON><PERSON><PERSON>", "transform", "transition", "className", "disabled", "LoadingVideoState", "borderTop", "px", "py", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/components/VideoFeed.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Avatar,\n  Chip,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nfunction VideoItem({ video, isActive, onVideoClick }) {\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // Load liked status from database\n  useEffect(() => {\n    const loadLikedStatus = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');\n        const data = await response.json();\n\n        if (data.success && Array.isArray(data.data)) {\n          setIsLiked(data.data.includes(video.id));\n        }\n      } catch (error) {\n        console.error('Failed to load liked status:', error);\n      }\n    };\n\n    loadLikedStatus();\n  }, [video.id]);\n\n  // Increment view count when video becomes active\n  useEffect(() => {\n    if (isActive) {\n      const incrementView = async () => {\n        try {\n          const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/x-www-form-urlencoded',\n            },\n            body: `id=${video.id}`\n          });\n\n          const data = await response.json();\n          if (data.success) {\n            console.log('✅ View count incremented for video:', video.id);\n            video.stats.views = data.views || video.stats.views + 1;\n          }\n        } catch (error) {\n          console.error('❌ Failed to increment view count:', error);\n        }\n      };\n\n      // Delay to avoid rapid increments\n      const timer = setTimeout(incrementView, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isActive, video.id, video.stats]);\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    console.log('Generating YouTube embed URL for:', videoId, 'autoplay:', autoplay);\n\n    if (!videoId) {\n      console.warn('⚠️ No YouTube ID provided, cannot generate embed URL');\n      return 'about:blank'; // Return blank page instead of null\n    }\n\n    if (videoId.length !== 11) {\n      console.warn('⚠️ Invalid YouTube ID format (should be 11 characters):', videoId);\n    }\n\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '1', // Enable controls for debugging\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '1', // Allow fullscreen\n      disablekb: '0' // Allow keyboard for debugging\n    });\n    const url = `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n    console.log('Generated YouTube URL:', url);\n    return url;\n  };\n\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n\n    try {\n      // Direct to localhost PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: `id=${video.id}`\n      });\n\n      const data = await response.json();\n      console.log('View increment response:', data);\n\n      if (data.success) {\n        console.log('✅ View count updated successfully');\n        // Update local view count if needed\n        video.stats.views = data.views || video.stats.views + 1;\n      } else {\n        console.error('❌ Failed to update view count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update view count:', error);\n    }\n  };\n\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n\n    try {\n      console.log('Toggling like for video:', video.id);\n\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: `id=${video.id}`\n      });\n\n      const data = await response.json();\n      console.log('Like toggle response:', data);\n\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked || !wasLiked);\n        setLikes(data.likes || (wasLiked ? likes - 1 : likes + 1));\n        console.log('✅ Like toggled successfully');\n      } else {\n        console.error('❌ Failed to toggle like on server:', data.message);\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('❌ Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n\n  const handleShare = async () => {\n    console.log('Sharing video:', video.id);\n\n    try {\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: `id=${video.id}`\n      });\n\n      const data = await response.json();\n      console.log('Share response:', data);\n\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares || video.stats.shares + 1;\n        console.log('✅ Share count updated successfully');\n\n        // Show share options (you can customize this)\n        if (navigator.share) {\n          navigator.share({\n            title: video.title,\n            text: video.description,\n            url: video.videoUrl\n          });\n        } else {\n          // Fallback: copy to clipboard\n          navigator.clipboard.writeText(video.videoUrl);\n          alert('Link video telah disalin ke clipboard!');\n        }\n      } else {\n        console.error('❌ Failed to update share count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update share count:', error);\n    }\n  };\n\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n\n  console.log('🎬 Rendering VideoItem with video:', video.title, 'ID:', video.youtubeId, 'Active:', isActive);\n\n  if (!video || !video.youtubeId) {\n    console.error('❌ Invalid video data - missing video or YouTube ID:', video);\n    return (\n      <Box sx={{\n        width: '100%',\n        height: '100%',\n        bgcolor: '#333',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: 'white',\n        textAlign: 'center',\n        p: 3\n      }}>\n        <Box sx={{ fontSize: '3rem', mb: 2 }}>📹</Box>\n        <Box sx={{ fontSize: '1.2rem', mb: 1 }}>Video Tidak Tersedia</Box>\n        <Box sx={{ fontSize: '0.9rem', opacity: 0.7 }}>\n          {video?.title || 'Video tidak memiliki ID YouTube yang valid'}\n        </Box>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{\n      position: 'relative',\n      height: '100vh',\n      width: '100%',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    }}>\n      {/* Debug Info */}\n      <Box sx={{\n        position: 'absolute',\n        top: 10,\n        left: 10,\n        color: 'lime',\n        fontSize: 14,\n        zIndex: 50,\n        bgcolor: 'rgba(0,0,0,0.8)',\n        p: 1,\n        borderRadius: 1\n      }}>\n        {video.title} | Type: {video.videoType} | ID: {video.youtubeId || 'N/A'} | Active: {isActive ? 'YES' : 'NO'}\n      </Box>\n\n      {/* Video Container */}\n      <Box sx={{\n        width: isMobile ? '100%' : '400px',\n        height: isMobile ? '100%' : '700px',\n        bgcolor: '#333',\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      }}>\n        {/* Render different video types */}\n        {video.videoType === 'youtube' ? (\n          /* YouTube Video Iframe */\n          <iframe\n            ref={iframeRef}\n            src={getYouTubeEmbedUrl(video.youtubeId, isActive)}\n            style={{\n              width: '100%',\n              height: '100%',\n              border: 'none',\n              cursor: 'pointer',\n              backgroundColor: '#000'\n            }}\n            onClick={handleVideoClick}\n            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n            allowFullScreen\n            title={video.title}\n            onLoad={() => console.log('✅ YouTube iframe loaded successfully for:', video.youtubeId)}\n            onError={() => console.error('❌ YouTube iframe failed to load for:', video.youtubeId)}\n          />\n        ) : (\n          /* Uploaded Video Player */\n          <video\n            ref={iframeRef}\n            src={video.videoUrl}\n            style={{\n              width: '100%',\n              height: '100%',\n              objectFit: 'contain',\n              backgroundColor: '#000'\n            }}\n            controls={isActive}\n            autoPlay={isActive}\n            muted={!isActive}\n            loop\n            onClick={handleVideoClick}\n            onLoadedData={() => console.log('✅ Upload video loaded successfully for:', video.title)}\n            onError={() => console.error('❌ Upload video failed to load for:', video.title)}\n          />\n        )}\n\n        {/* Fallback content */}\n        <Box sx={{\n          position: 'absolute',\n          bottom: 10,\n          left: 10,\n          right: 10,\n          color: 'white',\n          bgcolor: 'rgba(0,0,0,0.7)',\n          p: 2,\n          borderRadius: 1\n        }}>\n          <Typography variant=\"h6\" sx={{ mb: 1 }}>\n            {video.title}\n          </Typography>\n          <Typography variant=\"body2\" sx={{ mb: 1 }}>\n            {video.description}\n          </Typography>\n          <Typography variant=\"caption\">\n            Views: {video.stats.views} | Likes: {video.stats.likes}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Video Overlay for Interaction */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      }}\n      onClick={handleVideoClick}\n      />\n\n      {/* Right Side Actions (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      }}>\n        {/* Profile Avatar */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <Avatar\n            src={video.author.avatar}\n            sx={{ \n              width: 48, \n              height: 48, \n              border: '2px solid white',\n              cursor: 'pointer'\n            }}\n          />\n          {video.author.verified && (\n            <Box sx={{\n              bgcolor: '#1976d2',\n              borderRadius: '50%',\n              width: 20,\n              height: 20,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mt: -1,\n              border: '2px solid white'\n            }}>\n              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>\n            </Box>\n          )}\n        </Box>\n\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleLike}\n            sx={{\n              color: isLiked ? '#e91e63' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleComment}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.comments}\n          </Typography>\n        </Box>\n\n        {/* Save Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleSave}\n            sx={{\n              color: isSaved ? '#ffc107' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            Simpan\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleShare}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.shares}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Bottom Info (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      }}>\n        {/* Upload Date and Duration */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.8)', \n          fontSize: 12, \n          mb: 1 \n        }}>\n          {formatDate(video.uploadDate)} • {video.duration}\n        </Typography>\n\n        {/* Title */}\n        <Typography sx={{ \n          color: 'white', \n          fontWeight: 'bold', \n          fontSize: 16, \n          mb: 1,\n          lineHeight: 1.3\n        }}>\n          {video.title}\n        </Typography>\n\n        {/* Description */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.9)', \n          fontSize: 14, \n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        }}>\n          {video.description}\n        </Typography>\n\n        {/* Tags */}\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {video.tags.map((tag, index) => (\n            <Chip\n              key={index}\n              label={`#${tag}`}\n              size=\"small\"\n              sx={{\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                fontSize: 11,\n                height: 24,\n                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }\n              }}\n            />\n          ))}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Main VideoFeed Component\nexport default function VideoFeed() {\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = (url) => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return (match && match[2].length === 11) ? match[2] : null; // Return null if invalid\n  };\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('🎬 Loading videos from database...');\n        console.log('🔗 API URL: http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n\n        console.log('📡 Response status:', response.status);\n        console.log('📡 Response ok:', response.ok);\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        console.log('📊 API Response:', data);\n        console.log('📊 Data type:', typeof data);\n        console.log('📊 Data.success:', data.success);\n        console.log('📊 Data.data:', data.data);\n        console.log('📊 Is data.data array:', Array.isArray(data.data));\n        console.log('📊 Data count:', data.count);\n\n        if (data.success && Array.isArray(data.data)) {\n          console.log('Raw data from API:', data.data);\n          console.log('Number of videos in raw data:', data.data.length);\n\n          // Convert database video data to component format\n          const publishedVideos = data.data.filter(video => video.status === 'published');\n          console.log('Published videos after filter:', publishedVideos);\n          console.log('Number of published videos:', publishedVideos.length);\n\n          const dbVideos = publishedVideos\n            .map(video => {\n              console.log('Processing video:', video);\n\n              // Handle different video types\n              const videoType = video.video_type || 'youtube';\n              let youtubeId = null;\n              let videoUrl = '';\n              let thumbnail = '';\n\n              if (videoType === 'youtube') {\n                youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n                videoUrl = video.youtube_url;\n                thumbnail = youtubeId ? `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg` : '';\n\n                // Skip YouTube videos without valid ID\n                if (!youtubeId) {\n                  console.warn('⚠️ Skipping YouTube video without valid ID:', video.title);\n                  return null;\n                }\n              } else if (videoType === 'upload') {\n                // Handle uploaded videos\n                if (!video.video_path) {\n                  console.warn('⚠️ Skipping upload video without file path:', video.title);\n                  return null;\n                }\n\n                // Construct video URL for uploaded files\n                const filename = video.video_path;\n                videoUrl = `http://localhost/react-news/uploads/${filename}`;\n\n                // Use a default thumbnail for uploaded videos\n                thumbnail = 'data:image/svg+xml;base64,' + btoa(`\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"320\" height=\"180\" viewBox=\"0 0 320 180\" fill=\"#f3f4f6\">\n                    <rect width=\"320\" height=\"180\" fill=\"#e5e7eb\"/>\n                    <circle cx=\"160\" cy=\"90\" r=\"30\" fill=\"#9ca3af\"/>\n                    <polygon points=\"150,75 150,105 175,90\" fill=\"white\"/>\n                    <text x=\"160\" y=\"130\" text-anchor=\"middle\" fill=\"#6b7280\" font-family=\"Arial\" font-size=\"12\">Video Upload</text>\n                  </svg>\n                `);\n              } else {\n                console.warn('⚠️ Unknown video type:', videoType, video.title);\n                return null;\n              }\n\n              console.log('Video type:', videoType, 'YouTube ID:', youtubeId, 'Video URL:', videoUrl);\n\n              const processedVideo = {\n                id: video.id,\n                videoUrl: videoUrl,\n                youtubeId: youtubeId,\n                videoType: videoType,\n                title: video.title || 'Untitled Video',\n                description: video.description || video.content?.substring(0, 200) + '...' || 'No description available',\n                tags: (video.tags && video.tags.trim()) ? video.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [video.category?.toLowerCase() || 'video'],\n                author: {\n                  name: video.created_by || 'News Reporter',\n                  avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                  verified: true\n                },\n                stats: {\n                  likes: parseInt(video.likes) || 0,\n                  comments: parseInt(video.comments_count) || 0,\n                  shares: parseInt(video.shares) || 0,\n                  views: parseInt(video.views) || 0\n                },\n                uploadDate: video.created_at || new Date().toISOString(),\n                duration: video.duration || '03:00',\n                category: video.category || 'Video',\n                thumbnail: thumbnail\n              };\n              console.log('Processed video result:', processedVideo);\n              return processedVideo;\n            })\n            .filter(video => video !== null); // Remove null entries\n\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            console.log('✅ Setting videos state with:', dbVideos.length, 'videos');\n            setVideos(dbVideos);\n            setCurrentVideoIndex(0); // Reset to first video\n            console.log('✅ Videos state updated successfully');\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadVideos();\n  }, []);\n\n\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n\n    const handleScroll = (e) => {\n      if (isScrolling) return;\n      isScrolling = true;\n\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n\n      setTimeout(() => { isScrolling = false; }, 300);\n    };\n\n    const handleTouchStart = (e) => {\n      startY = e.touches[0].clientY;\n    };\n\n    const handleTouchEnd = (e) => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n\n      if (Math.abs(diffY) > 50) { // Minimum swipe distance\n        isScrolling = true;\n\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n\n        setTimeout(() => { isScrolling = false; }, 300);\n      }\n    };\n\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      color: 'white',\n      padding: { xs: 3, sm: 4 },\n      textAlign: 'center',\n      position: 'relative',\n      overflow: 'hidden'\n    }}>\n      {/* Background Animation */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)',\n        animation: 'pulse 4s ease-in-out infinite alternate'\n      }} />\n\n      {/* Main Content */}\n      <Box sx={{ position: 'relative', zIndex: 1 }}>\n        {/* Video Icon with Animation */}\n        <Box sx={{\n          width: { xs: 100, sm: 120 },\n          height: { xs: 100, sm: 120 },\n          borderRadius: '50%',\n          background: 'linear-gradient(45deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)',\n          backdropFilter: 'blur(10px)',\n          border: '2px solid rgba(255,255,255,0.3)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          mb: 4,\n          mx: 'auto',\n          boxShadow: '0 8px 32px rgba(0,0,0,0.3)'\n        }}>\n          <PlayArrowIcon sx={{\n            fontSize: { xs: 50, sm: 60 },\n            opacity: 0.9,\n            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'\n          }} />\n        </Box>\n\n        {/* Title */}\n        <Typography\n          variant=\"h4\"\n          sx={{\n            mb: 2,\n            fontWeight: 'bold',\n            fontSize: { xs: '1.8rem', sm: '2.2rem' },\n            background: 'linear-gradient(45deg, #fff 30%, #f0f0f0 90%)',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n          }}\n        >\n          Maaf, Saat Ini Belum Ada Video\n        </Typography>\n\n        {/* Description */}\n        <Typography\n          variant=\"body1\"\n          sx={{\n            opacity: 0.9,\n            maxWidth: { xs: 280, sm: 400 },\n            mb: 5,\n            lineHeight: 1.6,\n            fontSize: { xs: '0.95rem', sm: '1.1rem' },\n            textShadow: '0 1px 2px rgba(0,0,0,0.2)'\n          }}\n        >\n          Video menarik akan segera hadir! Silakan kembali lagi nanti untuk menikmati konten video terbaru dari kami.\n        </Typography>\n\n        {/* Refresh Button */}\n        <Box sx={{ mt: 2 }}>\n          <IconButton\n            onClick={reloadVideos}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.2)',\n              backdropFilter: 'blur(10px)',\n              border: '1px solid rgba(255, 255, 255, 0.3)',\n              '&:hover': {\n                bgcolor: 'rgba(255, 255, 255, 0.3)',\n                transform: 'scale(1.05)'\n              },\n              transition: 'all 0.3s ease',\n              mb: 2,\n              width: { xs: 56, sm: 64 },\n              height: { xs: 56, sm: 64 }\n            }}\n          >\n            <i className=\"fas fa-refresh\" style={{ fontSize: 24 }} />\n          </IconButton>\n\n          <Typography\n            variant=\"caption\"\n            sx={{\n              opacity: 0.8,\n              display: 'block',\n              fontSize: { xs: '0.75rem', sm: '0.85rem' }\n            }}\n          >\n            Ketuk untuk memuat ulang\n          </Typography>\n        </Box>\n\n        {/* Decorative Elements */}\n        <Box sx={{\n          display: 'flex',\n          gap: { xs: 3, sm: 4 },\n          mt: 4,\n          opacity: 0.6\n        }}>\n          {/* Like Button */}\n          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n            <IconButton\n              disabled\n              sx={{\n                color: 'white',\n                bgcolor: 'rgba(255, 255, 255, 0.1)',\n                '&.Mui-disabled': { opacity: 0.5 },\n                width: { xs: 48, sm: 56 },\n                height: { xs: 48, sm: 56 }\n              }}\n            >\n              <FavoriteBorderIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />\n            </IconButton>\n            <Typography sx={{ color: 'white', fontSize: { xs: 10, sm: 12 }, mt: 0.5, opacity: 0.7 }}>\n              0\n            </Typography>\n          </Box>\n\n          {/* Comment Button */}\n          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n            <IconButton\n              disabled\n              sx={{\n                color: 'white',\n                bgcolor: 'rgba(255, 255, 255, 0.1)',\n                '&.Mui-disabled': { opacity: 0.5 },\n                width: { xs: 48, sm: 56 },\n                height: { xs: 48, sm: 56 }\n              }}\n            >\n              <ChatBubbleOutlineIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />\n            </IconButton>\n            <Typography sx={{ color: 'white', fontSize: { xs: 10, sm: 12 }, mt: 0.5, opacity: 0.7 }}>\n              0\n            </Typography>\n          </Box>\n\n          {/* Share Button */}\n          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n            <IconButton\n              disabled\n              sx={{\n                color: 'white',\n                bgcolor: 'rgba(255, 255, 255, 0.1)',\n                '&.Mui-disabled': { opacity: 0.5 },\n                width: { xs: 48, sm: 56 },\n                height: { xs: 48, sm: 56 }\n              }}\n            >\n              <ShareIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />\n            </IconButton>\n            <Typography sx={{ color: 'white', fontSize: { xs: 10, sm: 12 }, mt: 0.5, opacity: 0.7 }}>\n              0\n            </Typography>\n          </Box>\n        </Box>\n      </Box>\n    </Box>\n  );\n\n  // Loading state\n  const LoadingVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    }}>\n      <Box sx={{\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': { transform: 'rotate(0deg)' },\n          '100%': { transform: 'rotate(360deg)' }\n        }\n      }} />\n\n      <Typography variant=\"body1\">\n        Memuat Video...\n      </Typography>\n    </Box>\n  );\n\n  console.log('VideoFeed render - Loading:', loading, 'Videos count:', videos.length);\n\n  return (\n    <Box sx={{\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    }}>\n      {/* Debug Info */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        color: 'white',\n        fontSize: 12,\n        zIndex: 100,\n        bgcolor: 'rgba(255,0,0,0.7)',\n        p: 1,\n        textAlign: 'center'\n      }}>\n        Loading: {loading ? 'Yes' : 'No'} | Videos: {videos.length} | Current: {currentVideoIndex}\n      </Box>\n\n      {loading ? (\n        <LoadingVideoState />\n      ) : videos && videos.length > 0 ? (\n        <>\n          <Box sx={{\n            position: 'absolute',\n            top: 50,\n            left: 10,\n            color: 'yellow',\n            fontSize: 14,\n            zIndex: 200,\n            bgcolor: 'rgba(0,0,0,0.8)',\n            p: 1\n          }}>\n            Rendering {videos.length} videos, current: {currentVideoIndex}\n          </Box>\n\n          {videos.map((video, index) => {\n            console.log(`Rendering video ${index}:`, video.title, 'isActive:', index === currentVideoIndex);\n            return (\n              <Box\n                key={video.id}\n                sx={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: '100%',\n                  height: '100%',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n                  transition: 'transform 0.3s ease-in-out',\n                  zIndex: index === currentVideoIndex ? 10 : 1\n                }}\n              >\n                <VideoItem\n                  video={video}\n                  isActive={index === currentVideoIndex}\n                />\n              </Box>\n            );\n          })}\n\n          {/* Video Counter */}\n          <Box sx={{\n            position: 'absolute',\n            top: 20,\n            right: 20,\n            bgcolor: 'rgba(0, 0, 0, 0.5)',\n            borderRadius: 2,\n            px: 2,\n            py: 1,\n            zIndex: 4\n          }}>\n            <Typography sx={{ color: 'white', fontSize: 12 }}>\n              {currentVideoIndex + 1} / {videos.length}\n            </Typography>\n          </Box>\n        </>\n      ) : (\n        <EmptyVideoState />\n      )}\n    </Box>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,SAAS,MAAM,2BAA2B;;AAEjD;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAACuB,KAAK,CAACU,KAAK,CAACF,KAAK,CAAC;EACrD,MAAMG,SAAS,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkC,KAAK,GAAG3B,QAAQ,CAAC,CAAC;EACxB,MAAM4B,QAAQ,GAAG3B,aAAa,CAAC0B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACApC,SAAS,CAAC,MAAM;IACd,MAAMqC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4FAA4F,CAAC;QAC1H,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5Cd,UAAU,CAACc,IAAI,CAACA,IAAI,CAACK,QAAQ,CAACxB,KAAK,CAACyB,EAAE,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDV,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAChB,KAAK,CAACyB,EAAE,CAAC,CAAC;;EAEd;EACA9C,SAAS,CAAC,MAAM;IACd,IAAIsB,QAAQ,EAAE;MACZ,MAAM2B,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,EAAE;YACxHW,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAE,MAAM/B,KAAK,CAACyB,EAAE;UACtB,CAAC,CAAC;UAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;UAClC,IAAID,IAAI,CAACE,OAAO,EAAE;YAChBM,OAAO,CAACK,GAAG,CAAC,qCAAqC,EAAEhC,KAAK,CAACyB,EAAE,CAAC;YAC5DzB,KAAK,CAACU,KAAK,CAACuB,KAAK,GAAGd,IAAI,CAACc,KAAK,IAAIjC,KAAK,CAACU,KAAK,CAACuB,KAAK,GAAG,CAAC;UACzD;QACF,CAAC,CAAC,OAAOP,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAC3D;MACF,CAAC;;MAED;MACA,MAAMQ,KAAK,GAAGC,UAAU,CAACP,aAAa,EAAE,IAAI,CAAC;MAC7C,OAAO,MAAMQ,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACjC,QAAQ,EAAED,KAAK,CAACyB,EAAE,EAAEzB,KAAK,CAACU,KAAK,CAAC,CAAC;;EAErC;EACA,MAAM2B,WAAW,GAAGxB,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC/C,MAAMyB,UAAU,GAAGzB,QAAQ,GAAG,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAM0B,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,GAAG,KAAK,KAAK;IACxDd,OAAO,CAACK,GAAG,CAAC,mCAAmC,EAAEQ,OAAO,EAAE,WAAW,EAAEC,QAAQ,CAAC;IAEhF,IAAI,CAACD,OAAO,EAAE;MACZb,OAAO,CAACe,IAAI,CAAC,sDAAsD,CAAC;MACpE,OAAO,aAAa,CAAC,CAAC;IACxB;IAEA,IAAIF,OAAO,CAACG,MAAM,KAAK,EAAE,EAAE;MACzBhB,OAAO,CAACe,IAAI,CAAC,yDAAyD,EAAEF,OAAO,CAAC;IAClF;IAEA,MAAMI,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCJ,QAAQ,EAAEA,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC9BK,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE,GAAG;MAAE;MACfC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAET,OAAO;MACjBU,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,GAAG;MACnBC,EAAE,EAAE,GAAG;MAAE;MACTC,SAAS,EAAE,GAAG,CAAC;IACjB,CAAC,CAAC;IACF,MAAMC,GAAG,GAAG,iCAAiChB,OAAO,IAAII,MAAM,CAACa,QAAQ,CAAC,CAAC,EAAE;IAC3E9B,OAAO,CAACK,GAAG,CAAC,wBAAwB,EAAEwB,GAAG,CAAC;IAC1C,OAAOA,GAAG;EACZ,CAAC;EAED,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC;IACA/B,OAAO,CAACK,GAAG,CAAC,gBAAgB,EAAEhC,KAAK,CAAC2D,KAAK,CAAC;IAE1C,IAAI;MACF;MACA,MAAM1C,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,EAAE;QACxHW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,MAAM/B,KAAK,CAACyB,EAAE;MACtB,CAAC,CAAC;MAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCO,OAAO,CAACK,GAAG,CAAC,0BAA0B,EAAEb,IAAI,CAAC;MAE7C,IAAIA,IAAI,CAACE,OAAO,EAAE;QAChBM,OAAO,CAACK,GAAG,CAAC,mCAAmC,CAAC;QAChD;QACAhC,KAAK,CAACU,KAAK,CAACuB,KAAK,GAAGd,IAAI,CAACc,KAAK,IAAIjC,KAAK,CAACU,KAAK,CAACuB,KAAK,GAAG,CAAC;MACzD,CAAC,MAAM;QACLN,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEP,IAAI,CAACyC,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMmC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,QAAQ,GAAG1D,OAAO;;IAExB;IACAC,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBK,QAAQ,CAACsD,IAAI,IAAI3D,OAAO,GAAG2D,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAE/C,IAAI;MACFpC,OAAO,CAACK,GAAG,CAAC,0BAA0B,EAAEhC,KAAK,CAACyB,EAAE,CAAC;;MAEjD;MACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,uFAAuF,EAAE;QACpHW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,MAAM/B,KAAK,CAACyB,EAAE;MACtB,CAAC,CAAC;MAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCO,OAAO,CAACK,GAAG,CAAC,uBAAuB,EAAEb,IAAI,CAAC;MAE1C,IAAIA,IAAI,CAACE,OAAO,EAAE;QAChB;QACAhB,UAAU,CAACc,IAAI,CAAC6C,KAAK,IAAI,CAACF,QAAQ,CAAC;QACnCrD,QAAQ,CAACU,IAAI,CAACX,KAAK,KAAKsD,QAAQ,GAAGtD,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC,CAAC;QAC1DmB,OAAO,CAACK,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,MAAM;QACLL,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEP,IAAI,CAACyC,OAAO,CAAC;QACjE;QACAvD,UAAU,CAACyD,QAAQ,CAAC;QACpBrD,QAAQ,CAACsD,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACArB,UAAU,CAACyD,QAAQ,CAAC;MACpBrD,QAAQ,CAACsD,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB1D,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAM4D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BvC,OAAO,CAACK,GAAG,CAAC,gBAAgB,EAAEhC,KAAK,CAACyB,EAAE,CAAC;IAEvC,IAAI;MACF;MACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,EAAE;QACxHW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,MAAM/B,KAAK,CAACyB,EAAE;MACtB,CAAC,CAAC;MAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCO,OAAO,CAACK,GAAG,CAAC,iBAAiB,EAAEb,IAAI,CAAC;MAEpC,IAAIA,IAAI,CAACE,OAAO,EAAE;QAChB;QACArB,KAAK,CAACU,KAAK,CAACyD,MAAM,GAAGhD,IAAI,CAACgD,MAAM,IAAInE,KAAK,CAACU,KAAK,CAACyD,MAAM,GAAG,CAAC;QAC1DxC,OAAO,CAACK,GAAG,CAAC,oCAAoC,CAAC;;QAEjD;QACA,IAAIoC,SAAS,CAACC,KAAK,EAAE;UACnBD,SAAS,CAACC,KAAK,CAAC;YACdV,KAAK,EAAE3D,KAAK,CAAC2D,KAAK;YAClBW,IAAI,EAAEtE,KAAK,CAACuE,WAAW;YACvBf,GAAG,EAAExD,KAAK,CAACwE;UACb,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAJ,SAAS,CAACK,SAAS,CAACC,SAAS,CAAC1E,KAAK,CAACwE,QAAQ,CAAC;UAC7CG,KAAK,CAAC,wCAAwC,CAAC;QACjD;MACF,CAAC,MAAM;QACLhD,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEP,IAAI,CAACyC,OAAO,CAAC;MAChE;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMkD,aAAa,GAAGA,CAAA,KAAM;IAC1BjD,OAAO,CAACK,GAAG,CAAC,0BAA0B,EAAEhC,KAAK,CAACyB,EAAE,CAAC;EACnD,CAAC;EAED,MAAMoD,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE,OAAO,aAAa;IACxC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,cAAc;IAClE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,aAAa;EACjD,CAAC;EAED1D,OAAO,CAACK,GAAG,CAAC,oCAAoC,EAAEhC,KAAK,CAAC2D,KAAK,EAAE,KAAK,EAAE3D,KAAK,CAACuF,SAAS,EAAE,SAAS,EAAEtF,QAAQ,CAAC;EAE3G,IAAI,CAACD,KAAK,IAAI,CAACA,KAAK,CAACuF,SAAS,EAAE;IAC9B5D,OAAO,CAACD,KAAK,CAAC,qDAAqD,EAAE1B,KAAK,CAAC;IAC3E,oBACEJ,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,MAAM;QACfC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,QAAQ;QACnBC,CAAC,EAAE;MACL,CAAE;MAAAC,QAAA,gBACAvG,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEY,QAAQ,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9C7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEY,QAAQ,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,EAAC;MAAoB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClE7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEY,QAAQ,EAAE,QAAQ;UAAEM,OAAO,EAAE;QAAI,CAAE;QAAAP,QAAA,EAC3C,CAAAnG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2D,KAAK,KAAI;MAA4C;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7G,OAAA,CAAChB,GAAG;IAAC4G,EAAE,EAAE;MACPmB,QAAQ,EAAE,UAAU;MACpBjB,MAAM,EAAE,OAAO;MACfD,KAAK,EAAE,MAAM;MACbE,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBa,QAAQ,EAAE;IACZ,CAAE;IAAAT,QAAA,gBAEAvG,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPmB,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRd,KAAK,EAAE,MAAM;QACbI,QAAQ,EAAE,EAAE;QACZW,MAAM,EAAE,EAAE;QACVpB,OAAO,EAAE,iBAAiB;QAC1BO,CAAC,EAAE,CAAC;QACJc,YAAY,EAAE;MAChB,CAAE;MAAAb,QAAA,GACCnG,KAAK,CAAC2D,KAAK,EAAC,WAAS,EAAC3D,KAAK,CAACiH,SAAS,EAAC,SAAO,EAACjH,KAAK,CAACuF,SAAS,IAAI,KAAK,EAAC,aAAW,EAACtF,QAAQ,GAAG,KAAK,GAAG,IAAI;IAAA;MAAAqG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eAGN7G,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPC,KAAK,EAAE5E,QAAQ,GAAG,MAAM,GAAG,OAAO;QAClC6E,MAAM,EAAE7E,QAAQ,GAAG,MAAM,GAAG,OAAO;QACnC8E,OAAO,EAAE,MAAM;QACfgB,QAAQ,EAAE,UAAU;QACpBf,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MAAAI,QAAA,GAECnG,KAAK,CAACiH,SAAS,KAAK,SAAS;MAAA;MAC5B;MACArH,OAAA;QACEsH,GAAG,EAAEvG,SAAU;QACfwG,GAAG,EAAE5E,kBAAkB,CAACvC,KAAK,CAACuF,SAAS,EAAEtF,QAAQ,CAAE;QACnDmH,KAAK,EAAE;UACL3B,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACd2B,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,SAAS;UACjBC,eAAe,EAAE;QACnB,CAAE;QACFC,OAAO,EAAE9D,gBAAiB;QAC1B+D,KAAK,EAAC,0FAA0F;QAChGC,eAAe;QACf/D,KAAK,EAAE3D,KAAK,CAAC2D,KAAM;QACnBgE,MAAM,EAAEA,CAAA,KAAMhG,OAAO,CAACK,GAAG,CAAC,2CAA2C,EAAEhC,KAAK,CAACuF,SAAS,CAAE;QACxFqC,OAAO,EAAEA,CAAA,KAAMjG,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAE1B,KAAK,CAACuF,SAAS;MAAE;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC;MAAA;MAEF;MACA7G,OAAA;QACEsH,GAAG,EAAEvG,SAAU;QACfwG,GAAG,EAAEnH,KAAK,CAACwE,QAAS;QACpB4C,KAAK,EAAE;UACL3B,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdmC,SAAS,EAAE,SAAS;UACpBN,eAAe,EAAE;QACnB,CAAE;QACFxE,QAAQ,EAAE9C,QAAS;QACnB6H,QAAQ,EAAE7H,QAAS;QACnB8H,KAAK,EAAE,CAAC9H,QAAS;QACjB+C,IAAI;QACJwE,OAAO,EAAE9D,gBAAiB;QAC1BsE,YAAY,EAAEA,CAAA,KAAMrG,OAAO,CAACK,GAAG,CAAC,yCAAyC,EAAEhC,KAAK,CAAC2D,KAAK,CAAE;QACxFiE,OAAO,EAAEA,CAAA,KAAMjG,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAE1B,KAAK,CAAC2D,KAAK;MAAE;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF,CACF,eAGD7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UACPmB,QAAQ,EAAE,UAAU;UACpBsB,MAAM,EAAE,EAAE;UACVnB,IAAI,EAAE,EAAE;UACRoB,KAAK,EAAE,EAAE;UACTlC,KAAK,EAAE,OAAO;UACdL,OAAO,EAAE,iBAAiB;UAC1BO,CAAC,EAAE,CAAC;UACJc,YAAY,EAAE;QAChB,CAAE;QAAAb,QAAA,gBACAvG,OAAA,CAACf,UAAU;UAACsJ,OAAO,EAAC,IAAI;UAAC3C,EAAE,EAAE;YAAEa,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EACpCnG,KAAK,CAAC2D;QAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACb7G,OAAA,CAACf,UAAU;UAACsJ,OAAO,EAAC,OAAO;UAAC3C,EAAE,EAAE;YAAEa,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EACvCnG,KAAK,CAACuE;QAAW;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACb7G,OAAA,CAACf,UAAU;UAACsJ,OAAO,EAAC,SAAS;UAAAhC,QAAA,GAAC,SACrB,EAACnG,KAAK,CAACU,KAAK,CAACuB,KAAK,EAAC,YAAU,EAACjC,KAAK,CAACU,KAAK,CAACF,KAAK;QAAA;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7G,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPmB,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPoB,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACTlB,MAAM,EAAE,CAAC;QACTO,MAAM,EAAE;MACV,CAAE;MACFE,OAAO,EAAE9D;IAAiB;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGF7G,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPmB,QAAQ,EAAE,UAAU;QACpBuB,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,GAAG;QACXrC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBuC,GAAG,EAAE,CAAC;QACNrB,MAAM,EAAE;MACV,CAAE;MAAAZ,QAAA,gBAEAvG,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EvG,OAAA,CAACb,MAAM;UACLoI,GAAG,EAAEnH,KAAK,CAACqI,MAAM,CAACC,MAAO;UACzB9C,EAAE,EAAE;YACFC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACV2B,MAAM,EAAE,iBAAiB;YACzBC,MAAM,EAAE;UACV;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDzG,KAAK,CAACqI,MAAM,CAACE,QAAQ,iBACpB3I,OAAA,CAAChB,GAAG;UAAC4G,EAAE,EAAE;YACPG,OAAO,EAAE,SAAS;YAClBqB,YAAY,EAAE,KAAK;YACnBvB,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVE,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxByC,EAAE,EAAE,CAAC,CAAC;YACNnB,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,eACAvG,OAAA,CAACf,UAAU;YAAC2G,EAAE,EAAE;cAAEQ,KAAK,EAAE,OAAO;cAAEI,QAAQ,EAAE,EAAE;cAAEqC,UAAU,EAAE;YAAO,CAAE;YAAAtC,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EvG,OAAA,CAACd,UAAU;UACT0I,OAAO,EAAE3D,UAAW;UACpB2B,EAAE,EAAE;YACFQ,KAAK,EAAE5F,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCuF,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAQ,QAAA,EAED/F,OAAO,gBAAGR,OAAA,CAACT,YAAY;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7G,OAAA,CAACR,kBAAkB;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACb7G,OAAA,CAACf,UAAU;UAAC2G,EAAE,EAAE;YAAEQ,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAI,CAAE;UAAArC,QAAA,EACvD3F,KAAK,GAAG,IAAI,GAAG,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEkI,OAAO,CAAC,CAAC,CAAC,GAAG,GAAGlI;QAAK;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EvG,OAAA,CAACd,UAAU;UACT0I,OAAO,EAAE5C,aAAc;UACvBY,EAAE,EAAE;YACFQ,KAAK,EAAE,OAAO;YACdL,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAQ,QAAA,eAEFvG,OAAA,CAACP,qBAAqB;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACb7G,OAAA,CAACf,UAAU;UAAC2G,EAAE,EAAE;YAAEQ,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAI,CAAE;UAAArC,QAAA,EACvDnG,KAAK,CAACU,KAAK,CAACiI;QAAQ;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EvG,OAAA,CAACd,UAAU;UACT0I,OAAO,EAAEvD,UAAW;UACpBuB,EAAE,EAAE;YACFQ,KAAK,EAAE1F,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCqF,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAQ,QAAA,EAED7F,OAAO,gBAAGV,OAAA,CAACL,YAAY;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7G,OAAA,CAACN,kBAAkB;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACb7G,OAAA,CAACf,UAAU;UAAC2G,EAAE,EAAE;YAAEQ,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAI,CAAE;UAAArC,QAAA,EAAC;QAE3D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EvG,OAAA,CAACd,UAAU;UACT0I,OAAO,EAAEtD,WAAY;UACrBsB,EAAE,EAAE;YACFQ,KAAK,EAAE,OAAO;YACdL,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAQ,QAAA,eAEFvG,OAAA,CAACJ,SAAS;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACb7G,OAAA,CAACf,UAAU;UAAC2G,EAAE,EAAE;YAAEQ,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAI,CAAE;UAAArC,QAAA,EACvDnG,KAAK,CAACU,KAAK,CAACyD;QAAM;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7G,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPmB,QAAQ,EAAE,UAAU;QACpBsB,MAAM,EAAE,EAAE;QACVnB,IAAI,EAAE,EAAE;QACRoB,KAAK,EAAE,EAAE;QACTnB,MAAM,EAAE;MACV,CAAE;MAAAZ,QAAA,gBAEAvG,OAAA,CAACf,UAAU;QAAC2G,EAAE,EAAE;UACdQ,KAAK,EAAE,0BAA0B;UACjCI,QAAQ,EAAE,EAAE;UACZC,EAAE,EAAE;QACN,CAAE;QAAAF,QAAA,GACCtB,UAAU,CAAC7E,KAAK,CAAC4I,UAAU,CAAC,EAAC,UAAG,EAAC5I,KAAK,CAAC6I,QAAQ;MAAA;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGb7G,OAAA,CAACf,UAAU;QAAC2G,EAAE,EAAE;UACdQ,KAAK,EAAE,OAAO;UACdyC,UAAU,EAAE,MAAM;UAClBrC,QAAQ,EAAE,EAAE;UACZC,EAAE,EAAE,CAAC;UACLyC,UAAU,EAAE;QACd,CAAE;QAAA3C,QAAA,EACCnG,KAAK,CAAC2D;MAAK;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGb7G,OAAA,CAACf,UAAU;QAAC2G,EAAE,EAAE;UACdQ,KAAK,EAAE,0BAA0B;UACjCI,QAAQ,EAAE,EAAE;UACZC,EAAE,EAAE,CAAC;UACLyC,UAAU,EAAE,GAAG;UACflD,OAAO,EAAE,aAAa;UACtBmD,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,UAAU;UAC3BpC,QAAQ,EAAE;QACZ,CAAE;QAAAT,QAAA,EACCnG,KAAK,CAACuE;MAAW;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGb7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEwC,GAAG,EAAE,CAAC;UAAEa,QAAQ,EAAE;QAAO,CAAE;QAAA9C,QAAA,EACpDnG,KAAK,CAACkJ,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzBzJ,OAAA,CAACZ,IAAI;UAEHsK,KAAK,EAAE,IAAIF,GAAG,EAAG;UACjBG,IAAI,EAAC,OAAO;UACZ/D,EAAE,EAAE;YACFG,OAAO,EAAE,0BAA0B;YACnCK,KAAK,EAAE,OAAO;YACdI,QAAQ,EAAE,EAAE;YACZV,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;cAAEC,OAAO,EAAE;YAA2B;UACnD;QAAE,GATG0D,KAAK;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAtG,EAAA,CAnhBSJ,SAAS;EAAA,QAKFd,QAAQ,EACLC,aAAa;AAAA;AAAAsK,EAAA,GANvBzJ,SAAS;AAohBlB,eAAe,SAAS0J,SAASA,CAAA,EAAG;EAAAC,GAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnL,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACoL,MAAM,EAAEC,SAAS,CAAC,GAAGrL,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsL,OAAO,EAAEC,UAAU,CAAC,GAAGvL,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMwL,gBAAgB,GAAIzG,GAAG,IAAK;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,IAAI;IACrB,MAAM0G,MAAM,GAAG,8DAA8D;IAC7E,MAAMC,KAAK,GAAG3G,GAAG,CAAC2G,KAAK,CAACD,MAAM,CAAC;IAC/B,OAAQC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACxH,MAAM,KAAK,EAAE,GAAIwH,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;EAC9D,CAAC;;EAED;EACAxL,SAAS,CAAC,MAAM;IACd,MAAMyL,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BJ,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACFrI,OAAO,CAACK,GAAG,CAAC,oCAAoC,CAAC;QACjDL,OAAO,CAACK,GAAG,CAAC,4FAA4F,CAAC;;QAEzG;QACA,MAAMf,QAAQ,GAAG,MAAMC,KAAK,CAAC,gFAAgF,CAAC;QAE9GS,OAAO,CAACK,GAAG,CAAC,qBAAqB,EAAEf,QAAQ,CAACoJ,MAAM,CAAC;QACnD1I,OAAO,CAACK,GAAG,CAAC,iBAAiB,EAAEf,QAAQ,CAACqJ,EAAE,CAAC;QAE3C,IAAI,CAACrJ,QAAQ,CAACqJ,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBtJ,QAAQ,CAACoJ,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMlJ,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCO,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAEb,IAAI,CAAC;QACrCQ,OAAO,CAACK,GAAG,CAAC,eAAe,EAAE,OAAOb,IAAI,CAAC;QACzCQ,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAEb,IAAI,CAACE,OAAO,CAAC;QAC7CM,OAAO,CAACK,GAAG,CAAC,eAAe,EAAEb,IAAI,CAACA,IAAI,CAAC;QACvCQ,OAAO,CAACK,GAAG,CAAC,wBAAwB,EAAEV,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,CAAC;QAC/DQ,OAAO,CAACK,GAAG,CAAC,gBAAgB,EAAEb,IAAI,CAACqJ,KAAK,CAAC;QAEzC,IAAIrJ,IAAI,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5CQ,OAAO,CAACK,GAAG,CAAC,oBAAoB,EAAEb,IAAI,CAACA,IAAI,CAAC;UAC5CQ,OAAO,CAACK,GAAG,CAAC,+BAA+B,EAAEb,IAAI,CAACA,IAAI,CAACwB,MAAM,CAAC;;UAE9D;UACA,MAAM8H,eAAe,GAAGtJ,IAAI,CAACA,IAAI,CAACuJ,MAAM,CAAC1K,KAAK,IAAIA,KAAK,CAACqK,MAAM,KAAK,WAAW,CAAC;UAC/E1I,OAAO,CAACK,GAAG,CAAC,gCAAgC,EAAEyI,eAAe,CAAC;UAC9D9I,OAAO,CAACK,GAAG,CAAC,6BAA6B,EAAEyI,eAAe,CAAC9H,MAAM,CAAC;UAElE,MAAMgI,QAAQ,GAAGF,eAAe,CAC7BtB,GAAG,CAACnJ,KAAK,IAAI;YAAA,IAAA4K,cAAA,EAAAC,eAAA;YACZlJ,OAAO,CAACK,GAAG,CAAC,mBAAmB,EAAEhC,KAAK,CAAC;;YAEvC;YACA,MAAMiH,SAAS,GAAGjH,KAAK,CAAC8K,UAAU,IAAI,SAAS;YAC/C,IAAIvF,SAAS,GAAG,IAAI;YACpB,IAAIf,QAAQ,GAAG,EAAE;YACjB,IAAIuG,SAAS,GAAG,EAAE;YAElB,IAAI9D,SAAS,KAAK,SAAS,EAAE;cAC3B1B,SAAS,GAAGvF,KAAK,CAACgL,UAAU,IAAIf,gBAAgB,CAACjK,KAAK,CAACiL,WAAW,CAAC;cACnEzG,QAAQ,GAAGxE,KAAK,CAACiL,WAAW;cAC5BF,SAAS,GAAGxF,SAAS,GAAG,8BAA8BA,SAAS,oBAAoB,GAAG,EAAE;;cAExF;cACA,IAAI,CAACA,SAAS,EAAE;gBACd5D,OAAO,CAACe,IAAI,CAAC,6CAA6C,EAAE1C,KAAK,CAAC2D,KAAK,CAAC;gBACxE,OAAO,IAAI;cACb;YACF,CAAC,MAAM,IAAIsD,SAAS,KAAK,QAAQ,EAAE;cACjC;cACA,IAAI,CAACjH,KAAK,CAACkL,UAAU,EAAE;gBACrBvJ,OAAO,CAACe,IAAI,CAAC,6CAA6C,EAAE1C,KAAK,CAAC2D,KAAK,CAAC;gBACxE,OAAO,IAAI;cACb;;cAEA;cACA,MAAMwH,QAAQ,GAAGnL,KAAK,CAACkL,UAAU;cACjC1G,QAAQ,GAAG,uCAAuC2G,QAAQ,EAAE;;cAE5D;cACAJ,SAAS,GAAG,4BAA4B,GAAGK,IAAI,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC;YACJ,CAAC,MAAM;cACLzJ,OAAO,CAACe,IAAI,CAAC,wBAAwB,EAAEuE,SAAS,EAAEjH,KAAK,CAAC2D,KAAK,CAAC;cAC9D,OAAO,IAAI;YACb;YAEAhC,OAAO,CAACK,GAAG,CAAC,aAAa,EAAEiF,SAAS,EAAE,aAAa,EAAE1B,SAAS,EAAE,YAAY,EAAEf,QAAQ,CAAC;YAEvF,MAAM6G,cAAc,GAAG;cACrB5J,EAAE,EAAEzB,KAAK,CAACyB,EAAE;cACZ+C,QAAQ,EAAEA,QAAQ;cAClBe,SAAS,EAAEA,SAAS;cACpB0B,SAAS,EAAEA,SAAS;cACpBtD,KAAK,EAAE3D,KAAK,CAAC2D,KAAK,IAAI,gBAAgB;cACtCY,WAAW,EAAEvE,KAAK,CAACuE,WAAW,IAAI,EAAAqG,cAAA,GAAA5K,KAAK,CAACsL,OAAO,cAAAV,cAAA,uBAAbA,cAAA,CAAeW,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK,IAAI,0BAA0B;cACxGrC,IAAI,EAAGlJ,KAAK,CAACkJ,IAAI,IAAIlJ,KAAK,CAACkJ,IAAI,CAACsC,IAAI,CAAC,CAAC,GAAIxL,KAAK,CAACkJ,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAACtC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACoC,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAACtB,GAAG,IAAIA,GAAG,CAAC,GAAG,CAAC,EAAAyB,eAAA,GAAA7K,KAAK,CAAC0L,QAAQ,cAAAb,eAAA,uBAAdA,eAAA,CAAgBc,WAAW,CAAC,CAAC,KAAI,OAAO,CAAC;cACtJtD,MAAM,EAAE;gBACNuD,IAAI,EAAE5L,KAAK,CAAC6L,UAAU,IAAI,eAAe;gBACzCvD,MAAM,EAAE,gCAAgC,IAAItI,KAAK,CAACyB,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC9D8G,QAAQ,EAAE;cACZ,CAAC;cACD7H,KAAK,EAAE;gBACLF,KAAK,EAAEsL,QAAQ,CAAC9L,KAAK,CAACQ,KAAK,CAAC,IAAI,CAAC;gBACjCmI,QAAQ,EAAEmD,QAAQ,CAAC9L,KAAK,CAAC+L,cAAc,CAAC,IAAI,CAAC;gBAC7C5H,MAAM,EAAE2H,QAAQ,CAAC9L,KAAK,CAACmE,MAAM,CAAC,IAAI,CAAC;gBACnClC,KAAK,EAAE6J,QAAQ,CAAC9L,KAAK,CAACiC,KAAK,CAAC,IAAI;cAClC,CAAC;cACD2G,UAAU,EAAE5I,KAAK,CAACgM,UAAU,IAAI,IAAIhH,IAAI,CAAC,CAAC,CAACiH,WAAW,CAAC,CAAC;cACxDpD,QAAQ,EAAE7I,KAAK,CAAC6I,QAAQ,IAAI,OAAO;cACnC6C,QAAQ,EAAE1L,KAAK,CAAC0L,QAAQ,IAAI,OAAO;cACnCX,SAAS,EAAEA;YACb,CAAC;YACDpJ,OAAO,CAACK,GAAG,CAAC,yBAAyB,EAAEqJ,cAAc,CAAC;YACtD,OAAOA,cAAc;UACvB,CAAC,CAAC,CACDX,MAAM,CAAC1K,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;;UAEpC2B,OAAO,CAACK,GAAG,CAAC,mBAAmB,EAAE2I,QAAQ,CAAC;;UAE1C;UACA,IAAIA,QAAQ,CAAChI,MAAM,GAAG,CAAC,EAAE;YACvBhB,OAAO,CAACK,GAAG,CAAC,8BAA8B,EAAE2I,QAAQ,CAAChI,MAAM,EAAE,QAAQ,CAAC;YACtEmH,SAAS,CAACa,QAAQ,CAAC;YACnBf,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;YACzBjI,OAAO,CAACK,GAAG,CAAC,qCAAqC,CAAC;UACpD,CAAC,MAAM;YACLL,OAAO,CAACK,GAAG,CAAC,mDAAmD,CAAC;YAChE8H,SAAS,CAAC,EAAE,CAAC;UACf;QACF,CAAC,MAAM;UACLnI,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEP,IAAI,CAAC;UACnD2I,SAAS,CAAC,EAAE,CAAC;QACf;MACF,CAAC,CAAC,OAAOpI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACAoI,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDI,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAIN;EACAzL,SAAS,CAAC,MAAM;IACd,IAAIkL,MAAM,CAAClH,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;IAEjC,IAAIuJ,MAAM,GAAG,CAAC;IACd,IAAIC,WAAW,GAAG,KAAK;IAEvB,MAAMC,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIF,WAAW,EAAE;MACjBA,WAAW,GAAG,IAAI;MAElB,IAAIE,CAAC,CAACC,MAAM,GAAG,CAAC,IAAI3C,iBAAiB,GAAGE,MAAM,CAAClH,MAAM,GAAG,CAAC,EAAE;QACzDiH,oBAAoB,CAAC7F,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIsI,CAAC,CAACC,MAAM,GAAG,CAAC,IAAI3C,iBAAiB,GAAG,CAAC,EAAE;QAChDC,oBAAoB,CAAC7F,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC;MAEA5B,UAAU,CAAC,MAAM;QAAEgK,WAAW,GAAG,KAAK;MAAE,CAAC,EAAE,GAAG,CAAC;IACjD,CAAC;IAED,MAAMI,gBAAgB,GAAIF,CAAC,IAAK;MAC9BH,MAAM,GAAGG,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC/B,CAAC;IAED,MAAMC,cAAc,GAAIL,CAAC,IAAK;MAC5B,IAAIF,WAAW,EAAE;MACjB,MAAMQ,IAAI,GAAGN,CAAC,CAACO,cAAc,CAAC,CAAC,CAAC,CAACH,OAAO;MACxC,MAAMI,KAAK,GAAGX,MAAM,GAAGS,IAAI;MAE3B,IAAIxH,IAAI,CAACC,GAAG,CAACyH,KAAK,CAAC,GAAG,EAAE,EAAE;QAAE;QAC1BV,WAAW,GAAG,IAAI;QAElB,IAAIU,KAAK,GAAG,CAAC,IAAIlD,iBAAiB,GAAGE,MAAM,CAAClH,MAAM,GAAG,CAAC,EAAE;UACtD;UACAiH,oBAAoB,CAAC7F,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAI8I,KAAK,GAAG,CAAC,IAAIlD,iBAAiB,GAAG,CAAC,EAAE;UAC7C;UACAC,oBAAoB,CAAC7F,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC;QAEA5B,UAAU,CAAC,MAAM;UAAEgK,WAAW,GAAG,KAAK;QAAE,CAAC,EAAE,GAAG,CAAC;MACjD;IACF,CAAC;IAEDW,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEX,YAAY,CAAC;IAC9CU,MAAM,CAACC,gBAAgB,CAAC,YAAY,EAAER,gBAAgB,CAAC;IACvDO,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAEL,cAAc,CAAC;IAEnD,OAAO,MAAM;MACXI,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEZ,YAAY,CAAC;MACjDU,MAAM,CAACE,mBAAmB,CAAC,YAAY,EAAET,gBAAgB,CAAC;MAC1DO,MAAM,CAACE,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAAC/C,iBAAiB,EAAEE,MAAM,CAAClH,MAAM,CAAC,CAAC;;EAEtC;EACA,MAAMsK,YAAY,GAAGA,CAAA,KAAM;IACzBjD,UAAU,CAAC,IAAI,CAAC;IAChB;IACA8C,MAAM,CAACI,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtBxN,OAAA,CAAChB,GAAG;IAAC4G,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfE,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBsH,UAAU,EAAE,mDAAmD;MAC/DrH,KAAK,EAAE,OAAO;MACdsH,OAAO,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC;MACzBvH,SAAS,EAAE,QAAQ;MACnBU,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAE;IAAAT,QAAA,gBAEAvG,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPmB,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPoB,KAAK,EAAE,CAAC;QACRD,MAAM,EAAE,CAAC;QACToF,UAAU,EAAE,oKAAoK;QAChLI,SAAS,EAAE;MACb;IAAE;MAAAnH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGL7G,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QAAEmB,QAAQ,EAAE,UAAU;QAAEI,MAAM,EAAE;MAAE,CAAE;MAAAZ,QAAA,gBAE3CvG,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UACPC,KAAK,EAAE;YAAE8H,EAAE,EAAE,GAAG;YAAEC,EAAE,EAAE;UAAI,CAAC;UAC3B9H,MAAM,EAAE;YAAE6H,EAAE,EAAE,GAAG;YAAEC,EAAE,EAAE;UAAI,CAAC;UAC5BxG,YAAY,EAAE,KAAK;UACnBqG,UAAU,EAAE,8EAA8E;UAC1FK,cAAc,EAAE,YAAY;UAC5BrG,MAAM,EAAE,iCAAiC;UACzCzB,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBM,EAAE,EAAE,CAAC;UACLsH,EAAE,EAAE,MAAM;UACVC,SAAS,EAAE;QACb,CAAE;QAAAzH,QAAA,eACAvG,OAAA,CAACH,aAAa;UAAC+F,EAAE,EAAE;YACjBY,QAAQ,EAAE;cAAEmH,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG,CAAC;YAC5B9G,OAAO,EAAE,GAAG;YACZgE,MAAM,EAAE;UACV;QAAE;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGN7G,OAAA,CAACf,UAAU;QACTsJ,OAAO,EAAC,IAAI;QACZ3C,EAAE,EAAE;UACFa,EAAE,EAAE,CAAC;UACLoC,UAAU,EAAE,MAAM;UAClBrC,QAAQ,EAAE;YAAEmH,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAS,CAAC;UACxCH,UAAU,EAAE,+CAA+C;UAC3DQ,cAAc,EAAE,MAAM;UACtBC,oBAAoB,EAAE,MAAM;UAC5BC,mBAAmB,EAAE,aAAa;UAClCC,UAAU,EAAE;QACd,CAAE;QAAA7H,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb7G,OAAA,CAACf,UAAU;QACTsJ,OAAO,EAAC,OAAO;QACf3C,EAAE,EAAE;UACFkB,OAAO,EAAE,GAAG;UACZuH,QAAQ,EAAE;YAAEV,EAAE,EAAE,GAAG;YAAEC,EAAE,EAAE;UAAI,CAAC;UAC9BnH,EAAE,EAAE,CAAC;UACLyC,UAAU,EAAE,GAAG;UACf1C,QAAQ,EAAE;YAAEmH,EAAE,EAAE,SAAS;YAAEC,EAAE,EAAE;UAAS,CAAC;UACzCQ,UAAU,EAAE;QACd,CAAE;QAAA7H,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UAAEgD,EAAE,EAAE;QAAE,CAAE;QAAArC,QAAA,gBACjBvG,OAAA,CAACd,UAAU;UACT0I,OAAO,EAAEyF,YAAa;UACtBzH,EAAE,EAAE;YACFQ,KAAK,EAAE,OAAO;YACdL,OAAO,EAAE,0BAA0B;YACnC+H,cAAc,EAAE,YAAY;YAC5BrG,MAAM,EAAE,oCAAoC;YAC5C,SAAS,EAAE;cACT1B,OAAO,EAAE,0BAA0B;cACnCuI,SAAS,EAAE;YACb,CAAC;YACDC,UAAU,EAAE,eAAe;YAC3B9H,EAAE,EAAE,CAAC;YACLZ,KAAK,EAAE;cAAE8H,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG,CAAC;YACzB9H,MAAM,EAAE;cAAE6H,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG;UAC3B,CAAE;UAAArH,QAAA,eAEFvG,OAAA;YAAGwO,SAAS,EAAC,gBAAgB;YAAChH,KAAK,EAAE;cAAEhB,QAAQ,EAAE;YAAG;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eAEb7G,OAAA,CAACf,UAAU;UACTsJ,OAAO,EAAC,SAAS;UACjB3C,EAAE,EAAE;YACFkB,OAAO,EAAE,GAAG;YACZd,OAAO,EAAE,OAAO;YAChBQ,QAAQ,EAAE;cAAEmH,EAAE,EAAE,SAAS;cAAEC,EAAE,EAAE;YAAU;UAC3C,CAAE;UAAArH,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UACPI,OAAO,EAAE,MAAM;UACfwC,GAAG,EAAE;YAAEmF,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAC;UACrBhF,EAAE,EAAE,CAAC;UACL9B,OAAO,EAAE;QACX,CAAE;QAAAP,QAAA,gBAEAvG,OAAA,CAAChB,GAAG;UAAC4G,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAK,QAAA,gBAC1EvG,OAAA,CAACd,UAAU;YACTuP,QAAQ;YACR7I,EAAE,EAAE;cACFQ,KAAK,EAAE,OAAO;cACdL,OAAO,EAAE,0BAA0B;cACnC,gBAAgB,EAAE;gBAAEe,OAAO,EAAE;cAAI,CAAC;cAClCjB,KAAK,EAAE;gBAAE8H,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cACzB9H,MAAM,EAAE;gBAAE6H,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG;YAC3B,CAAE;YAAArH,QAAA,eAEFvG,OAAA,CAACR,kBAAkB;cAACoG,EAAE,EAAE;gBAAEY,QAAQ,EAAE;kBAAEmH,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAAE;YAAE;cAAAlH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACb7G,OAAA,CAACf,UAAU;YAAC2G,EAAE,EAAE;cAAEQ,KAAK,EAAE,OAAO;cAAEI,QAAQ,EAAE;gBAAEmH,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cAAEhF,EAAE,EAAE,GAAG;cAAE9B,OAAO,EAAE;YAAI,CAAE;YAAAP,QAAA,EAAC;UAEzF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGN7G,OAAA,CAAChB,GAAG;UAAC4G,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAK,QAAA,gBAC1EvG,OAAA,CAACd,UAAU;YACTuP,QAAQ;YACR7I,EAAE,EAAE;cACFQ,KAAK,EAAE,OAAO;cACdL,OAAO,EAAE,0BAA0B;cACnC,gBAAgB,EAAE;gBAAEe,OAAO,EAAE;cAAI,CAAC;cAClCjB,KAAK,EAAE;gBAAE8H,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cACzB9H,MAAM,EAAE;gBAAE6H,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG;YAC3B,CAAE;YAAArH,QAAA,eAEFvG,OAAA,CAACP,qBAAqB;cAACmG,EAAE,EAAE;gBAAEY,QAAQ,EAAE;kBAAEmH,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAAE;YAAE;cAAAlH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACb7G,OAAA,CAACf,UAAU;YAAC2G,EAAE,EAAE;cAAEQ,KAAK,EAAE,OAAO;cAAEI,QAAQ,EAAE;gBAAEmH,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cAAEhF,EAAE,EAAE,GAAG;cAAE9B,OAAO,EAAE;YAAI,CAAE;YAAAP,QAAA,EAAC;UAEzF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGN7G,OAAA,CAAChB,GAAG;UAAC4G,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,aAAa,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAK,QAAA,gBAC1EvG,OAAA,CAACd,UAAU;YACTuP,QAAQ;YACR7I,EAAE,EAAE;cACFQ,KAAK,EAAE,OAAO;cACdL,OAAO,EAAE,0BAA0B;cACnC,gBAAgB,EAAE;gBAAEe,OAAO,EAAE;cAAI,CAAC;cAClCjB,KAAK,EAAE;gBAAE8H,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cACzB9H,MAAM,EAAE;gBAAE6H,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG;YAC3B,CAAE;YAAArH,QAAA,eAEFvG,OAAA,CAACJ,SAAS;cAACgG,EAAE,EAAE;gBAAEY,QAAQ,EAAE;kBAAEmH,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAAE;YAAE;cAAAlH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACb7G,OAAA,CAACf,UAAU;YAAC2G,EAAE,EAAE;cAAEQ,KAAK,EAAE,OAAO;cAAEI,QAAQ,EAAE;gBAAEmH,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cAAEhF,EAAE,EAAE,GAAG;cAAE9B,OAAO,EAAE;YAAI,CAAE;YAAAP,QAAA,EAAC;UAEzF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAM6H,iBAAiB,GAAGA,CAAA,kBACxB1O,OAAA,CAAChB,GAAG;IAAC4G,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfE,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBJ,OAAO,EAAE,MAAM;MACfK,KAAK,EAAE;IACT,CAAE;IAAAG,QAAA,gBACAvG,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVsB,YAAY,EAAE,KAAK;QACnBK,MAAM,EAAE,iCAAiC;QACzCkH,SAAS,EAAE,iBAAiB;QAC5Bd,SAAS,EAAE,yBAAyB;QACpCpH,EAAE,EAAE,CAAC;QACL,iBAAiB,EAAE;UACjB,IAAI,EAAE;YAAE6H,SAAS,EAAE;UAAe,CAAC;UACnC,MAAM,EAAE;YAAEA,SAAS,EAAE;UAAiB;QACxC;MACF;IAAE;MAAA5H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL7G,OAAA,CAACf,UAAU;MAACsJ,OAAO,EAAC,OAAO;MAAAhC,QAAA,EAAC;IAE5B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACN;EAED9E,OAAO,CAACK,GAAG,CAAC,6BAA6B,EAAE+H,OAAO,EAAE,eAAe,EAAEF,MAAM,CAAClH,MAAM,CAAC;EAEnF,oBACE/C,OAAA,CAAChB,GAAG;IAAC4G,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfkB,QAAQ,EAAE,QAAQ;MAClBD,QAAQ,EAAE,UAAU;MACpBhB,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAE;IAAAI,QAAA,gBAEAvG,OAAA,CAAChB,GAAG;MAAC4G,EAAE,EAAE;QACPmB,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPoB,KAAK,EAAE,CAAC;QACRlC,KAAK,EAAE,OAAO;QACdI,QAAQ,EAAE,EAAE;QACZW,MAAM,EAAE,GAAG;QACXpB,OAAO,EAAE,mBAAmB;QAC5BO,CAAC,EAAE,CAAC;QACJD,SAAS,EAAE;MACb,CAAE;MAAAE,QAAA,GAAC,WACQ,EAAC4D,OAAO,GAAG,KAAK,GAAG,IAAI,EAAC,aAAW,EAACF,MAAM,CAAClH,MAAM,EAAC,cAAY,EAACgH,iBAAiB;IAAA;MAAArD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC,EAELsD,OAAO,gBACNnK,OAAA,CAAC0O,iBAAiB;MAAAhI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACnBoD,MAAM,IAAIA,MAAM,CAAClH,MAAM,GAAG,CAAC,gBAC7B/C,OAAA,CAAAE,SAAA;MAAAqG,QAAA,gBACEvG,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UACPmB,QAAQ,EAAE,UAAU;UACpBE,GAAG,EAAE,EAAE;UACPC,IAAI,EAAE,EAAE;UACRd,KAAK,EAAE,QAAQ;UACfI,QAAQ,EAAE,EAAE;UACZW,MAAM,EAAE,GAAG;UACXpB,OAAO,EAAE,iBAAiB;UAC1BO,CAAC,EAAE;QACL,CAAE;QAAAC,QAAA,GAAC,YACS,EAAC0D,MAAM,CAAClH,MAAM,EAAC,oBAAkB,EAACgH,iBAAiB;MAAA;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,EAELoD,MAAM,CAACV,GAAG,CAAC,CAACnJ,KAAK,EAAEqJ,KAAK,KAAK;QAC5B1H,OAAO,CAACK,GAAG,CAAC,mBAAmBqH,KAAK,GAAG,EAAErJ,KAAK,CAAC2D,KAAK,EAAE,WAAW,EAAE0F,KAAK,KAAKM,iBAAiB,CAAC;QAC/F,oBACE/J,OAAA,CAAChB,GAAG;UAEF4G,EAAE,EAAE;YACFmB,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPrB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdE,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBmI,SAAS,EAAE,cAAc,CAAC7E,KAAK,GAAGM,iBAAiB,IAAI,GAAG,IAAI;YAC9DwE,UAAU,EAAE,4BAA4B;YACxCpH,MAAM,EAAEsC,KAAK,KAAKM,iBAAiB,GAAG,EAAE,GAAG;UAC7C,CAAE;UAAAxD,QAAA,eAEFvG,OAAA,CAACG,SAAS;YACRC,KAAK,EAAEA,KAAM;YACbC,QAAQ,EAAEoJ,KAAK,KAAKM;UAAkB;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC,GAlBGzG,KAAK,CAACyB,EAAE;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBV,CAAC;MAEV,CAAC,CAAC,eAGF7G,OAAA,CAAChB,GAAG;QAAC4G,EAAE,EAAE;UACPmB,QAAQ,EAAE,UAAU;UACpBE,GAAG,EAAE,EAAE;UACPqB,KAAK,EAAE,EAAE;UACTvC,OAAO,EAAE,oBAAoB;UAC7BqB,YAAY,EAAE,CAAC;UACfwH,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACL1H,MAAM,EAAE;QACV,CAAE;QAAAZ,QAAA,eACAvG,OAAA,CAACf,UAAU;UAAC2G,EAAE,EAAE;YAAEQ,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE;UAAG,CAAE;UAAAD,QAAA,GAC9CwD,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACE,MAAM,CAAClH,MAAM;QAAA;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACN,CAAC,gBAEH7G,OAAA,CAACwN,eAAe;MAAA9G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACnB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACiD,GAAA,CA5gBuBD,SAAS;AAAAiF,GAAA,GAATjF,SAAS;AAAA,IAAAD,EAAA,EAAAkF,GAAA;AAAAC,YAAA,CAAAnF,EAAA;AAAAmF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}