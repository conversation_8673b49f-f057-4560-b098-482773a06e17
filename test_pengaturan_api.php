<?php
require_once 'frontend/src/pages/admin/connect.php';

echo "<h2>Test Pengaturan API</h2>";

try {
    $pdo = getConnection();
    
    // Test 1: Check if table exists
    echo "<h3>1. Check Table Pengaturan</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'pengaturan'");
    $stmt->execute();
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "✅ Table 'pengaturan' exists<br>";
        
        // Test 2: Check table structure
        echo "<h3>2. Table Structure</h3>";
        $stmt = $pdo->prepare("DESCRIBE pengaturan");
        $stmt->execute();
        $columns = $stmt->fetchAll();
        
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test 3: Check data
        echo "<h3>3. Data in Table</h3>";
        $stmt = $pdo->prepare("SELECT * FROM pengaturan");
        $stmt->execute();
        $data = $stmt->fetchAll();
        
        if ($data) {
            echo "<pre>" . print_r($data, true) . "</pre>";
        } else {
            echo "❌ No data found in pengaturan table<br>";
            
            // Insert default data
            echo "<h3>4. Inserting Default Data</h3>";
            $stmt = $pdo->prepare("INSERT INTO pengaturan (nama_website, deskripsi_website, logo_file_path) VALUES (?, ?, ?)");
            $result = $stmt->execute(['React News Portal', 'Portal berita terkini dan terpercaya', 'uploads/default-logo.png']);
            
            if ($result) {
                echo "✅ Default data inserted<br>";
            } else {
                echo "❌ Failed to insert default data<br>";
            }
        }
        
    } else {
        echo "❌ Table 'pengaturan' does not exist<br>";
        echo "Please run create_pengaturan_table.sql first<br>";
    }
    
    // Test 4: Test API call simulation
    echo "<h3>5. Test API Call Simulation</h3>";
    $_GET['action'] = 'get_pengaturan';
    
    ob_start();
    include 'frontend/src/pages/admin/api.php';
    $output = ob_get_clean();
    
    echo "API Response:<br>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
