<?php
require_once 'frontend/src/pages/admin/connect.php';

try {
    $pdo = getConnection();
    
    // Update logo path to correct location
    $stmt = $pdo->prepare("UPDATE pengaturan SET logo_file_path = ? WHERE id = 1");
    $result = $stmt->execute(['uploads/default-logo.png']);
    
    if ($result) {
        echo "✅ Logo path updated to uploads/default-logo.png\n";
    } else {
        echo "❌ Failed to update logo path\n";
    }
    
    // Verify the update
    $stmt = $pdo->prepare("SELECT logo_file_path FROM pengaturan WHERE id = 1");
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "📋 Current logo path: " . $result['logo_file_path'] . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
