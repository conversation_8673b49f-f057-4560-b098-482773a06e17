<?php
// Video Player - Clean implementation from scratch
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once __DIR__ . '/../admin/config.php';
} catch (Exception $e) {
    die("Config error: " . $e->getMessage());
}

// Get database connection
try {
    $pdo = getConnection();
} catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
}

// Get videos from database
try {
    $stmt = $pdo->prepare("
        SELECT id, title, description, content, youtube_url, youtube_id,
               video_base64, video_type, video_size, video_format,
               thumbnail, category, tags, duration, status, views, likes,
               shares, comments_count, featured, created_at, updated_at
        FROM videos
        WHERE status = 'published'
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($videos)) {
        // Create sample data if no videos exist
        $videos = [[
            'id' => 1,
            'title' => 'Sample YouTube Video',
            'description' => 'This is a sample video for testing',
            'content' => 'Sample content',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'video_base64' => null,
            'video_type' => 'youtube',
            'video_size' => null,
            'video_format' => null,
            'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
            'category' => 'Sample',
            'tags' => 'sample,test',
            'duration' => '03:32',
            'status' => 'published',
            'views' => 100,
            'likes' => 10,
            'shares' => 5,
            'comments_count' => 2,
            'featured' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]];
    }
} catch (Exception $e) {
    die("Database query error: " . $e->getMessage());
}

// Helper functions
function extractYouTubeId($url) {
    if (!$url) return null;
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : null;
}

function getYouTubeEmbedUrl($videoId) {
    $params = http_build_query([
        'autoplay' => '0',
        'mute' => '0',
        'controls' => '1',
        'rel' => '0',
        'modestbranding' => '1',
        'enablejsapi' => '1'
    ]);
    return "https://www.youtube.com/embed/{$videoId}?{$params}";
}

function getVideoSource($video) {
    $video_type = $video['video_type'] ?? 'youtube';

    if ($video_type === 'upload' && !empty($video['video_base64'])) {
        $video_format = $video['video_format'] ?? 'mp4';
        $mimeType = 'video/' . $video_format;
        $dataUrl = 'data:' . $mimeType . ';base64,' . $video['video_base64'];

        return [
            'type' => 'upload',
            'src' => $dataUrl,
            'format' => $video_format,
            'size' => $video['video_size'] ?? 0
        ];
    } else {
        $youtubeId = $video['youtube_id'] ?: extractYouTubeId($video['youtube_url']);
        return [
            'type' => 'youtube',
            'src' => getYouTubeEmbedUrl($youtubeId),
            'youtube_id' => $youtubeId
        ];
    }
}

function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB', 'TB');
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

// Check if we have videos
if (empty($videos)) {
    die("No videos available");
}

// Prepare all videos with their sources
$videosWithSources = [];
foreach ($videos as $video) {
    $videosWithSources[] = [
        'video' => $video,
        'source' => getVideoSource($video)
    ];
}

// Get first video for initial display
$currentVideo = $videos[0];
$videoSource = getVideoSource($currentVideo);
$video_type = $videoSource['type'];
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($currentVideo['title']) ?> - Video Player</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjM0I4MkY2Ij4KICA8cGF0aCBkPSJNOCAydjIwbDEzLTEwTDh6Ii8+Cjwvc3ZnPg==">
    <link rel="shortcut icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjM0I4MkY2Ij4KICA8cGF0aCBkPSJNOCAydjIwbDEzLTEwTDh6Ii8+Cjwvc3ZnPg==">

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Video Container */
        .video-container {
            position: relative;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            width: 100%;
        }

        /* Hide HTML5 video controls but keep functionality */
        .video-container video::-webkit-media-controls {
            display: none !important;
        }

        .video-container video::-webkit-media-controls-enclosure {
            display: none !important;
        }

        .video-container video::-webkit-media-controls-panel {
            display: none !important;
        }

        .video-container video::-moz-media-controls {
            display: none !important;
        }

        .video-container video::--ms-media-controls {
            display: none !important;
        }

        /* Remove controls attribute styling */
        .video-container video[controls] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }
        
        /* Play Button - ukuran sedang */
        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            transition: all 0.3s ease;
        }
        
        .play-button button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .play-button button:hover {
            background: rgba(0, 0, 0, 0.9);
            border-color: white;
            transform: scale(1.1);
        }
        
        .play-button i {
            font-size: 18px;
            margin-left: 2px;
        }
        
        /* Video States */
        .video-playing .play-button {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            transition: all 0.3s ease;
        }

        .video-paused .play-button {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
            transition: all 0.3s ease;
        }

        /* Show play button on hover when video is playing */
        .video-container:hover .play-button {
            opacity: 0.8;
            visibility: visible;
            pointer-events: auto;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .video-container {
                border-radius: 8px;
                height: 100vh;
                width: 100vw;
                position: relative;
            }

            .video-container video {
                width: 100% !important;
                height: 100% !important;
                object-fit: contain;
                background: #000;
                cursor: pointer;
                -webkit-tap-highlight-color: transparent;
                -webkit-playsinline: true;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }

            .play-button button {
                width: 50px;
                height: 50px;
                z-index: 20;
            }

            .play-button i {
                font-size: 16px;
            }

            /* Ensure mobile video is clickable */
            .video-container.video-paused video {
                pointer-events: auto;
            }

            .video-container.video-playing video {
                pointer-events: auto;
            }

            /* Mobile action buttons - same colors as desktop */
            .action-btn.liked {
                background: rgba(59, 130, 246, 0.8) !important;
                border-color: rgba(59, 130, 246, 1) !important;
                color: white !important;
            }

            .action-btn.saved {
                background: rgba(251, 191, 36, 0.8) !important;
                border-color: rgba(251, 191, 36, 1) !important;
                color: white !important;
            }

            .action-btn.commented {
                background: rgba(34, 197, 94, 0.8) !important;
                border-color: rgba(34, 197, 94, 1) !important;
                color: white !important;
            }

            /* Mobile specific hover effects */
            @media (max-width: 768px) {
                .action-btn.liked:hover {
                    background: rgba(59, 130, 246, 0.9) !important;
                    transform: scale(1.05);
                }

                .action-btn.saved:hover {
                    background: rgba(251, 191, 36, 0.9) !important;
                    transform: scale(1.05);
                }

                .action-btn.commented:hover {
                    background: rgba(34, 197, 94, 0.9) !important;
                    transform: scale(1.05);
                }

                .action-btn:hover {
                    transform: scale(1.05);
                    transition: all 0.2s ease;
                }
            }
        }
        
        @media (min-width: 769px) {
            .video-container {
                max-width: 800px;
                margin: 0 auto;
                aspect-ratio: 16/9;
            }
        }
        
        /* Loading Spinner */
        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Action Buttons */
        .action-btn {
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .action-btn:hover {
            background: rgba(0, 0, 0, 0.8);
            border-color: rgba(255, 255, 255, 0.6);
            transform: scale(1.05);
        }
        
        .action-btn.liked {
            background: rgba(59, 130, 246, 0.8);
            border-color: rgba(59, 130, 246, 1);
            color: white;
        }

        .action-btn.saved {
            background: rgba(251, 191, 36, 0.8);
            border-color: rgba(251, 191, 36, 1);
            color: white;
        }

        .action-btn.commented {
            background: rgba(34, 197, 94, 0.8);
            border-color: rgba(34, 197, 94, 1);
            color: white;
        }

        /* Badge styles for action buttons */
        .action-btn {
            position: relative;
        }

        .action-btn .badge {
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
            font-weight: 500;
            line-height: 1.2;
            z-index: 10;
        }

        .action-btn.liked .badge {
            background: rgba(59, 130, 246, 0.9) !important;
            color: white !important;
        }

        .action-btn.saved .badge {
            background: rgba(251, 191, 36, 0.9) !important;
            color: white !important;
        }

        .action-btn.commented .badge {
            background: rgba(34, 197, 94, 0.9) !important;
            color: white !important;
        }

        /* Mobile badge colors */
        @media (max-width: 768px) {
            .action-btn.liked .badge {
                background: rgba(59, 130, 246, 0.9) !important;
                color: white !important;
            }

            .action-btn.saved .badge {
                background: rgba(251, 191, 36, 0.9) !important;
                color: white !important;
            }

            .action-btn.commented .badge {
                background: rgba(34, 197, 94, 0.9) !important;
                color: white !important;
            }
        }

        /* Hide badge when count is 0 */
        .action-btn .badge[data-count="0"] {
            display: none;
        }

        /* Comment Modal */
        .comment-modal {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-radius: 20px 20px 0 0;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
            max-height: 70vh;
            overflow: hidden;
        }

        .comment-modal.show {
            transform: translateY(0);
        }

        .comment-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .comment-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .comment-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
        }

        .comment-content {
            max-height: 50vh;
            overflow-y: auto;
            padding: 16px 20px;
        }

        .comment-item {
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .comment-item:last-child {
            border-bottom: none;
        }

        .comment-author {
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .comment-text {
            color: #4b5563;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .comment-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: #6b7280;
        }

        .comment-input-section {
            padding: 16px 20px;
            border-top: 1px solid #e5e7eb;
            background: white;
        }

        .comment-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            resize: none;
            color: #000000 !important;
            background-color: #ffffff;
        }

        .comment-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .comment-input::placeholder {
            color: #6b7280 !important;
            opacity: 1;
        }

        .comment-submit {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 8px;
        }

        .comment-submit:hover {
            background: #2563eb;
        }

        .comment-submit:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        /* Custom video progress bar */
        .video-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            z-index: 15;
        }

        .video-progress-bar {
            height: 100%;
            background: #3b82f6;
            width: 0%;
            transition: width 0.1s ease;
        }

        .video-progress:hover {
            height: 8px;
        }

        .video-progress:hover .video-progress-bar {
            background: #2563eb;
        }

        /* TikTok-style scroll container */
        .video-scroll-container {
            height: 100vh;
            overflow-y: auto;
            scroll-snap-type: y mandatory;
            -webkit-overflow-scrolling: touch;
        }

        .video-scroll-item {
            height: 100vh;
            scroll-snap-align: start;
            scroll-snap-stop: always;
            position: relative;
        }

        /* Hide scrollbar but keep functionality - Enhanced */
        .video-scroll-container::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
            background: transparent !important;
        }

        .video-scroll-container::-webkit-scrollbar-track {
            display: none !important;
        }

        .video-scroll-container::-webkit-scrollbar-thumb {
            display: none !important;
        }

        .video-scroll-container::-webkit-scrollbar-corner {
            display: none !important;
        }

        .video-scroll-container {
            -ms-overflow-style: none !important;
            scrollbar-width: none !important;
            overflow: -moz-scrollbars-none !important;
        }

        /* Additional scrollbar hiding for all browsers */
        #videoScrollContainer::-webkit-scrollbar,
        #desktopVideoScrollContainer::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
        }

        #videoScrollContainer,
        #desktopVideoScrollContainer {
            -ms-overflow-style: none !important;
            scrollbar-width: none !important;
        }

        /* Global scrollbar hiding */
        * {
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
        }

        *::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
        }


    </style>
</head>
<body class="bg-gray-900 text-white">
    <!-- Navigation Header -->
    <div class="fixed top-0 left-0 right-0 z-50 bg-gray-900 bg-opacity-90 backdrop-blur-sm border-b border-gray-700">
        <div class="flex items-center justify-between p-4">
            <button onclick="goToLandingPage()" class="flex items-center space-x-2 text-white hover:text-gray-300 transition-colors cursor-pointer p-2 rounded-lg hover:bg-gray-800 active:bg-gray-700" title="Kembali ke Beranda">
                <i class="fas fa-arrow-left text-lg"></i>
                <span class="hidden sm:inline font-medium">Kembali</span>
            </button>
            <h1 class="text-lg font-semibold truncate mx-4" id="navbarTitle"><?= htmlspecialchars($currentVideo['title']) ?></h1>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-400" id="navbarVideoCount"><?= count($videos) ?> video</span>
            </div>
        </div>
    </div>

    <div class="min-h-screen pt-16">
        <!-- Desktop Layout - TikTok Style Scroll -->
        <div class="hidden md:block video-scroll-container" id="desktopVideoScrollContainer">
            <?php foreach ($videosWithSources as $index => $videoData): ?>
                <?php
                $video = $videoData['video'];
                $source = $videoData['source'];
                $vType = $source['type'];
                ?>
                <div class="video-scroll-item flex justify-center items-center p-4"
                     data-video-id="<?= $video['id'] ?>"
                     data-video-type="<?= $vType ?>"
                     data-video-index="<?= $index ?>">

                    <div class="flex flex-col lg:flex-row gap-6 max-w-6xl w-full">
                        <!-- Video Container -->
                        <div class="flex-1">
                            <div class="video-container video-paused" data-video-id="<?= $video['id'] ?>" data-video-type="<?= $vType ?>">
                                <?php if ($vType === 'upload'): ?>
                                    <!-- HTML5 Video Player -->
                                    <video class="w-full h-full object-contain bg-black"
                                           preload="metadata"
                                           crossorigin="anonymous"
                                           id="desktop-video-<?= $video['id'] ?>"
                                           onplay="handleVideoPlay(this)"
                                           onclick="handleVideoClick(this)">
                                        <source src="<?= htmlspecialchars($source['src']) ?>" type="video/<?= htmlspecialchars($source['format']) ?>">
                                        Your browser does not support the video tag.
                                    </video>
                                <?php else: ?>
                                    <!-- YouTube Video -->
                                    <iframe class="w-full h-full"
                                            src="<?= htmlspecialchars($source['src']) ?>"
                                            frameborder="0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            allowfullscreen>
                                    </iframe>
                                <?php endif; ?>

                                <!-- Play Button Overlay -->
                                <div class="play-button">
                                    <button type="button">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>

                                <!-- Custom Progress Bar -->
                                <div class="video-progress" onclick="seekVideo(event, <?= $video['id'] ?>)">
                                    <div class="video-progress-bar" id="progress-desktop-<?= $video['id'] ?>"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Info Sidebar -->
                        <div class="w-full lg:w-80 space-y-4">
                            <!-- Title and Description -->
                            <div class="bg-gray-800 rounded-lg p-4">
                                <h1 class="text-xl font-bold mb-3"><?= htmlspecialchars($video['title']) ?></h1>
                                <p class="text-gray-300 text-sm mb-3"><?= htmlspecialchars($video['description']) ?></p>

                                <!-- Stats -->
                                <div class="flex items-center space-x-4 text-sm text-gray-400 mb-4">
                                    <span><i class="fas fa-eye mr-1"></i><span class="view-count"><?= number_format($video['views']) ?></span></span>
                                    <span><i class="fas fa-heart mr-1"></i><?= number_format($video['likes']) ?></span>
                                    <span><i class="fas fa-share mr-1"></i><?= number_format($video['shares']) ?></span>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex space-x-3">
                                    <button class="action-btn like-btn" data-video-id="<?= $video['id'] ?>" title="Like">
                                        <i class="fas fa-thumbs-up"></i>
                                        <span class="badge" data-count="<?= $video['likes'] ?>"><?= number_format($video['likes']) ?></span>
                                    </button>
                                    <button class="action-btn comment-btn" data-video-id="<?= $video['id'] ?>" title="Comment">
                                        <i class="fas fa-comment"></i>
                                        <span class="badge" data-count="<?= $video['comments_count'] ?>"><?= number_format($video['comments_count']) ?></span>
                                    </button>
                                    <button class="action-btn save-btn" data-video-id="<?= $video['id'] ?>" title="Save">
                                        <i class="fas fa-bookmark"></i>
                                        <span class="badge save-count" data-count="0">0</span>
                                    </button>
                                    <button class="action-btn share-btn" data-video-id="<?= $video['id'] ?>" title="Share">
                                        <i class="fas fa-share"></i>
                                        <span class="badge" data-count="<?= $video['shares'] ?>"><?= number_format($video['shares']) ?></span>
                                    </button>
                                </div>
                            </div>

                            <!-- Video Details -->
                            <div class="bg-gray-800 rounded-lg p-4">
                                <h3 class="font-semibold mb-2">Detail Video</h3>
                                <div class="space-y-2 text-sm text-gray-300">
                                    <p><span class="text-gray-400">Kategori:</span> <?= htmlspecialchars($video['category']) ?></p>
                                    <p><span class="text-gray-400">Durasi:</span> <?= htmlspecialchars($video['duration']) ?></p>
                                    <p><span class="text-gray-400">Tanggal:</span> <?= date('d M Y', strtotime($video['created_at'])) ?></p>
                                    <?php if ($vType === 'youtube'): ?>
                                        <p><span class="text-gray-400">Sumber:</span> YouTube</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Mobile Layout - TikTok Style Scroll -->
        <div class="md:hidden video-scroll-container" id="videoScrollContainer">
            <?php foreach ($videosWithSources as $index => $videoData): ?>
                <?php
                $video = $videoData['video'];
                $source = $videoData['source'];
                $vType = $source['type'];
                ?>
                <div class="video-scroll-item video-container video-paused"
                     data-video-id="<?= $video['id'] ?>"
                     data-video-type="<?= $vType ?>"
                     data-video-index="<?= $index ?>">

                    <?php if ($vType === 'upload'): ?>
                        <!-- HTML5 Video Player -->
                        <video class="w-full h-full object-contain bg-black"
                               preload="metadata"
                               playsinline
                               webkit-playsinline
                               muted="false"
                               controls="false"
                               crossorigin="anonymous"
                               id="mobile-video-<?= $video['id'] ?>"
                               data-video-id="<?= $video['id'] ?>"
                               onloadstart="console.log('📱 Mobile video: loadstart')"
                               onloadedmetadata="console.log('📱 Mobile video: metadata loaded')"
                               onloadeddata="console.log('📱 Mobile video: data loaded')"
                               oncanplay="console.log('📱 Mobile video: can play')"
                               oncanplaythrough="console.log('📱 Mobile video: can play through')"
                               onplay="console.log('📱 Mobile video: started playing')"
                               onpause="console.log('📱 Mobile video: paused')"
                               onended="console.log('📱 Mobile video: ended')"
                               onerror="console.error('📱 Mobile video error:', this.error)"
                               onstalled="console.log('📱 Mobile video: stalled')"
                               onwaiting="console.log('📱 Mobile video: waiting')"
                               style="pointer-events: auto; touch-action: manipulation;">
                            <source src="<?= htmlspecialchars($source['src']) ?>" type="video/<?= htmlspecialchars($source['format']) ?>">
                            Your browser does not support the video tag.
                        </video>
                    <?php else: ?>
                        <!-- YouTube Video -->
                        <iframe class="w-full h-full"
                                src="<?= htmlspecialchars($source['src']) ?>"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowfullscreen>
                        </iframe>
                    <?php endif; ?>

                    <!-- Play Button Overlay -->
                    <div class="play-button">
                        <button type="button">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>

                    <!-- Custom Progress Bar -->
                    <div class="video-progress" onclick="seekVideo(event, <?= $video['id'] ?>)">
                        <div class="video-progress-bar" id="progress-mobile-<?= $video['id'] ?>"></div>
                    </div>

                    <!-- Mobile Video Info Overlay -->
                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-4">
                        <h2 class="text-lg font-bold mb-2 line-clamp-2"><?= htmlspecialchars($video['title']) ?></h2>
                        <p class="text-sm opacity-80 mb-2 line-clamp-2"><?= htmlspecialchars($video['description']) ?></p>
                        <div class="flex items-center space-x-4 text-xs opacity-70">
                            <span><i class="fas fa-eye mr-1"></i><?= number_format($video['views']) ?></span>
                            <span><i class="fas fa-calendar mr-1"></i><?= date('d M Y', strtotime($video['created_at'])) ?></span>
                            <?php if ($vType === 'upload'): ?>
                                <span><i class="fas fa-hdd mr-1"></i><?= formatFileSize($source['size']) ?></span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Mobile Action Buttons -->
                    <div class="absolute right-4 bottom-20 flex flex-col space-y-4">
                        <button class="action-btn like-btn" data-video-id="<?= $video['id'] ?>" title="Like">
                            <i class="fas fa-thumbs-up"></i>
                            <span class="badge" data-count="<?= $video['likes'] ?>"><?= number_format($video['likes']) ?></span>
                        </button>
                        <button class="action-btn comment-btn" data-video-id="<?= $video['id'] ?>" title="Comment">
                            <i class="fas fa-comment"></i>
                            <span class="badge" data-count="<?= $video['comments_count'] ?>"><?= number_format($video['comments_count']) ?></span>
                        </button>
                        <button class="action-btn save-btn" data-video-id="<?= $video['id'] ?>" title="Save">
                            <i class="fas fa-bookmark"></i>
                            <span class="badge save-count" data-count="0">0</span>
                        </button>
                        <button class="action-btn share-btn" data-video-id="<?= $video['id'] ?>" title="Share">
                            <i class="fas fa-share"></i>
                            <span class="badge" data-count="<?= $video['shares'] ?>"><?= number_format($video['shares']) ?></span>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>



    </div>

    <!-- Comment Modal -->
    <div class="comment-modal-overlay" id="commentOverlay" onclick="closeCommentModal()"></div>
    <div class="comment-modal" id="commentModal">
        <div class="comment-header">
            <h3 class="text-lg font-semibold text-gray-900">Komentar</h3>
            <button onclick="closeCommentModal()" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div class="comment-content" id="commentContent">
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                <p>Memuat komentar...</p>
            </div>
        </div>

        <div class="comment-input-section">
            <textarea
                class="comment-input"
                id="commentInput"
                placeholder="Tulis komentar Anda..."
                rows="2"
                maxlength="500"></textarea>
            <div class="flex justify-between items-center mt-2">
                <span class="text-xs text-gray-500" id="commentCounter">0/500</span>
                <button
                    class="comment-submit"
                    id="commentSubmit"
                    onclick="submitComment()"
                    disabled>
                    Kirim
                </button>
            </div>
        </div>
    </div>

    <script>
        // Navigation function to go back to Landing Page
        function goToLandingPage() {
            console.log('🏠 Back button clicked - Navigating to Landing Page');
            try {
                // Navigate back to LandingPage.js (React app root)
                window.location.href = 'http://localhost:3000/';
            } catch (error) {
                console.error('❌ Navigation error:', error);
                // Fallback: use browser back
                history.back();
            }
        }

        // Function to truncate title based on device
        function truncateTitle(title, isMobile = false) {
            const maxLength = isMobile ? 15 : 25; // Mobile: 15 chars, Desktop: 25 chars
            if (title.length <= maxLength) {
                return title;
            }
            return title.substring(0, maxLength) + '...';
        }

        // Function to update navbar title
        function updateNavbarTitle(title) {
            const navbarTitle = document.getElementById('navbarTitle');
            if (navbarTitle) {
                const isMobile = window.innerWidth < 768;
                const truncatedTitle = truncateTitle(title, isMobile);
                navbarTitle.textContent = truncatedTitle;
                navbarTitle.title = title; // Full title in tooltip
                console.log(`📱 Title updated: ${truncatedTitle} (Mobile: ${isMobile})`);
            }
        }

        // Scroll detection for both mobile and desktop
        function initScrollDetection() {
            const isMobile = window.innerWidth < 768;
            const scrollContainer = isMobile ?
                document.getElementById('videoScrollContainer') :
                document.getElementById('desktopVideoScrollContainer');

            if (!scrollContainer) return;

            let currentVideoIndex = 0;
            let isScrolling = false;

            function updateCurrentVideo() {
                const scrollItems = scrollContainer.querySelectorAll('.video-scroll-item');
                const containerHeight = scrollContainer.clientHeight;
                const scrollTop = scrollContainer.scrollTop;

                let newIndex = 0;
                let minDistance = Infinity;

                scrollItems.forEach((item, index) => {
                    const itemTop = item.offsetTop;
                    const itemCenter = itemTop + (item.offsetHeight / 2);
                    const containerCenter = scrollTop + (containerHeight / 2);
                    const distance = Math.abs(itemCenter - containerCenter);

                    if (distance < minDistance) {
                        minDistance = distance;
                        newIndex = index;
                    }
                });

                if (newIndex !== currentVideoIndex) {
                    // Pause previous video
                    if (currentVideoIndex >= 0 && currentVideoIndex < scrollItems.length) {
                        const prevItem = scrollItems[currentVideoIndex];
                        const prevVideoContainer = prevItem.querySelector('.video-container');
                        const prevVideo = prevItem.querySelector('video');
                        if (prevVideo && !prevVideo.paused) {
                            prevVideo.pause();
                            prevVideoContainer.classList.remove('video-playing');
                            prevVideoContainer.classList.add('video-paused');
                        }
                    }

                    currentVideoIndex = newIndex;
                    const currentItem = scrollItems[newIndex];
                    if (currentItem) {
                        const videoId = currentItem.getAttribute('data-video-id');
                        const videoIndex = currentItem.getAttribute('data-video-index');

                        // Get video title from the current item
                        const titleElement = currentItem.querySelector('h1');
                        if (titleElement) {
                            const videoTitle = titleElement.textContent;
                            updateNavbarTitle(videoTitle);
                            console.log(`📺 Current video: ${videoTitle} (Index: ${videoIndex})`);
                        }



                        // Auto-play current video after scroll settles
                        setTimeout(() => {
                            if (!isScrolling) {
                                const videoContainer = currentItem.querySelector('.video-container');
                                const video = currentItem.querySelector('video');
                                if (video && videoContainer) {
                                    console.log('🎬 Auto-playing video after scroll:', videoTitle);
                                    // Auto-play the video
                                    video.muted = false;
                                    video.volume = 1.0;
                                    const playPromise = video.play();
                                    if (playPromise !== undefined) {
                                        playPromise.then(() => {
                                            videoContainer.classList.remove('video-paused');
                                            videoContainer.classList.add('video-playing');
                                        }).catch(error => {
                                            console.log('Auto-play blocked, user interaction required');
                                        });
                                    }
                                }
                            }
                        }, 500);
                    }
                }
            }

            // Throttled scroll handler
            let scrollTimeout;
            scrollContainer.addEventListener('scroll', () => {
                isScrolling = true;
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    isScrolling = false;
                    updateCurrentVideo();
                }, 100);
            });

            // Initial update
            updateCurrentVideo();
        }



        // Video Play/Pause Function
        function toggleVideoPlay(videoId) {
            console.log('🎬 Toggle play for video:', videoId);

            const isMobile = window.innerWidth < 768;
            const videoContainer = document.querySelector(`[data-video-id="${videoId}"]`);

            if (!videoContainer) {
                console.error('❌ Video container not found');
                return;
            }

            const playButton = videoContainer.querySelector('.play-button');
            const videoType = videoContainer.getAttribute('data-video-type');
            const iframe = videoContainer.querySelector('iframe');
            const htmlVideo = videoContainer.querySelector('video');

            console.log('🎥 Video type:', videoType);
            console.log('📹 HTML Video:', htmlVideo);
            console.log('🎞️ Iframe:', iframe);

            // For HTML5 video, check actual video state instead of container class
            if (videoType === 'upload' && htmlVideo) {
                const isPlaying = !htmlVideo.paused && !htmlVideo.ended && htmlVideo.readyState > 2;
                console.log('📹 Video is playing:', isPlaying);

                if (isPlaying) {
                    // Pause video
                    htmlVideo.pause();
                    videoContainer.classList.remove('video-playing');
                    videoContainer.classList.add('video-paused');
                    console.log('⏸️ Video paused via overlay button');
                    return;
                } else {
                    // Play video
                    playVideoWithSound(htmlVideo, videoContainer, isMobile);
                    return;
                }
            }

            if (videoContainer.classList.contains('video-paused')) {
                // Play video
                videoContainer.classList.remove('video-paused');
                videoContainer.classList.add('video-playing');

                if (videoType === 'upload' && htmlVideo) {
                    // HTML5 Video
                    console.log('▶️ Playing HTML5 video');

                    // Special handling for mobile
                    if (isMobile) {
                        console.log('📱 Mobile video play handling');
                        handleMobileVideoClick(htmlVideo);
                        return; // Use mobile-specific handler
                    }

                    // Desktop video handling
                    if (htmlVideo.readyState < 2) {
                        htmlVideo.load();
                    }

                    // Ensure video has sound
                    htmlVideo.muted = false;
                    htmlVideo.volume = 1.0;

                    const playPromise = htmlVideo.play();
                    if (playPromise !== undefined) {
                        playPromise.then(() => {
                            console.log('✅ Desktop video playing with sound');
                        }).catch(error => {
                            console.error('❌ Desktop play error:', error);
                            // Try with muted first if autoplay is blocked
                            if (error.name === 'NotAllowedError') {
                                console.log('🔇 Trying muted play for desktop...');
                                htmlVideo.muted = true;
                                htmlVideo.play().then(() => {
                                    showUnmuteInstruction(htmlVideo);
                                });
                            }
                            // Reset to paused state on error
                            videoContainer.classList.remove('video-playing');
                            videoContainer.classList.add('video-paused');
                        });
                    }
                } else if (iframe) {
                    // YouTube Video
                    console.log('▶️ Playing YouTube video');
                    try {
                        iframe.contentWindow.postMessage('{"event":"command","func":"playVideo","args":""}', '*');
                    } catch (e) {
                        console.log('⚠️ YouTube API not available');
                    }
                }
            } else {
                // Pause video
                videoContainer.classList.remove('video-playing');
                videoContainer.classList.add('video-paused');

                if (videoType === 'upload' && htmlVideo) {
                    // Special handling for mobile pause
                    if (isMobile) {
                        console.log('📱 Pausing mobile video');
                        htmlVideo.pause();
                        // Ensure play button shows
                        const playButton = videoContainer.querySelector('.play-button');
                        if (playButton) {
                            playButton.style.opacity = '1';
                            playButton.style.visibility = 'visible';
                            playButton.style.pointerEvents = 'auto';
                        }
                    } else {
                        htmlVideo.pause();
                        console.log('⏸️ Desktop video paused');
                    }
                } else if (iframe) {
                    try {
                        iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
                        console.log('⏸️ YouTube video paused');
                    } catch (e) {
                        console.log('⚠️ YouTube API not available');
                    }
                }
            }
        }

        // Play video with sound function
        function playVideoWithSound(htmlVideo, videoContainer, isMobile) {
            console.log('▶️ Resuming video from current position');

            videoContainer.classList.remove('video-paused');
            videoContainer.classList.add('video-playing');

            // Special handling for mobile
            if (isMobile) {
                // Don't reload video, just resume
                if (htmlVideo.paused) {
                    htmlVideo.muted = false;
                    htmlVideo.volume = 1.0;
                    htmlVideo.play().catch(error => {
                        console.error('❌ Mobile play error:', error);
                    });
                }
                return;
            }

            // Desktop video handling - don't reload unless necessary
            // Only load if video hasn't been loaded yet
            if (htmlVideo.readyState === 0) {
                htmlVideo.load();
            }

            // Ensure video has sound but don't reset position
            htmlVideo.muted = false;
            htmlVideo.volume = 1.0;

            const playPromise = htmlVideo.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('✅ Video resumed from position:', htmlVideo.currentTime);
                }).catch(error => {
                    console.error('❌ Play error:', error);
                    // Try with muted first if autoplay is blocked
                    if (error.name === 'NotAllowedError') {
                        console.log('🔇 Trying muted play...');
                        htmlVideo.muted = true;
                        htmlVideo.play().then(() => {
                            showUnmuteInstruction(htmlVideo);
                        });
                    } else {
                        // Reset to paused state on error
                        videoContainer.classList.remove('video-playing');
                        videoContainer.classList.add('video-paused');
                    }
                });
            }
        }

        // Video click and play handlers
        function handleVideoClick(videoElement) {
            console.log('🎥 Video clicked');

            if (videoElement.paused) {
                // Ensure video has sound
                videoElement.muted = false;
                videoElement.volume = 1.0;

                const playPromise = videoElement.play();
                if (playPromise !== undefined) {
                    playPromise.then(() => {
                        console.log('✅ Video playing with sound');
                    }).catch(error => {
                        console.error('❌ Play error:', error);
                        // If autoplay is blocked, try with muted first
                        if (error.name === 'NotAllowedError') {
                            console.log('🔇 Trying muted play first...');
                            videoElement.muted = true;
                            videoElement.play().then(() => {
                                console.log('✅ Video playing muted, will unmute after user interaction');
                                // Show unmute button or instruction
                                showUnmuteInstruction(videoElement);
                            });
                        }
                    });
                }
            } else {
                videoElement.pause();
                console.log('⏸️ Video paused');
            }
        }

        function handleVideoPlay(videoElement) {
            console.log('🎵 Video started playing');

            // Ensure video is not muted when playing
            if (videoElement.muted) {
                // Try to unmute after a short delay
                setTimeout(() => {
                    videoElement.muted = false;
                    videoElement.volume = 1.0;
                    console.log('🔊 Video unmuted');
                }, 500);
            }
        }

        function showUnmuteInstruction(videoElement) {
            // Create unmute button overlay
            const container = videoElement.closest('.video-container');
            if (!container) return;

            const unmuteBtn = document.createElement('div');
            unmuteBtn.className = 'absolute top-4 right-4 bg-black bg-opacity-70 text-white px-3 py-2 rounded-lg cursor-pointer z-20';
            unmuteBtn.innerHTML = '<i class="fas fa-volume-mute mr-2"></i>Tap untuk suara';
            unmuteBtn.onclick = function() {
                videoElement.muted = false;
                videoElement.volume = 1.0;
                this.remove();
                console.log('🔊 Video unmuted by user');
            };

            container.appendChild(unmuteBtn);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (unmuteBtn.parentNode) {
                    unmuteBtn.remove();
                }
            }, 5000);
        }

        // Mobile video interaction handlers
        function handleMobileVideoClick(videoElement) {
            console.log('📱 Mobile video clicked');

            const container = videoElement.closest('.video-container');
            if (!container) {
                console.error('❌ Video container not found');
                return;
            }

            // Force play/pause on mobile
            if (videoElement.paused) {
                console.log('📱 Attempting to play mobile video from position:', videoElement.currentTime);

                // Ensure video is ready to play
                if (videoElement.readyState < 2) {
                    console.log('📱 Video not ready, loading...');
                    videoElement.load();
                }

                // Set volume and unmute
                videoElement.muted = false;
                videoElement.volume = 1.0;

                // Try to play
                const playPromise = videoElement.play();
                if (playPromise !== undefined) {
                    playPromise.then(() => {
                        console.log('✅ Mobile video playing successfully');
                        // Update UI state
                        container.classList.remove('video-paused');
                        container.classList.add('video-playing');

                        // Update play button icon
                        const playButtonIcon = container.querySelector('.play-button i');
                        if (playButtonIcon) {
                            playButtonIcon.className = 'fas fa-pause';
                        }
                    }).catch(error => {
                        console.error('❌ Mobile video play failed:', error);

                        // If autoplay is blocked, try with muted first
                        if (error.name === 'NotAllowedError') {
                            console.log('🔇 Trying muted play for mobile...');
                            videoElement.muted = true;
                            videoElement.play().then(() => {
                                console.log('✅ Mobile video playing muted');
                                container.classList.remove('video-paused');
                                container.classList.add('video-playing');

                                // Show unmute instruction
                                showUnmuteInstruction(videoElement);

                                // Try to unmute after a delay
                                setTimeout(() => {
                                    videoElement.muted = false;
                                    videoElement.volume = 1.0;
                                    console.log('🔊 Mobile video unmuted');
                                }, 1000);
                            }).catch(err => {
                                console.error('❌ Even muted play failed:', err);
                                // Reset to paused state
                                container.classList.remove('video-playing');
                                container.classList.add('video-paused');
                            });
                        } else {
                            // Reset to paused state on other errors
                            container.classList.remove('video-playing');
                            container.classList.add('video-paused');
                        }
                    });
                }

            } else {
                console.log('📱 Pausing mobile video');
                videoElement.pause();

                // Update UI state
                container.classList.remove('video-playing');
                container.classList.add('video-paused');

                // Update play button icon
                const playButtonIcon = container.querySelector('.play-button i');
                if (playButtonIcon) {
                    playButtonIcon.className = 'fas fa-play';
                }
            }
        }

        function handleMobileVideoTouch(videoElement) {
            console.log('📱 Mobile video touched');
            // Add visual feedback
            videoElement.style.opacity = '0.8';
            setTimeout(() => {
                videoElement.style.opacity = '1';
            }, 150);
        }

        // Like functionality
        async function toggleLike(videoId) {
            console.log('👍 Toggle like for video:', videoId);

            const likeBtn = document.querySelector(`.like-btn[data-video-id="${videoId}"]`);
            if (!likeBtn) return;

            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();

                if (data.success) {
                    // Toggle visual state
                    const isLiked = likeBtn.classList.contains('liked');
                    likeBtn.classList.toggle('liked');

                    // Debug mobile color
                    const isMobile = window.innerWidth < 768;
                    if (isMobile) {
                        console.log('📱 Mobile like button toggled:', !isLiked ? 'BLUE' : 'DEFAULT');
                    }

                    // Update badge with new count
                    if (data.likes !== undefined) {
                        updateLikeBadge(videoId, data.likes);
                    }

                    console.log('✅ Like toggled successfully, new count:', data.likes);
                } else {
                    console.error('❌ Like toggle failed:', data.message);
                }
            } catch (error) {
                console.error('❌ Like error:', error);
            }
        }

        // Comment functionality
        async function toggleComment(videoId) {
            console.log('💬 Opening comment modal for video:', videoId);

            // Open comment modal
            openCommentModal(videoId);
        }

        // Comment Modal Functions
        function openCommentModal(videoId) {
            const overlay = document.getElementById('commentOverlay');
            const modal = document.getElementById('commentModal');

            overlay.classList.add('show');
            modal.classList.add('show');

            // Load comments
            loadComments(videoId);

            // Store current video ID for comment submission
            window.currentVideoId = videoId;
        }

        function closeCommentModal() {
            const overlay = document.getElementById('commentOverlay');
            const modal = document.getElementById('commentModal');

            overlay.classList.remove('show');
            modal.classList.remove('show');

            // Clear input
            document.getElementById('commentInput').value = '';
            updateCommentCounter();
        }

        async function loadComments(videoId) {
            const commentContent = document.getElementById('commentContent');

            try {
                const response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_comments&video_id=${videoId}`);
                const data = await response.json();

                if (data.success && data.comments) {
                    displayComments(data.comments);
                } else {
                    commentContent.innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-comment text-2xl mb-2"></i>
                            <p>Belum ada komentar</p>
                            <p class="text-sm">Jadilah yang pertama berkomentar!</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading comments:', error);
                commentContent.innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Gagal memuat komentar</p>
                    </div>
                `;
            }
        }

        function displayComments(comments) {
            const commentContent = document.getElementById('commentContent');

            if (comments.length === 0) {
                commentContent.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-comment text-2xl mb-2"></i>
                        <p>Belum ada komentar</p>
                        <p class="text-sm">Jadilah yang pertama berkomentar!</p>
                    </div>
                `;
                return;
            }

            const commentsHtml = comments.map(comment => `
                <div class="comment-item">
                    <div class="comment-author">${comment.user_name || 'Unknown'}</div>
                    <div class="comment-text">${comment.comment_text}</div>
                    <div class="comment-meta">
                        <span><i class="fas fa-clock mr-1"></i>${formatDate(comment.created_at)}</span>
                        <span><i class="fas fa-thumbs-up mr-1"></i>${comment.likes || 0}</span>
                    </div>
                </div>
            `).join('');

            commentContent.innerHTML = commentsHtml;
        }

        // Check if user is logged in
        function checkUserLogin() {
            try {
                const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

                // Check if user has valid ID and name
                const hasValidId = userInfo.id && !isNaN(parseInt(userInfo.id));
                const hasValidName = userInfo.name && userInfo.name.trim() !== '';
                const isLoggedIn = hasValidId && hasValidName;

                console.log('🔍 Login check:', {
                    userInfo: userInfo,
                    hasValidId: hasValidId,
                    hasValidName: hasValidName,
                    isLoggedIn: isLoggedIn
                });

                return {
                    isLoggedIn: isLoggedIn,
                    userId: hasValidId ? parseInt(userInfo.id) : null,
                    userName: hasValidName ? userInfo.name.trim() : 'Unknown',
                    userEmail: userInfo.email || null
                };
            } catch (error) {
                console.error('❌ Error checking login status:', error);
                return {
                    isLoggedIn: false,
                    userId: null,
                    userName: 'Unknown',
                    userEmail: null
                };
            }
        }

        // Function to increment video view count
        function incrementVideoView(videoId) {
            console.log('👁️ Incrementing view for video:', videoId);

            fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_view', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `video_id=${videoId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ View count updated:', data.views);

                    // Update view count in UI if element exists
                    const viewCountElement = document.querySelector('.view-count');
                    if (viewCountElement && data.views) {
                        viewCountElement.textContent = formatViewCount(data.views);
                    }
                } else {
                    console.log('ℹ️ View count:', data.message);
                }
            })
            .catch(error => {
                console.error('❌ Error incrementing view:', error);
            });
        }

        // Format view count for display
        function formatViewCount(count) {
            if (count >= 1000000) {
                return (count / 1000000).toFixed(1) + 'M views';
            } else if (count >= 1000) {
                return (count / 1000).toFixed(1) + 'K views';
            } else {
                return count + ' views';
            }
        }

        async function submitComment() {
            const input = document.getElementById('commentInput');
            const submitBtn = document.getElementById('commentSubmit');
            const commentText = input.value.trim();

            if (!commentText) return;

            // Check login status
            const loginStatus = checkUserLogin();

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Mengirim...';

            try {
                // Prepare user data
                let userName = 'Unknown';
                let userId = null;

                if (loginStatus.isLoggedIn) {
                    // User is logged in - get name from database
                    userName = loginStatus.userName;
                    userId = loginStatus.userId;
                    console.log('📝 Comment from logged in user:', userName, 'ID:', userId);
                } else {
                    // User not logged in - use "Unknown"
                    userName = 'Unknown';
                    userId = null;
                    console.log('📝 Comment from guest user');
                }

                // Prepare form data with proper null handling
                let formData = `video_id=${window.currentVideoId}&comment_text=${encodeURIComponent(commentText)}&user_name=${encodeURIComponent(userName)}`;

                // Only add user_id if it's not null
                if (userId !== null && userId !== undefined) {
                    formData += `&user_id=${userId}`;
                }

                console.log('📤 Sending comment data:', formData);

                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=add_video_comment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    input.value = '';
                    updateCommentCounter();
                    loadComments(window.currentVideoId); // Reload comments

                    // Update comment badge
                    updateCommentBadge(window.currentVideoId);
                } else {
                    alert('Gagal mengirim komentar: ' + (data.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error submitting comment:', error);
                alert('Gagal mengirim komentar');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Kirim';
            }
        }

        function updateCommentCounter() {
            const input = document.getElementById('commentInput');
            const counter = document.getElementById('commentCounter');
            const submitBtn = document.getElementById('commentSubmit');

            const length = input.value.length;
            counter.textContent = `${length}/500`;

            submitBtn.disabled = length === 0 || length > 500;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diff = now - date;

            const minutes = Math.floor(diff / 60000);
            const hours = Math.floor(diff / 3600000);
            const days = Math.floor(diff / 86400000);

            if (minutes < 1) return 'Baru saja';
            if (minutes < 60) return `${minutes} menit lalu`;
            if (hours < 24) return `${hours} jam lalu`;
            if (days < 7) return `${days} hari lalu`;

            return date.toLocaleDateString('id-ID');
        }

        // Update badge functions
        function updateLikeBadge(videoId, newCount) {
            const likeBtns = document.querySelectorAll(`.like-btn[data-video-id="${videoId}"] .badge`);
            likeBtns.forEach(badge => {
                badge.textContent = newCount;
                badge.setAttribute('data-count', newCount);
            });
        }

        function updateCommentBadge(videoId) {
            // Get updated comment count from server
            fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_comments&video_id=${videoId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const commentCount = data.count || 0;
                        const commentBtns = document.querySelectorAll(`.comment-btn[data-video-id="${videoId}"] .badge`);
                        commentBtns.forEach(badge => {
                            badge.textContent = commentCount;
                            badge.setAttribute('data-count', commentCount);
                        });
                    }
                })
                .catch(error => console.error('Error updating comment badge:', error));
        }

        function updateSaveBadge(videoId, newCount) {
            const saveBtns = document.querySelectorAll(`.save-btn[data-video-id="${videoId}"] .badge`);
            saveBtns.forEach(badge => {
                badge.textContent = newCount;
                badge.setAttribute('data-count', newCount);
            });
        }

        function updateShareBadge(videoId, newCount) {
            const shareBtns = document.querySelectorAll(`.share-btn[data-video-id="${videoId}"] .badge`);
            shareBtns.forEach(badge => {
                badge.textContent = newCount;
                badge.setAttribute('data-count', newCount);
            });
        }

        // Video progress and seek functions
        function updateProgress(videoId) {
            const isMobile = window.innerWidth < 768;
            const video = document.querySelector(`#${isMobile ? 'mobile-' : 'desktop-'}video-${videoId}`);
            const progressBar = document.querySelector(`#progress${isMobile ? '-mobile' : ''}-${videoId}`);

            if (video && progressBar && video.duration) {
                const progress = (video.currentTime / video.duration) * 100;
                progressBar.style.width = progress + '%';
            }
        }

        // Update progress for all videos
        function updateAllProgress() {
            videosData.forEach(videoData => {
                updateProgress(videoData.id);
            });
        }

        function seekVideo(event, videoId) {
            const isMobile = window.innerWidth < 768;
            const video = document.querySelector(`#${isMobile ? 'mobile-' : 'desktop-'}video-${videoId}`);
            const progressContainer = event.currentTarget;

            if (video && video.duration) {
                const rect = progressContainer.getBoundingClientRect();
                const clickX = event.clientX - rect.left;
                const width = rect.width;
                const percentage = clickX / width;
                const newTime = percentage * video.duration;

                video.currentTime = newTime;
                console.log('🎯 Seeked to:', newTime, 'seconds');
            }
        }

        // TikTok-style scroll functions
        let currentVideoIndex = 0;
        const totalVideos = <?= count($videosWithSources) ?>;

        // Video data for JavaScript
        const videosData = <?= json_encode(array_map(function($videoData) {
            return [
                'id' => $videoData['video']['id'],
                'title' => $videoData['video']['title'],
                'views' => $videoData['video']['views'],
                'video_type' => $videoData['source']['type']
            ];
        }, $videosWithSources)) ?>;

        function scrollToVideo(index) {
            const container = document.getElementById('videoScrollContainer');
            const videoItems = container.querySelectorAll('.video-scroll-item');

            if (index >= 0 && index < videoItems.length) {
                // Pause current video
                pauseAllVideos();

                // Scroll to target video
                videoItems[index].scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Update current index
                currentVideoIndex = index;
                updateNavDots();
                updateNavbarTitle();

                console.log('📱 Scrolled to video index:', index);
            }
        }

        function pauseAllVideos() {
            const videos = document.querySelectorAll('video');
            videos.forEach(video => {
                if (!video.paused) {
                    video.pause();
                }
            });
        }

        function updateNavDots() {
            const dots = document.querySelectorAll('.video-nav-dot');
            dots.forEach((dot, index) => {
                if (index === currentVideoIndex) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        function updateNavbarTitle() {
            if (videosData[currentVideoIndex]) {
                const currentVideoData = videosData[currentVideoIndex];
                const titleElement = document.getElementById('navbarTitle');
                const videoCountElement = document.getElementById('navbarVideoCount');

                if (titleElement) {
                    titleElement.textContent = currentVideoData.title;
                }

                if (videoCountElement) {
                    videoCountElement.textContent = `${currentVideoIndex + 1}/${totalVideos} video`;
                }

                console.log('📱 Updated navbar to:', currentVideoData.title);
            }
        }

        function handleScroll() {
            const container = document.getElementById('videoScrollContainer');
            const videoItems = container.querySelectorAll('.video-scroll-item');
            const containerHeight = container.clientHeight;
            const scrollTop = container.scrollTop;

            // Find which video is currently in view
            let newIndex = 0;
            videoItems.forEach((item, index) => {
                const itemTop = item.offsetTop;
                const itemBottom = itemTop + item.offsetHeight;
                const viewportCenter = scrollTop + containerHeight / 2;

                if (viewportCenter >= itemTop && viewportCenter < itemBottom) {
                    newIndex = index;
                }
            });

            // Update if index changed
            if (newIndex !== currentVideoIndex) {
                pauseAllVideos();
                currentVideoIndex = newIndex;
                updateNavDots();
                updateNavbarTitle();
                console.log('📱 Scrolled to video index:', newIndex);
            }
        }

        // Auto-play video when in view (individual video control)
        function autoPlayVideoInView() {
            const container = document.getElementById('videoScrollContainer');
            const videoItems = container.querySelectorAll('.video-scroll-item');
            const containerHeight = container.clientHeight;
            const scrollTop = container.scrollTop;

            videoItems.forEach((item, index) => {
                const itemTop = item.offsetTop;
                const itemBottom = itemTop + item.offsetHeight;
                const isInView = scrollTop >= itemTop - containerHeight * 0.1 &&
                               scrollTop < itemBottom - containerHeight * 0.9;

                const video = item.querySelector('video');
                const videoContainer = item;

                if (video && isInView && index === currentVideoIndex) {
                    // Auto play current video in view only
                    if (video.paused) {
                        video.muted = false;
                        video.volume = 1.0;

                        // Update container state
                        videoContainer.classList.remove('video-paused');
                        videoContainer.classList.add('video-playing');

                        // Update play button icon
                        const playButtonIcon = videoContainer.querySelector('.play-button i');
                        if (playButtonIcon) {
                            playButtonIcon.className = 'fas fa-pause';
                        }

                        video.play().catch(e => {
                            console.log('Auto-play blocked for video', index, ':', e);
                            // Reset state if play fails
                            videoContainer.classList.remove('video-playing');
                            videoContainer.classList.add('video-paused');
                            if (playButtonIcon) {
                                playButtonIcon.className = 'fas fa-play';
                            }
                        });
                    }
                } else if (video && !isInView) {
                    // Pause video out of view
                    if (!video.paused) {
                        video.pause();

                        // Update container state
                        videoContainer.classList.remove('video-playing');
                        videoContainer.classList.add('video-paused');

                        // Update play button icon
                        const playButtonIcon = videoContainer.querySelector('.play-button i');
                        if (playButtonIcon) {
                            playButtonIcon.className = 'fas fa-play';
                        }
                    }
                }
            });
        }

        // Save functionality
        async function toggleSave(videoId) {
            console.log('💾 Toggle save for video:', videoId);

            const saveBtn = document.querySelector(`.save-btn[data-video-id="${videoId}"]`);
            if (!saveBtn) return;

            // Toggle visual state - yellow when saved
            const isSaved = saveBtn.classList.contains('saved');

            const isMobile = window.innerWidth < 768;

            if (isSaved) {
                // Remove saved state
                saveBtn.classList.remove('saved');
                console.log('📤 Video unsaved');

                if (isMobile) {
                    console.log('📱 Mobile save button: DEFAULT color');
                }

                // Update badge (decrease count)
                const currentBadge = saveBtn.querySelector('.badge');
                if (currentBadge) {
                    const currentCount = parseInt(currentBadge.getAttribute('data-count')) || 0;
                    const newCount = Math.max(0, currentCount - 1);
                    updateSaveBadge(videoId, newCount);
                }
            } else {
                // Add saved state
                saveBtn.classList.add('saved');
                console.log('💾 Video saved');

                if (isMobile) {
                    console.log('📱 Mobile save button: YELLOW color');
                }

                // Update badge (increase count)
                const currentBadge = saveBtn.querySelector('.badge');
                if (currentBadge) {
                    const currentCount = parseInt(currentBadge.getAttribute('data-count')) || 0;
                    const newCount = currentCount + 1;
                    updateSaveBadge(videoId, newCount);
                }
            }

            // Here you can add API call to save video
            // Example: await fetch('/api/toggle-save', { method: 'POST', body: JSON.stringify({videoId}) });
        }

        // Share functionality
        async function shareVideo(videoId) {
            console.log('📤 Share video:', videoId);

            // Increment share count in database
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}&platform=direct`
                });

                const data = await response.json();
                if (data.success && data.shares !== undefined) {
                    updateShareBadge(videoId, data.shares);
                }
            } catch (error) {
                console.error('❌ Failed to update share count:', error);
            }

            if (navigator.share) {
                try {
                    await navigator.share({
                        title: '<?= htmlspecialchars($currentVideo['title']) ?>',
                        text: '<?= htmlspecialchars($currentVideo['description']) ?>',
                        url: window.location.href
                    });
                } catch (err) {
                    console.log('Share cancelled');
                }
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Link copied to clipboard!');
                });
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Video player initialized');

            const videoId = <?= $currentVideo['id'] ?>;
            const isMobile = window.innerWidth < 768;

            // Set initial title with proper truncation
            const initialTitle = '<?= addslashes($currentVideo['title']) ?>';
            updateNavbarTitle(initialTitle);

            // Auto increment view count on page load
            incrementVideoView(videoId);

            // Play button event listeners
            const playButtons = document.querySelectorAll('.play-button button');
            playButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Get video ID from the closest video container
                    const videoContainer = button.closest('.video-container');
                    if (!videoContainer) {
                        console.error('❌ Video container not found for play button');
                        return;
                    }

                    const videoId = videoContainer.getAttribute('data-video-id');
                    if (!videoId) {
                        console.error('❌ Video ID not found');
                        return;
                    }

                    console.log('🎬 Play button clicked for video:', videoId);

                    // For mobile, directly handle video click
                    if (isMobile) {
                        const mobileVideo = videoContainer.querySelector('video');
                        if (mobileVideo) {
                            console.log('📱 Handling mobile video click via play button');
                            handleMobileVideoClick(mobileVideo);
                            return;
                        }
                    }

                    // For desktop or YouTube videos
                    toggleVideoPlay(videoId);
                });
            });

            // Video container click (for play/pause) - only if not clicking overlay button
            const videoContainers = document.querySelectorAll('.video-container');
            videoContainers.forEach(container => {
                container.addEventListener('click', function(e) {
                    // Don't trigger if clicking on action buttons or play button overlay
                    if (e.target.closest('.action-btn') ||
                        e.target.closest('.play-button')) {
                        return;
                    }

                    // Only trigger for container background clicks
                    const videoId = container.getAttribute('data-video-id');
                    if (videoId) {
                        // For mobile, handle video click directly
                        if (isMobile) {
                            const mobileVideo = container.querySelector('video');
                            if (mobileVideo) {
                                console.log('📱 Container background click - handling mobile video');
                                handleMobileVideoClick(mobileVideo);
                                return;
                            }
                        }

                        toggleVideoPlay(parseInt(videoId));
                    }
                });
            });

            // Action button event listeners
            const likeButtons = document.querySelectorAll('.like-btn');
            likeButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const videoId = this.getAttribute('data-video-id');
                    toggleLike(videoId);
                });
            });

            const commentButtons = document.querySelectorAll('.comment-btn');
            commentButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const videoId = this.getAttribute('data-video-id');
                    toggleComment(videoId);
                });
            });

            const saveButtons = document.querySelectorAll('.save-btn');
            saveButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const videoId = this.getAttribute('data-video-id');
                    toggleSave(videoId);
                });
            });

            const shareButtons = document.querySelectorAll('.share-btn');
            shareButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const videoId = this.getAttribute('data-video-id');
                    shareVideo(videoId);
                });
            });

            // Comment input event listeners
            const commentInput = document.getElementById('commentInput');
            if (commentInput) {
                commentInput.addEventListener('input', updateCommentCounter);
                commentInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        submitComment();
                    }
                });
            }

            // TikTok-style scroll event listeners (mobile only)
            if (window.innerWidth < 768) {
                const scrollContainer = document.getElementById('videoScrollContainer');
                if (scrollContainer) {
                    let scrollTimeout;

                    scrollContainer.addEventListener('scroll', function() {
                        clearTimeout(scrollTimeout);
                        scrollTimeout = setTimeout(() => {
                            handleScroll();
                            autoPlayVideoInView();
                        }, 100);
                    });

                    // Keyboard navigation
                    document.addEventListener('keydown', function(e) {
                        if (e.key === 'ArrowUp' && currentVideoIndex > 0) {
                            e.preventDefault();
                            scrollToVideo(currentVideoIndex - 1);
                        } else if (e.key === 'ArrowDown' && currentVideoIndex < totalVideos - 1) {
                            e.preventDefault();
                            scrollToVideo(currentVideoIndex + 1);
                        }
                    });

                    // Touch swipe detection
                    let startY = 0;
                    let endY = 0;

                    scrollContainer.addEventListener('touchstart', function(e) {
                        startY = e.touches[0].clientY;
                    });

                    scrollContainer.addEventListener('touchend', function(e) {
                        // Remove swipe detection to avoid conflicts with native scroll
                        // Let browser handle natural scroll behavior
                        console.log('📱 Touch end on scroll container');
                    });

                    // Initial setup
                    setTimeout(() => {
                        autoPlayVideoInView();
                        updateNavbarTitle(); // Set initial navbar title
                    }, 1000);
                }
            }

            // Video event listeners for syncing with overlay button
            const videos = document.querySelectorAll('video');
            videos.forEach(video => {
                const isMobileVideo = video.id.includes('mobile');

                video.addEventListener('loadstart', () => console.log(`📹 Video ${isMobileVideo ? '(mobile)' : '(desktop)'}: loadstart`));
                video.addEventListener('loadeddata', () => console.log(`📹 Video ${isMobileVideo ? '(mobile)' : '(desktop)'}: loadeddata`));
                video.addEventListener('canplay', () => {
                    console.log(`📹 Video ${isMobileVideo ? '(mobile)' : '(desktop)'}: canplay`);
                    video.style.pointerEvents = 'auto';
                });
                video.addEventListener('error', (e) => {
                    console.error(`📹 Video ${isMobileVideo ? '(mobile)' : '(desktop)'} error:`, e);
                    console.error('📹 Video error details:', video.error);
                });

                // Sync overlay button with video play state
                video.addEventListener('play', () => {
                    console.log(`📹 Video ${isMobileVideo ? '(mobile)' : '(desktop)'}: play event`);
                    const container = video.closest('.video-container');
                    if (container) {
                        container.classList.remove('video-paused');
                        container.classList.add('video-playing');

                        // Update button icon to pause
                        const playButtonIcon = container.querySelector('.play-button i');
                        if (playButtonIcon) {
                            playButtonIcon.className = 'fas fa-pause';
                        }
                    }
                });

                video.addEventListener('pause', () => {
                    console.log(`📹 Video ${isMobileVideo ? '(mobile)' : '(desktop)'}: pause event`);
                    const container = video.closest('.video-container');
                    if (container) {
                        container.classList.remove('video-playing');
                        container.classList.add('video-paused');

                        // Update button icon to play
                        const playButtonIcon = container.querySelector('.play-button i');
                        if (playButtonIcon) {
                            playButtonIcon.className = 'fas fa-play';
                        }
                    }
                });

                video.addEventListener('ended', () => {
                    console.log(`📹 Video ${isMobileVideo ? '(mobile)' : '(desktop)'}: ended`);
                    const container = video.closest('.video-container');
                    if (container) {
                        container.classList.remove('video-playing');
                        container.classList.add('video-paused');

                        // Reset button icon to play
                        const playButtonIcon = container.querySelector('.play-button i');
                        if (playButtonIcon) {
                            playButtonIcon.className = 'fas fa-play';
                        }
                    }
                });

                // Update progress bar for each video individually
                video.addEventListener('timeupdate', () => {
                    const videoId = video.id.replace(/^(mobile-|desktop-)video-/, '');
                    updateProgress(parseInt(videoId));
                });

                // Reset progress when video loads
                video.addEventListener('loadedmetadata', () => {
                    const videoId = video.id.replace(/^(mobile-|desktop-)video-/, '');
                    updateProgress(parseInt(videoId));
                });

                // Mobile specific events
                if (isMobileVideo) {
                    video.addEventListener('touchstart', (e) => {
                        console.log('📱 Mobile video touchstart');
                        // Remove stopPropagation to avoid scroll conflicts
                        // Add visual feedback
                        video.style.opacity = '0.8';
                        setTimeout(() => {
                            video.style.opacity = '1';
                        }, 150);
                        // Store touch start position for later use
                        video.dataset.touchStartY = e.touches[0].clientY;
                    });

                    video.addEventListener('click', (e) => {
                        console.log('📱 Mobile video direct click');
                        // Only prevent default, not stopPropagation to avoid scroll conflicts
                        e.preventDefault();

                        // Handle mobile video play/pause directly
                        handleMobileVideoClick(video);
                    });

                    // Add touch event for better mobile interaction
                    video.addEventListener('touchend', (e) => {
                        console.log('📱 Mobile video touchend');
                        // Remove preventDefault and stopPropagation to avoid scroll conflicts
                        // Only handle if it's a tap, not a scroll
                        if (Math.abs(e.changedTouches[0].clientY - (video.dataset.touchStartY || 0)) < 10) {
                            // Handle mobile video play/pause on touch end
                            handleMobileVideoClick(video);
                        }
                    });


                }
            });

            // Initialize scroll detection for title updates
            initScrollDetection();

            // Handle window resize for responsive title truncation
            window.addEventListener('resize', () => {
                const navbarTitle = document.getElementById('navbarTitle');
                if (navbarTitle && navbarTitle.title) {
                    // Re-truncate title based on new screen size
                    updateNavbarTitle(navbarTitle.title);
                }
            });
        });
    </script>
</body>
</html>
