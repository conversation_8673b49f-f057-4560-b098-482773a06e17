-- Safe way to add logo columns to settings table
-- Run each statement one by one, ignore errors if column already exists

-- Add logo columns to settings table
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'settings' 
     AND column_name = 'logo_id' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column logo_id already exists"',
    'ALTER TABLE settings ADD COLUMN logo_id INT NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'settings' 
     AND column_name = 'logo_filename' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column logo_filename already exists"',
    'ALTER TABLE settings ADD COLUMN logo_filename VARCHAR(255) NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'settings' 
     AND column_name = 'logo_original_name' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column logo_original_name already exists"',
    'ALTER TABLE settings ADD COLUMN logo_original_name VARCHAR(255) NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'settings' 
     AND column_name = 'logo_file_path' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column logo_file_path already exists"',
    'ALTER TABLE settings ADD COLUMN logo_file_path VARCHAR(500) NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'settings' 
     AND column_name = 'logo_file_size' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column logo_file_size already exists"',
    'ALTER TABLE settings ADD COLUMN logo_file_size INT NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'settings' 
     AND column_name = 'logo_mime_type' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column logo_mime_type already exists"',
    'ALTER TABLE settings ADD COLUMN logo_mime_type VARCHAR(100) NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'settings' 
     AND column_name = 'logo_uploaded_at' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column logo_uploaded_at already exists"',
    'ALTER TABLE settings ADD COLUMN logo_uploaded_at TIMESTAMP NULL'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
